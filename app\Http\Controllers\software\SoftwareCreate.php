<?php

namespace App\Http\Controllers\software;

use App\Http\Controllers\Controller;
use App\Models\Software;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SoftwareCreate extends Controller
{
    public function index(Request $request)
    {
        $allTags = Tag::all()->toArray();
        $allTags = array_column($allTags,'name');
        return view('content.software.software-create', compact('allTags'));
    }

    public function create(Request $request)
    {
        // dd($request->all());
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'en_name' => 'nullable|string|max:255',
            'description' => 'required|string',
            'video' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_free' => 'sometimes|accepted',
            'price_1_m'  => 'nullable|required_unless:is_free,on|integer|min:0',
            'price_3_m'  => 'nullable|required_unless:is_free,on|integer|min:0',
            'price_6_m'  => 'nullable|required_unless:is_free,on|integer|min:0',
            'price_1_y'  => 'nullable|required_unless:is_free,on|integer|min:0',
            'price_lifetime' => 'nullable|required_unless:is_free,on|integer|min:0',
            'price_code' => 'nullable|required_unless:is_free,on|integer|min:0',
            'tags' => 'nullable|string',
        ]);

        // Xử lý tags - tách ra khỏi data chính
        $tagData = $data['tags'] ?? '';
        unset($data['tags']); // Loại bỏ tags khỏi data để tránh lỗi khi create software

        $tagValues = [];
        $decodedTags = json_decode($tagData, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decodedTags)) {
            $tagValues = array_column($decodedTags, 'value');
        }
        $maxId = Software::max('id') ?? 0;
        $data['url'] = Str::slug($data['name']) . '-' . ($maxId + 1);
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $fileName = $data['url'] . '.' . $file->getClientOriginalExtension();
            $destinationPath = public_path('tool_image');
            $file->move($destinationPath, $fileName);
            $data['image'] = '/tool_image/' . $fileName;
        }
        $data['is_free'] = $request->has('is_free');
        $software = Software::create($data);

        // Xử lý tags sau khi tạo software
        if (!empty($tagValues)) {
            foreach ($tagValues as $tagName) {
                // Tìm hoặc tạo tag
                $tag = Tag::firstOrCreate(['name' => $tagName]);

                // Attach tag vào software (sử dụng sync để tránh duplicate)
                $software->tags()->syncWithoutDetaching([$tag->id]);
            }
        }

        return redirect()->route('software-list')->with('success', 'Software created successfully.');
    }
}
