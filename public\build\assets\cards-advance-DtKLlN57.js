(function(){let i=$(".select2");i.length&&i.each(function(){var r=$(this);r.wrap('<div class="position-relative"></div>').select2({placeholder:"Select tags",dropdownParent:r.parent()})});let e,a;isDarkStyle?(config.colors_dark.cardColor,a=config.colors_dark.textMuted,config.colors_dark.bodyColor,e=config.colors_dark.headingColor):(config.colors.cardColor,a=config.colors.textMuted,config.colors.bodyColor,e=config.colors.headingColor);function d(r,l,o){return{chart:{height:o=="true"?58:48,width:o=="true"?58:38,type:"radialBar"},plotOptions:{radialBar:{hollow:{size:o=="true"?"50%":"25%"},dataLabels:{show:o=="true",value:{offsetY:-10,fontSize:"15px",fontWeight:500,fontFamily:"Public Sans",color:e}},track:{background:config.colors_label.secondary}}},stroke:{lineCap:"round"},colors:[r],grid:{padding:{top:o=="true"?-12:-15,bottom:o=="true"?-17:-15,left:o=="true"?-17:-5,right:-15}},series:[l],labels:o=="true"?[""]:["Progress"]}}const s=document.querySelectorAll(".chart-progress");s&&s.forEach(function(r){const l=config.colors[r.dataset.color],o=r.dataset.series,c=r.dataset.progress_variant?r.dataset.progress_variant:"false",f=d(l,o,c);new ApexCharts(r,f).render()});const t=document.querySelector("#reportBarChart"),p={chart:{height:200,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{barHeight:"60%",columnWidth:"60%",startingShape:"rounded",endingShape:"rounded",borderRadius:4,distributed:!0}},grid:{show:!1,padding:{top:-20,bottom:0,left:-10,right:-10}},colors:[config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors_label.primary,config.colors.primary,config.colors_label.primary,config.colors_label.primary],dataLabels:{enabled:!1},series:[{data:[40,95,60,45,90,50,75]}],legend:{show:!1},xaxis:{categories:["Mo","Tu","We","Th","Fr","Sa","Su"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:a,fontSize:"13px"}}},yaxis:{labels:{show:!1}}};typeof t!==void 0&&t!==null&&new ApexCharts(t,p).render();const n=document.querySelector("#swiper-with-pagination-cards");n&&new Swiper(n,{loop:!0,autoplay:{delay:2500,disableOnInteraction:!1},pagination:{clickable:!0,el:".swiper-pagination"}})})();
