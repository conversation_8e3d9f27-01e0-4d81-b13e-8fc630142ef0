<?php

namespace App\Http\Controllers\simrental;

use App\Http\Controllers\Controller;
use App\Models\SmsServer;
use App\Models\SmsCountry;
use App\Models\SmsService;
use App\Models\SmsSimRental;
use Illuminate\Http\Request;

class SimRentalController extends Controller
{
    /**
     * Display the sim rental index page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Page configuration
        $pageConfigs = [
            'pageHeader' => false,
            'contentLayout' => 'wide',
            'footerFixed' => false
        ];

        // Get all servers with their countries and services
        $servers = SmsServer::active()->ordered()->with(['countries.services'])->get();

        // Get all countries and services for dropdowns
        $countries = SmsCountry::active()->ordered()->get();
        $services = SmsService::active()->ordered()->get();

        return view('content.sim-rental.sim-rental-index', compact('pageConfigs', 'servers', 'countries', 'services'));
    }

    /**
     * Get servers data via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServers(Request $request)
    {
        $servers = SmsServer::active()->ordered()->withCount(['countries', 'simRentals'])->get();

        $serversData = $servers->map(function ($server) {
            return [
                'id' => $server->id,
                'name' => $server->name,
                'description' => $server->description,
                'status' => $server->is_active ? 'active' : 'inactive',
                'countries_count' => $server->countries_count,
                'services_count' => $server->countries->sum(function ($country) {
                    return $country->services()->count();
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $serversData
        ]);
    }

    /**
     * Get countries for a specific server
     *
     * @param Request $request
     * @param string $serverId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCountries(Request $request, $serverId)
    {
        $countries = SmsCountry::where('server_id', $serverId)
            ->active()
            ->ordered()
            ->with(['services' => function ($query) {
                $query->active()->ordered();
            }])
            ->get();

        $countriesData = $countries->map(function ($country) {
            // Get translated country name
            $translatedName = $this->getTranslatedCountryName($country->code);

            return [
                'id' => $country->id,
                'name' => $translatedName ?: $country->name,
                'code' => $country->code,
                'available' => $country->services->where('stock', '>', 0)->count() > 0,
                'price_from' => $country->services->min('price') ?? 0,
                'services_count' => $country->services->count()
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $countriesData
        ]);
    }

    /**
     * Get services for a specific server and country
     *
     * @param Request $request
     * @param string $serverId
     * @param string $countryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServices(Request $request, $serverId, $countryId = null)
    {
        $query = SmsService::active()->ordered();

        if ($countryId) {
            $query->where('country_id', $countryId);
        } else {
            // Get services for all countries in this server
            $query->whereHas('country', function ($q) use ($serverId) {
                $q->where('server_id', $serverId);
            });
        }

        $services = $query->with('country')->get();

        $servicesData = $services->map(function ($service) {
            return [
                'id' => $service->id,
                'name' => $service->name,
                'code' => $service->code,
                'price' => $service->price,
                'stock' => $service->stock,
                'country_name' => $service->country->name,
                'available' => $service->stock > 0,
                'description' => "Xác thực tài khoản {$service->name}",
                'success_rate' => rand(90, 99), // Mock success rate
                'avg_time' => rand(1, 5) // Mock average time
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $servicesData
        ]);

    }

    /**
     * Get sim cards based on filters
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSimCards(Request $request)
    {
        $serverId = $request->get('server_id');
        $countryId = $request->get('country_id');
        $serviceId = $request->get('service_id');

        // Get sim rentals based on filters
        $query = SmsSimRental::with(['server', 'country', 'service', 'user'])
            ->orderBy('created_at', 'desc');

        if ($serverId) {
            $query->where('server_id', $serverId);
        }
        if ($countryId) {
            $query->where('country_id', $countryId);
        }
        if ($serviceId) {
            $query->where('service_id', $serviceId);
        }

        $simCards = $query->paginate(20);

        $simCardsData = $simCards->map(function ($simCard) {
            return [
                'id' => $simCard->id,
                'sim_id' => $simCard->sim_id,
                'phone_number' => $simCard->phone_number,
                'server_name' => $simCard->server->name,
                'country_name' => $simCard->country->name,
                'service_name' => $simCard->service->name,
                'price' => $simCard->price,
                'status' => $simCard->status,
                'status_text' => $simCard->status_text,
                'status_badge_class' => $simCard->status_badge_class,
                'otp_code' => $simCard->otp_code,
                'created_at' => $simCard->created_at->format('d/m/Y H:i'),
                'expires_at' => $simCard->expires_at ? $simCard->expires_at->format('d/m/Y H:i') : null,
                'user_name' => $simCard->user ? $simCard->user->username : 'Guest'
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $simCardsData,
            'pagination' => [
                'current_page' => $simCards->currentPage(),
                'last_page' => $simCards->lastPage(),
                'per_page' => $simCards->perPage(),
                'total' => $simCards->total()
            ],
            'filters' => [
                'server_id' => $serverId,
                'country_id' => $countryId,
                'service_id' => $serviceId
            ]
        ]);
    }

    /**
     * Rent a sim card
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function rentSim(Request $request)
    {
        $request->validate([
            'server_id' => 'required|integer',
            'country_id' => 'required|integer',
            'service_id' => 'required|integer'
        ]);

        // Check if service exists and has stock
        $service = SmsService::with(['country.server'])
            ->where('id', $request->service_id)
            ->where('country_id', $request->country_id)
            ->whereHas('country', function ($q) use ($request) {
                $q->where('server_id', $request->server_id);
            })
            ->first();

        if (!$service) {
            return response()->json([
                'success' => false,
                'message' => 'Dịch vụ không tồn tại!'
            ], 404);
        }

        if ($service->stock <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'Dịch vụ này hiện đã hết hàng!'
            ], 400);
        }

        // Create sim rental
        $simRental = SmsSimRental::create([
            'sim_id' => 'SIM' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'phone_number' => '+84' . rand(100000000, 999999999),
            'server_id' => $request->server_id,
            'country_id' => $request->country_id,
            'service_id' => $request->service_id,
            'price' => $service->price,
            'status' => 'active',
            'expires_at' => now()->addMinutes(10),
            'user_id' => auth()->id()
        ]);

        // Decrease stock
        $service->decreaseStock();

        return response()->json([
            'success' => true,
            'message' => 'Sim đã được thuê thành công!',
            'data' => [
                'sim_id' => $simRental->sim_id,
                'phone_number' => $simRental->phone_number,
                'expires_at' => $simRental->expires_at->toISOString(),
                'status' => $simRental->status,
                'price' => $simRental->price,
                'service_name' => $service->name,
                'country_name' => $service->country->name
            ]
        ]);
    }
}
