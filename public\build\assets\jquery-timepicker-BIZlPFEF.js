import{r as Y}from"./jquery-Czc5UB_B.js";import"./_commonjsHelpers-BosuxZz1.js";var T={exports:{}};/*!
 * jquery-timepicker v1.14.1 - A jQuery timepicker plugin inspired by Google Calendar. It supports both mouse and keyboard navigation.
 * Copyright (c) 2021 Jon <PERSON> - https://www.jonthornton.com/jquery-timepicker/
 * License: MIT
 */T.exports;(function(E,O){(function(){function d(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(s){return typeof s}:d=function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},d(i)}function j(i,s){if(!(i instanceof s))throw new TypeError("Cannot call a class as a function")}function x(i,s){for(var e=0;e<s.length;e++){var r=s[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(i,r.key,r)}}function M(i,s,e){return s&&x(i.prototype,s),e&&x(i,e),i}function N(i,s,e){return s in i?Object.defineProperty(i,s,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[s]=e,i}function D(i,s){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);s&&(r=r.filter(function(u){return Object.getOwnPropertyDescriptor(i,u).enumerable})),e.push.apply(e,r)}return e}function _(i){for(var s=1;s<arguments.length;s++){var e=arguments[s]!=null?arguments[s]:{};s%2?D(Object(e),!0).forEach(function(r){N(i,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):D(Object(e)).forEach(function(r){Object.defineProperty(i,r,Object.getOwnPropertyDescriptor(e,r))})}return i}function I(i,s){if(i){if(typeof i=="string")return F(i,s);var e=Object.prototype.toString.call(i).slice(8,-1);if(e==="Object"&&i.constructor&&(e=i.constructor.name),e==="Map"||e==="Set")return Array.from(i);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return F(i,s)}}function F(i,s){(s==null||s>i.length)&&(s=i.length);for(var e=0,r=new Array(s);e<s;e++)r[e]=i[e];return r}function y(i,s){var e;if(typeof Symbol>"u"||i[Symbol.iterator]==null){if(Array.isArray(i)||(e=I(i))||s){e&&(i=e);var r=0,u=function(){};return{s:u,n:function(){return r>=i.length?{done:!0}:{done:!1,value:i[r++]}},e:function(l){throw l},f:u}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var a=!0,n=!1,t;return{s:function(){e=i[Symbol.iterator]()},n:function(){var l=e.next();return a=l.done,l},e:function(l){n=!0,t=l},f:function(){try{!a&&e.return!=null&&e.return()}finally{if(n)throw t}}}}var m=86400,g={bubbles:!0,cancelable:!1,detail:null},L=function(s,e){if(s===null)return null;for(var r=0,u=0;u<s;)r++,u+=e.step(r)*60;var a=u-e.step(r-1)*60;return s-a<u-s?S(a,e):S(u,e)};function S(i,s){return i==m&&s.show2400?i:i%m}var C={appendTo:"body",className:null,closeOnWindow:!1,closeOnScroll:!1,disableTextInput:!1,disableTimeRanges:[],disableTouchKeyboard:!1,durationTime:null,forceRoundTime:!1,lang:{},listWidth:null,maxTime:null,minTime:null,noneOption:!1,orientation:"l",roundingFunction:L,scrollDefault:null,selectOnBlur:!1,show2400:!1,showDuration:!1,showOn:["click","focus"],step:30,stopScrollPropagation:!1,timeFormat:"g:ia",typeaheadHighlight:!0,useSelect:!1,wrapHours:!0},P={am:"am",pm:"pm",AM:"AM",PM:"PM",decimal:".",mins:"mins",hr:"hr",hrs:"hrs"},k=function(){function i(s){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};j(this,i),this._handleFormatValue=this._handleFormatValue.bind(this),this._handleKeyUp=this._handleKeyUp.bind(this),this.targetEl=s;var r=i.extractAttrOptions(s,Object.keys(C));this.settings=this.parseSettings(_(_(_({},C),e),r))}return M(i,[{key:"hideMe",value:function(){if(this.settings.useSelect){this.targetEl.blur();return}if(!(!this.list||!i.isVisible(this.list))){this.settings.selectOnBlur&&this._selectValue(),this.list.hide();var e=new CustomEvent("hideTimepicker",g);this.targetEl.dispatchEvent(e)}}},{key:"_findRow",value:function(u){if(!u&&u!==0)return!1;var r=!1,u=this.settings.roundingFunction(u,this.settings);return this.list?(this.list.find("li").each(function(a,n){var t=parseInt(n.dataset.time);if(!isNaN(t)&&t==u)return r=n,!1}),r):!1}},{key:"_hideKeyboard",value:function(){return(window.navigator.msMaxTouchPoints||"ontouchstart"in document)&&this.settings.disableTouchKeyboard}},{key:"_setTimeValue",value:function(e,r){if(this.targetEl.nodeName==="INPUT"){(e!==null||this.targetEl.value!="")&&(this.targetEl.value=e);var u=this,a=u.settings;a.useSelect&&r!="select"&&u.list&&u.list.val(u._roundAndFormatTime(u.anytime2int(e)))}var n=new CustomEvent("selectTime",g);if(this.selectedValue!=e){this.selectedValue=e;var t=new CustomEvent("changeTime",g),l=new CustomEvent("change",Object.assign(g,{detail:"timepicker"}));return r=="select"?(this.targetEl.dispatchEvent(n),this.targetEl.dispatchEvent(t),this.targetEl.dispatchEvent(l)):["error","initial"].indexOf(r)==-1&&this.targetEl.dispatchEvent(t),!0}else return["error","initial"].indexOf(r)==-1&&this.targetEl.dispatchEvent(n),!1}},{key:"_getTimeValue",value:function(){return this.targetEl.nodeName==="INPUT"?this.targetEl.value:this.selectedValue}},{key:"_selectValue",value:function(){var e=this;e.settings;var r=e.list,u=r.find(".ui-timepicker-selected");if(u.hasClass("ui-timepicker-disabled"))return!1;if(!u.length)return!0;var a=u.get(0).dataset.time;if(a){var n=parseInt(a);isNaN(n)||(a=n)}return a!==null&&(typeof a!="string"&&(a=e._int2time(a)),e._setTimeValue(a,"select")),!0}},{key:"anytime2int",value:function(e){return typeof e=="number"?e:typeof e=="string"?this.time2int(e):d(e)==="object"&&e instanceof Date?e.getHours()*3600+e.getMinutes()*60+e.getSeconds():typeof e=="function"?e():null}},{key:"time2int",value:function(e){if(e===""||e===null||e===void 0)return null;if(e==="now")return this.anytime2int(new Date);if(typeof e!="string")return e;e=e.toLowerCase().replace(/[\s\.]/g,""),this.settings.lang.am==="am"&&(e.slice(-1)=="a"||e.slice(-1)=="p")&&(e+="m");var r=/^(([^0-9]*))?([0-9]?[0-9])(([0-5][0-9]))?(([0-5][0-9]))?(([^0-9]*))$/,u=e.match(/\W/);u&&(r=/^(([^0-9]*))?([0-9]?[0-9])(\W+([0-5][0-9]?))?(\W+([0-5][0-9]))?(([^0-9]*))$/);var a=e.match(r);if(!a)return null;var n=parseInt(a[3]*1,10),t=a[2]||a[9],l=this.parseMinuteString(a[5]),o=a[7]*1||0;!t&&a[3].length==2&&a[3][0]=="0"&&(t="am"),n>24&&!l&&(n=a[3][0]*1,l=this.parseMinuteString(a[3][1]));var c=n;if(n<=12&&t){t=t.trim();var f=t==this.settings.lang.pm||t==this.settings.lang.PM;n==12?c=f?12:0:c=n+(f?12:0)}else{var p=n*3600+l*60+o;if(p>=m+(this.settings.show2400?1:0)){if(this.settings.wrapHours===!1)return null;c=n%24}}var h=c*3600+l*60+o;if(n<12&&!t&&this.settings._twelveHourTime&&this.settings.scrollDefault()){var v=h-this.settings.scrollDefault();v<0&&v>=m/-2&&(h=(h+m/2)%m)}return h}},{key:"parseMinuteString",value:function(e){e||(e=0);var r=1;return e.length==1&&(r=10),parseInt(e)*r||0}},{key:"intStringDateOrFunc2func",value:function(e){var r=this;return e==null?function(){return null}:typeof e=="function"?function(){return r.anytime2int(e())}:function(){return r.anytime2int(e)}}},{key:"parseSettings",value:function(e){if(e.lang=_(_({},P),e.lang),this.settings=e,e.listWidth&&(e.listWidth=this.anytime2int(e.listWidth)),e.minTime=this.intStringDateOrFunc2func(e.minTime),e.maxTime=this.intStringDateOrFunc2func(e.maxTime),e.durationTime=this.intStringDateOrFunc2func(e.durationTime),e.scrollDefault?e.scrollDefault=this.intStringDateOrFunc2func(e.scrollDefault):e.scrollDefault=e.minTime,typeof e.timeFormat=="string"&&e.timeFormat.match(/[gh]/)&&(e._twelveHourTime=!0),typeof e.step!="function"){var r=e.step;e.step=function(){return r}}return e.disableTimeRanges=this._parseDisableTimeRanges(e.disableTimeRanges),e.closeOnWindowScroll&&!e.closeOnScroll&&(e.closeOnScroll=e.closeOnWindowScroll),e.closeOnScroll===!0&&(e.closeOnScroll=window.document),e}},{key:"_parseDisableTimeRanges",value:function(e){if(!e||e.length==0)return[];for(var r in e)e[r]=[this.anytime2int(e[r][0]),this.anytime2int(e[r][1])];e=e.sort(function(u,a){return u[0]-a[0]});for(var r=e.length-1;r>0;r--)e[r][0]<=e[r-1][1]&&(e[r-1]=[Math.min(e[r][0],e[r-1][0]),Math.max(e[r][1],e[r-1][1])],e.splice(r,1));return e}},{key:"_disableTextInputHandler",value:function(e){switch(e.keyCode){case 13:case 9:return;default:e.preventDefault()}}},{key:"_int2duration",value:function(e,r){e=Math.abs(e);var u=Math.round(e/60),a=[],n,t;return u<60?a=[u,this.settings.lang.mins]:(n=Math.floor(u/60),t=u%60,r==30&&t==30&&(n+=this.settings.lang.decimal+5),a.push(n),a.push(n==1?this.settings.lang.hr:this.settings.lang.hrs),r!=30&&t&&(a.push(t),a.push(this.settings.lang.mins))),a.join(" ")}},{key:"_roundAndFormatTime",value:function(e){if(e=this.settings.roundingFunction(e,this.settings),e!==null)return this._int2time(e)}},{key:"_int2time",value:function(e){if(typeof e!="number")return null;var r=parseInt(e%60),u=parseInt(e/60%60),a=parseInt(e/(60*60)%24),n=new Date(1970,0,2,a,u,r,0);if(isNaN(n.getTime()))return null;if(typeof this.settings.timeFormat=="function")return this.settings.timeFormat(n);for(var t="",l,o,c=0;c<this.settings.timeFormat.length;c++)switch(o=this.settings.timeFormat.charAt(c),o){case"a":t+=n.getHours()>11?this.settings.lang.pm:this.settings.lang.am;break;case"A":t+=n.getHours()>11?this.settings.lang.PM:this.settings.lang.AM;break;case"g":l=n.getHours()%12,t+=l===0?"12":l;break;case"G":l=n.getHours(),e===m&&(l=this.settings.show2400?24:0),t+=l;break;case"h":l=n.getHours()%12,l!==0&&l<10&&(l="0"+l),t+=l===0?"12":l;break;case"H":l=n.getHours(),e===m&&(l=this.settings.show2400?24:0),t+=l>9?l:"0"+l;break;case"i":var u=n.getMinutes();t+=u>9?u:"0"+u;break;case"s":r=n.getSeconds(),t+=r>9?r:"0"+r;break;case"\\":c++,t+=this.settings.timeFormat.charAt(c);break;default:t+=o}return t}},{key:"_setSelected",value:function(){var e=this.list;e.find("li").removeClass("ui-timepicker-selected");var r=this.anytime2int(this._getTimeValue());if(r!==null){var u=this._findRow(r);if(u){var a=u.getBoundingClientRect(),n=e.get(0).getBoundingClientRect(),t=a.top-n.top;if(t+a.height>n.height||t<0){var l=e.scrollTop()+(a.top-n.top)-a.height;e.scrollTop(l)}var o=parseInt(u.dataset.time);(this.settings.forceRoundTime||o===r)&&u.classList.add("ui-timepicker-selected")}}}},{key:"_isFocused",value:function(e){return e===document.activeElement}},{key:"_handleFormatValue",value:function(e){e&&e.detail=="timepicker"||this._formatValue(e)}},{key:"_formatValue",value:function(e,r){if(this.targetEl.value===""){this._setTimeValue(null,r);return}if(!(this._isFocused(this.targetEl)&&(!e||e.type!="change"))){var u=this.settings,a=this.anytime2int(this.targetEl.value);if(a===null){var n=new CustomEvent("timeFormatError",g);this.targetEl.dispatchEvent(n);return}var t=this._isTimeRangeError(a,u);if(u.forceRoundTime){var l=u.roundingFunction(a,u);l!=a&&(a=l,r=null)}var o=this._int2time(a);if(t){this._setTimeValue(o);var c=new CustomEvent("timeRangeError",g);this.targetEl.dispatchEvent(c)}else this._setTimeValue(o,r)}}},{key:"_isTimeRangeError",value:function(e,r){if(r.minTime!==null&&r.maxTime!==null&&(e<r.minTime()||e>r.maxTime()))return!0;var u=y(r.disableTimeRanges),a;try{for(u.s();!(a=u.n()).done;){var n=a.value;if(e>=n[0]&&e<n[1])return!0}}catch(t){u.e(t)}finally{u.f()}return!1}},{key:"_generateNoneElement",value:function(e,r){var u,a,n;d(e)=="object"?(u=e.label,a=e.className,n=e.value):typeof e=="string"?(u=e,n=""):$.error("Invalid noneOption value");var t;return r?(t=document.createElement("option"),t.value=n):(t=document.createElement("li"),t.dataset.time=String(n)),t.innerText=u,t.classList.add(a),t}},{key:"_handleKeyUp",value:function(e){var r=this;if(!this.list||!i.isVisible(this.list)||this.settings.disableTextInput)return!0;if(e.type==="paste"||e.type==="cut"){var u=function(){r.settings.typeaheadHighlight?r._setSelected():r.list.hide()};setTimeout(u,0);return}switch(e.keyCode){case 96:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 77:case 80:case 186:case 8:case 46:this.settings.typeaheadHighlight?this._setSelected():this.list.hide();break}}}],[{key:"extractAttrOptions",value:function(e,r){var u={},a=y(r),n;try{for(a.s();!(n=a.n()).done;){var t=n.value;t in e.dataset&&(u[t]=e.dataset[t])}}catch(l){a.e(l)}finally{a.f()}return u}},{key:"isVisible",value:function(e){var r=e[0];return r.offsetWidth>0&&r.offsetHeight>0}},{key:"hideAll",value:function(){var e=y(document.getElementsByClassName("ui-timepicker-input")),r;try{for(e.s();!(r=e.n()).done;){var u=r.value,a=u.timepickerObj;a&&a.hideMe()}}catch(n){e.e(n)}finally{e.f()}}}]),i}();(function(){if(typeof window.CustomEvent=="function")return!1;function i(s,e){e||(e={}),e=Object.assign(g,e);var r=document.createEvent("CustomEvent");return r.initCustomEvent(s,e.bubbles,e.cancelable,e.detail),r}window.CustomEvent=i})();function W(i){if(!i.noneOption)return[];var s=A(i.noneOption);return Array.isArray(i.noneOption)?s:[s]}function A(i){return Array.isArray(i)?i.map(A):i===!0?{label:"None",value:""}:d(i)==="object"?i:{label:i,value:""}}function K(i){var s,e,r=i.settings,u=(s=r.minTime())!==null&&s!==void 0?s:0,a=(e=r.maxTime())!==null&&e!==void 0?e:u+m-1;a<u&&(a+=m),a===m-1&&typeof r.timeFormat=="string"&&r.show2400&&(a=m);for(var n=[],t=u,l=0;t<=a;l++,t+=r.step(l)*60){var o=t,c=i._int2time(o),f=o%m<m/2?"ui-timepicker-am":"ui-timepicker-pm",p={label:c,value:S(o,r),className:f};if((r.minTime()!==null||r.durationTime()!==null)&&r.showDuration){var h,v=(h=r.durationTime())!==null&&h!==void 0?h:r.minTime();v>t&&(v-=m);var V=i._int2duration(t-v,r.step());p.duration=V}var b=y(r.disableTimeRanges),w;try{for(b.s();!(w=b.n()).done;){var H=w.value;if(o%m>=H[0]&&o%m<H[1]){p.disabled=!0;break}}}catch(Q){b.e(Q)}finally{b.f()}n.push(p)}return n}function U(i){var s=document.createElement("option");return s.value=i.value||i.label,i.duration?s.appendChild(document.createTextNode(i.label+" ("+i.duration+")")):s.appendChild(document.createTextNode(i.label)),i.disabled&&(s.disabled=!0),s}function B(i){var s=document.createElement("li");if(s.dataset.time=i.value,i.className&&s.classList.add(i.className),s.className=i.className,s.appendChild(document.createTextNode(i.label)),i.duration){var e=document.createElement("span");e.appendChild(document.createTextNode("("+i.duration+")")),e.classList.add("ui-timepicker-duration"),s.appendChild(e)}return i.disabled&&s.classList.add("ui-timepicker-disabled"),s}function q(i){var s=document.createElement("ul");s.classList.add("ui-timepicker-list");var e=y(i),r;try{for(e.s();!(r=e.n()).done;){var u=r.value,a=B(u);s.appendChild(a)}}catch(t){e.e(t)}finally{e.f()}var n=document.createElement("div");return n.classList.add("ui-timepicker-wrapper"),n.tabIndex=-1,n.style.display="none",n.style.position="absolute",n.appendChild(s),n}function G(i,s){var e=document.createElement("select");e.classList.add("ui-timepicker-select"),s&&(e.name="ui-timepicker-"+s);var r=y(i),u;try{for(r.s();!(u=r.n()).done;){var a=u.value,n=U(a);e.appendChild(n)}}catch(t){r.e(t)}finally{r.f()}return e}function R(i){var s=[].concat(W(i.settings),K(i)),e;if(i.settings.useSelect?e=G(s,i.targetEl.name):e=q(s),i.settings.className){var r=y(i.settings.className.split(" ")),u;try{for(r.s();!(u=r.n()).done;){var a=u.value;e.classList.add(a)}}catch(n){r.e(n)}finally{r.f()}}return i.settings.showDuration&&(i.settings.minTime!==null||i.settings.durationTime!==null)&&(e.classList.add("ui-timepicker-with-duration"),e.classList.add("ui-timepicker-step-"+i.settings.step())),e}(function(i){d(O)==="object"&&O&&d(E)==="object"&&E&&E.exports===O?i(Y()):i(jQuery)})(function(i){var s={init:function(n){return this.each(function(){var t=i(this),l=new k(this,n),o=l.settings;if(o.lang,this.timepickerObj=l,t.addClass("ui-timepicker-input"),o.useSelect)e(t);else{if(t.prop("autocomplete","off"),o.showOn)for(var c in o.showOn)t.on(o.showOn[c]+".timepicker",s.show);t.on("change.timepicker",l._handleFormatValue),t.on("keydown.timepicker",u),t.on("keyup.timepicker",l._handleKeyUp),o.disableTextInput&&t.on("keydown.timepicker",l._disableTextInputHandler),t.on("cut.timepicker",l._handleKeyUp),t.on("paste.timepicker",l._handleKeyUp),l._formatValue(null,"initial")}})},show:function(n){var t=i(this),l=t[0].timepickerObj,o=l.settings;if(n&&n.preventDefault(),o.useSelect){l.list.trigger("focus");return}l._hideKeyboard()&&t.trigger("blur");var c=l.list;if(!t.prop("readonly")&&(e(t),c=l.list,!k.isVisible(c))){t.is("input")&&(l.selectedValue=t.val()),l._setSelected(),k.hideAll(),typeof o.listWidth=="number"&&c.width(t.outerWidth()*o.listWidth),c.show();var f={};o.orientation.match(/r/)?f.left=t.offset().left+t.outerWidth()-c.outerWidth()+parseInt(c.css("marginLeft").replace("px",""),10):o.orientation.match(/l/)?f.left=t.offset().left+parseInt(c.css("marginLeft").replace("px",""),10):o.orientation.match(/c/)&&(f.left=t.offset().left+(t.outerWidth()-c.outerWidth())/2+parseInt(c.css("marginLeft").replace("px",""),10));var p;o.orientation.match(/t/)?p="t":o.orientation.match(/b/)?p="b":t.offset().top+t.outerHeight(!0)+c.outerHeight()>i(window).height()+i(window).scrollTop()?p="t":p="b",p=="t"?(c.addClass("ui-timepicker-positioned-top"),f.top=t.offset().top-c.outerHeight()+parseInt(c.css("marginTop").replace("px",""),10)):(c.removeClass("ui-timepicker-positioned-top"),f.top=t.offset().top+t.outerHeight()+parseInt(c.css("marginTop").replace("px",""),10)),c.offset(f);var h=c.find(".ui-timepicker-selected");if(!h.length){var v=l.anytime2int(l._getTimeValue());v!==null?h=i(l._findRow(v)):o.scrollDefault()&&(h=i(l._findRow(o.scrollDefault())))}if((!h.length||h.hasClass("ui-timepicker-disabled"))&&(h=c.find("li:not(.ui-timepicker-disabled):first")),h&&h.length){var V=c.scrollTop()+h.position().top-h.outerHeight();c.scrollTop(V)}else c.scrollTop(0);return o.stopScrollPropagation&&i(document).on("wheel.ui-timepicker",".ui-timepicker-wrapper",function(b){b.preventDefault();var w=i(this).scrollTop();i(this).scrollTop(w+b.originalEvent.deltaY)}),i(document).on("mousedown.ui-timepicker",r),window.addEventListener("resize",r),o.closeOnScroll&&i(o.closeOnScroll).on("scroll.ui-timepicker",r),t.trigger("showTimepicker"),this}},hide:function(n){var t=this[0].timepickerObj;return t&&t.hideMe(),k.hideAll(),this},option:function(n,t){if(typeof n=="string"&&typeof t>"u"){var l=this[0].timepickerObj;return l.settings[n]}return this.each(function(){var o=i(this),c=o[0].timepickerObj,f=c.settings,p=c.list;d(n)=="object"?f=i.extend(f,n):typeof n=="string"&&(f[n]=t),f=c.parseSettings(f),c.settings=f,c._formatValue({type:"change"},"initial"),p&&(p.remove(),c.list=null),f.useSelect&&e(o)})},getSecondsFromMidnight:function(){var n=this[0].timepickerObj;return n.anytime2int(n._getTimeValue())},getTime:function(n){var t=this[0].timepickerObj,l=t._getTimeValue();if(!l)return null;var o=t.anytime2int(l);if(o===null)return null;n||(n=new Date);var c=new Date(n);return c.setHours(o/3600),c.setMinutes(o%3600/60),c.setSeconds(o%60),c.setMilliseconds(0),c},isVisible:function(){var n=this[0].timepickerObj;return!!(n&&n.list&&k.isVisible(n.list))},setTime:function(n){var t=this[0].timepickerObj,l=t.settings,o=t.anytime2int(n);if(t._isTimeRangeError(o,l)){var c=new CustomEvent("timeRangeError",g);t.targetEl.dispatchEvent(c)}if(l.forceRoundTime)var f=t._roundAndFormatTime(o);else var f=t._int2time(o);return n&&f===null&&l.noneOption&&(f=n),t._setTimeValue(f,"initial"),t._formatValue({type:"change"},"initial"),t&&t.list&&t._setSelected(),this},remove:function(){var n=this;if(n.hasClass("ui-timepicker-input")){var t=n[0].timepickerObj,l=t.settings;return n.removeAttr("autocomplete","off"),n.removeClass("ui-timepicker-input"),n.removeData("timepicker-obj"),n.off(".timepicker"),t.list&&t.list.remove(),l.useSelect&&n.show(),t.list=null,this}}};function e(a){var n=a[0].timepickerObj,t=n.list,l=n.settings;t&&t.length&&(t.remove(),n.list=null);var o=i(R(n));if(l.useSelect?t=o:t=o.children("ul"),o.data("timepicker-input",a),n.list=o,l.useSelect)a.val()&&t.val(n._roundAndFormatTime(n.anytime2int(a.val()))),t.on("focus",function(){i(this).data("timepicker-input").trigger("showTimepicker")}),t.on("blur",function(){i(this).data("timepicker-input").trigger("hideTimepicker")}),t.on("change",function(){n._setTimeValue(i(this).val(),"select")}),n._setTimeValue(t.val(),"initial"),a.hide().after(t);else{var c=l.appendTo;typeof c=="string"?c=i(c):typeof c=="function"&&(c=c(a)),c.append(o),n._setSelected(),t.on("mousedown click","li",function(f){a.off("focus.timepicker"),a.on("focus.timepicker-ie-hack",function(){a.off("focus.timepicker-ie-hack"),a.on("focus.timepicker",s.show)}),n._hideKeyboard()||a[0].focus(),t.find("li").removeClass("ui-timepicker-selected"),i(this).addClass("ui-timepicker-selected"),n._selectValue()&&(a.trigger("hideTimepicker"),t.on("mouseup.timepicker click.timepicker","li",function(p){t.off("mouseup.timepicker click.timepicker"),o.hide()}))})}}function r(a){if(!(a.type=="focus"&&a.target==window)){var n=i(a.target);n.closest(".ui-timepicker-input").length||n.closest(".ui-timepicker-wrapper").length||(k.hideAll(),i(document).off(".ui-timepicker"),i(window).off(".ui-timepicker"))}}function u(a){var n=i(this),t=n[0].timepickerObj,l=t.list;if(!l||!k.isVisible(l))if(a.keyCode==40)s.show.call(n.get(0)),l=t.list,t._hideKeyboard()||n.trigger("focus");else return!0;switch(a.keyCode){case 13:return t._selectValue()&&(t._formatValue({type:"change"}),t.hideMe()),a.preventDefault(),!1;case 38:var o=l.find(".ui-timepicker-selected");return o.length?o.is(":first-child")||(o.removeClass("ui-timepicker-selected"),o.prev().addClass("ui-timepicker-selected"),o.prev().position().top<o.outerHeight()&&l.scrollTop(l.scrollTop()-o.outerHeight())):(l.find("li").each(function(c,f){if(i(f).position().top>0)return o=i(f),!1}),o.addClass("ui-timepicker-selected")),!1;case 40:return o=l.find(".ui-timepicker-selected"),o.length===0?(l.find("li").each(function(c,f){if(i(f).position().top>0)return o=i(f),!1}),o.addClass("ui-timepicker-selected")):o.is(":last-child")||(o.removeClass("ui-timepicker-selected"),o.next().addClass("ui-timepicker-selected"),o.next().position().top+2*o.outerHeight()>l.outerHeight()&&l.scrollTop(l.scrollTop()+o.outerHeight())),!1;case 27:l.find("li").removeClass("ui-timepicker-selected"),t.hideMe();break;case 9:t.hideMe();break;default:return!0}}i.fn.timepicker=function(a){if(!this.length)return this;if(s[a])return this.hasClass("ui-timepicker-input")?s[a].apply(this,Array.prototype.slice.call(arguments,1)):this;if(d(a)==="object"||!a)return s.init.apply(this,arguments);i.error("Method "+a+" does not exist on jQuery.timepicker")},i.fn.timepicker.defaults=C})})()})(T,T.exports);T.exports;
