@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import 'datatables.net-responsive-bs5/css/responsive.bootstrap5';
@import 'mixins';

// Responsive table area '+' icon position
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  &:before,
  &:before {
    position: absolute;
    line-height: 0.9em;
    font-weight: light.$font-weight-medium;
    height: 0.85em;
    width: 0.85em;
    color: light.$white;
    border-radius: 1em;
    box-sizing: content-box;
    text-align: center;
    font-family: 'Courier New', Courier, monospace;
    content: '+';
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: '+';
}
// To scroll within datatable area
@media screen and (max-width: 1399.98px) {
  table.dataTable.table-responsive {
    display: block;
  }
}

// Modal table style
.modal.dtr-bs-modal {
  .modal-body {
    padding: 0;
  }
  .table {
    tr:last-child > td {
      border-bottom: 0;
    }
    .btn {
      box-shadow: none !important;
    }
    .emp_name {
      font-weight: light.$font-weight-medium;
    }
  }
}
