<!-- BEGIN: Theme CSS-->
<!-- Fonts - Optimized for Performance -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<!-- Preload critical font weights -->
<link rel="preload" as="font" href="https://fonts.gstatic.com/s/publicsans/v15/ijwGs572Xtc6ZYQws9YVwllKVG8qX1oyOymuFpmJygcob0yL_V1g.woff2" type="font/woff2" crossorigin>
<link rel="preload" as="font" href="https://fonts.gstatic.com/s/publicsans/v15/ijwGs572Xtc6ZYQws9YVwllKVG8qX1oyOymuFpmJygcob0yL_V1g.woff2" type="font/woff2" crossorigin>
<!-- Load fonts with display=swap for better performance -->
<link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
<noscript><link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet"></noscript>

<!-- Icons -->
@vite(['resources/assets/vendor/fonts/tabler-icons.scss'])

<!-- Core CSS -->
@vite([
  'resources/assets/vendor/scss'.$configData['rtlSupport'].'/core' .($configData['style'] !== 'light' ? '-' . $configData['style'] : '') .'.scss',
  'resources/assets/vendor/scss'.$configData['rtlSupport'].'/' .$configData['theme'] .($configData['style'] !== 'light' ? '-' . $configData['style'] : '') .'.scss',
  'resources/assets/css/demo.css',
  'resources/assets/vendor/libs/node-waves/node-waves.scss',
  'resources/assets/vendor/scss/pages/front-page.scss'
])

<!-- Vendor Styles -->
@yield('vendor-style')


<!-- Page Styles -->
@yield('page-style')
