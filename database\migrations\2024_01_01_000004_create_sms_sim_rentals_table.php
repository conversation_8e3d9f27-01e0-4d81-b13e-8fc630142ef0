<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_sim_rentals', function (Blueprint $table) {
            $table->id();
            $table->string('sim_id')->unique(); // SIM001, SIM002, etc.
            $table->string('phone_number');
            $table->foreignId('server_id')->constrained('sms_servers')->onDelete('cascade');
            $table->foreignId('country_id')->constrained('sms_countries')->onDelete('cascade');
            $table->foreignId('service_id')->constrained('sms_services')->onDelete('cascade');
            $table->decimal('price', 10, 2);
            $table->enum('status', ['active', 'waiting_otp', 'completed', 'expired', 'cancelled'])->default('active');
            $table->string('otp_code')->nullable();
            $table->timestamp('otp_received_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_sim_rentals');
    }
};
