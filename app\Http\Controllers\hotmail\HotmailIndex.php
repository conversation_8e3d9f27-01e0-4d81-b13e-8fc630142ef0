<?php

namespace App\Http\Controllers\hotmail;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class HotmailIndex extends Controller
{
    public function index(Request $request)
    {
        $hotmails = config('hotmail');
        $tags = collect($hotmails)
            ->pluck('Tags')
            ->flatten()
            ->unique()
            ->values()
            ->all();
        if ($request->has('search') || $request->has('tag')) {
            $search = $request->input('search');
            $filterTags = (array) $request->input('tag', []);
            $hotmails = collect($hotmails)->filter(function ($item) use ($search, $filterTags) {
                return (!$search || stripos($item['Name'], $search) !== false)
                    && (empty($filterTags) || count(array_intersect($filterTags, (array)($item['Tags'] ?? []))) > 0);
            })->values()->all();
        }
        foreach ($hotmails as $index => $item) {
            $key = 'hotmail_' . $item['AccountCode'];
            $hotmails[$index]['Instock'] = Cache::get($key);
        }
        return view('content.hotmail.hotmail-index', compact('hotmails', 'tags'));
    }
}
