<?php

namespace App\Services;

use App\Models\User;
use App\Models\TransactionLog;
use Illuminate\Support\Facades\DB;
use Exception;

class ConvertMoney
{
    public static function usdToVnd($value)
    {
        $convertRate = config('app.convert_rate');
        // dd(round($value * $convertRate));
        return round($value * $convertRate);
    }

    public static  function vndToUsd($value)
    {
        $convertRate = config('app.convert_rate');
        return round($value / $convertRate, 2);
    }
}
