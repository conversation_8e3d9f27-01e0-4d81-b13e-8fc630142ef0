<!-- Server Modal -->
<div class="modal fade" id="serverModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serverModalTitle">Add Server</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="serverForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="serverName" class="form-label">Server Name *</label>
                            <input type="text" class="form-control" id="serverName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="serverSortOrder" class="form-label">Sort Order *</label>
                            <input type="number" class="form-control" id="serverSortOrder" name="sort_order" min="0" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="serverDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="serverDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="serverIsActive" name="is_active" checked>
                            <label class="form-check-label" for="serverIsActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Server</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Country Modal -->
<div class="modal fade" id="countryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="countryModalTitle">Add Country</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="countryForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="countryName" class="form-label">Country Name *</label>
                            <input type="text" class="form-control" id="countryName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="countryCode" class="form-label">Country Code *</label>
                            <input type="text" class="form-control" id="countryCode" name="code" maxlength="2" style="text-transform: uppercase;" required>
                            <div class="form-text">2-letter country code (e.g., VN, US)</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="countryServer" class="form-label">Server *</label>
                            <select class="form-select" id="countryServer" name="server_id" required>
                                <option value="">Select Server</option>
                                @foreach($servers as $server)
                                    <option value="{{ $server->id }}">{{ $server->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="countrySortOrder" class="form-label">Sort Order *</label>
                            <input type="number" class="form-control" id="countrySortOrder" name="sort_order" min="0" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="countryIsActive" name="is_active" checked>
                            <label class="form-check-label" for="countryIsActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Country</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Service Modal -->
<div class="modal fade" id="serviceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceModalTitle">Add Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="serviceForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="serviceName" class="form-label">Service Name *</label>
                            <input type="text" class="form-control" id="serviceName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="serviceCode" class="form-label">Service Code *</label>
                            <input type="text" class="form-control" id="serviceCode" name="code" required>
                            <div class="form-text">Unique identifier (e.g., telegram, whatsapp)</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="serviceServer" class="form-label">Server *</label>
                            <select class="form-select" id="serviceServer" name="server_id" required>
                                <option value="">Select Server</option>
                                @foreach($servers as $server)
                                    <option value="{{ $server->id }}">{{ $server->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="serviceCountry" class="form-label">Country *</label>
                            <select class="form-select" id="serviceCountry" name="country_id" required>
                                <option value="">Select Country</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="servicePrice" class="form-label">Price (VNĐ) *</label>
                            <input type="number" class="form-control" id="servicePrice" name="price" min="0" step="1000" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="serviceStock" class="form-label">Stock *</label>
                            <input type="number" class="form-control" id="serviceStock" name="stock" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="serviceSortOrder" class="form-label">Sort Order *</label>
                            <input type="number" class="form-control" id="serviceSortOrder" name="sort_order" min="0" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="serviceIsActive" name="is_active" checked>
                            <label class="form-check-label" for="serviceIsActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Service</button>
                </div>
            </form>
        </div>
    </div>
</div>
