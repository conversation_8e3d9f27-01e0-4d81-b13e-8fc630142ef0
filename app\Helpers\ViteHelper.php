<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

class ViteHelper
{
    /**
     * Get Vite asset URL
     * 
     * @param string $asset
     * @return string
     */
    public static function asset($asset)
    {
        // In development, return the dev server URL
        if (app()->environment('local') && self::isDevServerRunning()) {
            return "http://localhost:5173/{$asset}";
        }
        
        // In production, get from manifest
        return self::getProductionAssetUrl($asset);
    }
    
    /**
     * Check if Vite dev server is running
     * 
     * @return bool
     */
    private static function isDevServerRunning()
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 1,
                    'method' => 'HEAD'
                ]
            ]);
            
            $result = @file_get_contents('http://localhost:5173', false, $context);
            return $result !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get production asset URL from manifest
     * 
     * @param string $asset
     * @return string
     */
    private static function getProductionAssetUrl($asset)
    {
        $manifestPath = public_path('build/manifest.json');
        
        if (!File::exists($manifestPath)) {
            // Fallback to basic path
            return "/build/{$asset}";
        }
        
        $manifest = json_decode(File::get($manifestPath), true);
        
        // Convert SCSS to CSS for production
        $assetKey = str_replace('.scss', '.css', $asset);
        
        if (isset($manifest[$asset])) {
            return "/build/{$manifest[$asset]['file']}";
        }
        
        if (isset($manifest[$assetKey])) {
            return "/build/{$manifest[$assetKey]['file']}";
        }
        
        // Fallback
        return "/build/" . str_replace('.scss', '.css', $asset);
    }
    
    /**
     * Get CSS URLs for deferred loading
     * 
     * @return array
     */
    public static function getDeferredCSSUrls()
    {
        return [
            'fontawesome' => self::asset('resources/assets/vendor/fonts/fontawesome.scss'),
            'node-waves' => self::asset('resources/assets/vendor/libs/node-waves/node-waves.scss'),
            'perfect-scrollbar' => self::asset('resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss'),
            'typeahead-js' => self::asset('resources/assets/vendor/libs/typeahead-js/typeahead.scss'),
            'datatables' => self::asset('resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'),
            'datatables-responsive' => self::asset('resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss'),
            'datatables-buttons' => self::asset('resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss'),
        ];
    }
    
    /**
     * Generate deferred CSS loading script
     * 
     * @param array $cssFiles
     * @return string
     */
    public static function generateDeferredCSSScript($cssFiles = [])
    {
        if (empty($cssFiles)) {
            $cssFiles = ['fontawesome', 'flag-icons', 'node-waves'];
        }
        
        $urls = self::getDeferredCSSUrls();
        $script = '<script>';
        $script .= '(function() {';
        $script .= 'function loadCSS(href) {';
        $script .= 'var link = document.createElement("link");';
        $script .= 'link.rel = "stylesheet";';
        $script .= 'link.href = href;';
        $script .= 'document.head.appendChild(link);';
        $script .= '}';
        $script .= 'window.addEventListener("load", function() {';
        $script .= 'setTimeout(function() {';
        
        foreach ($cssFiles as $cssFile) {
            if (isset($urls[$cssFile])) {
                $script .= 'loadCSS("' . $urls[$cssFile] . '");';
            }
        }
        
        $script .= '}, 100);';
        $script .= '});';
        $script .= '})();';
        $script .= '</script>';
        
        return $script;
    }
    
    /**
     * Generate interaction-based CSS loading script
     * 
     * @param array $cssFiles
     * @return string
     */
    public static function generateInteractionCSSScript($cssFiles = [])
    {
        if (empty($cssFiles)) {
            $cssFiles = ['perfect-scrollbar', 'typeahead-js'];
        }
        
        $urls = self::getDeferredCSSUrls();
        $script = '<script>';
        $script .= '(function() {';
        $script .= 'function loadCSS(href) {';
        $script .= 'var link = document.createElement("link");';
        $script .= 'link.rel = "stylesheet";';
        $script .= 'link.href = href;';
        $script .= 'document.head.appendChild(link);';
        $script .= '}';
        $script .= 'var events = ["click", "scroll", "keydown", "touchstart"];';
        $script .= 'var loaded = false;';
        $script .= 'function loadInteractionCSS() {';
        $script .= 'if (loaded) return;';
        $script .= 'loaded = true;';
        
        foreach ($cssFiles as $cssFile) {
            if (isset($urls[$cssFile])) {
                $script .= 'loadCSS("' . $urls[$cssFile] . '");';
            }
        }
        
        $script .= 'events.forEach(function(event) {';
        $script .= 'document.removeEventListener(event, loadInteractionCSS);';
        $script .= '});';
        $script .= '}';
        $script .= 'events.forEach(function(event) {';
        $script .= 'document.addEventListener(event, loadInteractionCSS, { passive: true });';
        $script .= '});';
        $script .= 'setTimeout(loadInteractionCSS, 3000);';
        $script .= '})();';
        $script .= '</script>';
        
        return $script;
    }
}
