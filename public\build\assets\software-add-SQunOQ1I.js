(function(){const t=document.querySelector(".comment-editor");t&&new Quill(t,{modules:{toolbar:".comment-toolbar"},placeholder:"Product Description",theme:"snow"});const a=`<div class="dz-preview dz-file-preview">
<div class="dz-details">
  <div class="dz-thumbnail">
    <img data-dz-thumbnail>
    <span class="dz-nopreview">No preview</span>
    <div class="dz-success-mark"></div>
    <div class="dz-error-mark"></div>
    <div class="dz-error-message"><span data-dz-errormessage></span></div>
    <div class="progress">
      <div class="progress-bar progress-bar-primary" role="progressbar" aria-valuemin="0" aria-valuemax="100" data-dz-uploadprogress></div>
    </div>
  </div>
  <div class="dz-filename" data-dz-name></div>
  <div class="dz-size" data-dz-size></div>
</div>
</div>`,r=document.querySelector("#dropzone-basic");r&&new Dropzone(r,{previewTemplate:a,parallelUploads:1,maxFilesize:5,acceptedFiles:".jpg,.jpeg,.png,.gif",addRemoveLinks:!0,maxFiles:1});const s=document.querySelector("#tags");new Tagify(s,{whitelist:window.suggestions,dropdown:{enabled:1,classname:"custom-dropdown",maxItems:10,position:"text",closeOnSelect:!1}});const e=new Date,o=document.querySelector(".product-date");o&&o.flatpickr({monthSelectorType:"static",defaultDate:e})})();$(function(){var t=$(".select2");t.length&&t.each(function(){var e=$(this);e.wrap('<div class="position-relative"></div>').select2({dropdownParent:e.parent(),placeholder:e.data("placeholder")})});var a=$(".form-repeater");if(a.length){var r=2,s=1;a.on("submit",function(e){e.preventDefault()}),a.repeater({show:function(){var e=$(this).find(".form-control, .form-select"),o=$(this).find(".form-label");e.each(function(i){var d="form-repeater-"+r+"-"+s;$(e[i]).attr("id",d),$(o[i]).attr("for",d),s++}),r++,$(this).slideDown(),$(".select2-container").remove(),$(".select2.form-select").select2({placeholder:"Placeholder text"}),$(".select2-container").css("width","100%"),$(".form-repeater:first .form-select").select2({dropdownParent:$(this).parent(),placeholder:"Placeholder text"}),$(".position-relative .select2").each(function(){$(this).select2({dropdownParent:$(this).closest(".position-relative")})})}})}});
