class o{constructor(){this.loadedCSS=new Set,this.init()}init(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{this.loadDeferredCSS()}):this.loadDeferredCSS(),this.bindInteractionEvents()}loadDeferredCSS(){setTimeout(()=>{this.loadCSSFile("fontawesome","/build/assets/vendor/fonts/fontawesome.css"),this.loadCSSFile("flag-icons","/build/assets/vendor/fonts/flag-icons.css"),this.loadCSSFile("node-waves","/build/assets/vendor/libs/node-waves/node-waves.css")},100)}loadCSSFile(e,t){if(this.loadedCSS.has(e))return;const s=document.createElement("link");s.rel="stylesheet",s.href=t,s.media="all",document.head.appendChild(s),this.loadedCSS.add(e),this.showCSSLoadingIndicator(e)}bindInteractionEvents(){const e=["click","scroll","keydown","touchstart"],t=()=>{this.loadInteractionCSS(),e.forEach(s=>{document.removeEventListener(s,t,{passive:!0})})};e.forEach(s=>{document.addEventListener(s,t,{passive:!0})})}loadInteractionCSS(){this.loadCSSFile("perfect-scrollbar","/build/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css"),this.loadCSSFile("typeahead-js","/build/assets/vendor/libs/typeahead-js/typeahead.css")}showCSSLoadingIndicator(e){console.log(`Loading CSS: ${e}`)}preloadCSS(e){const t=document.createElement("link");t.rel="preload",t.as="style",t.href=e,document.head.appendChild(t)}loadPageSpecificCSS(e){switch(e){case"software":this.loadCSSFile("datatables","/build/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css");break;case"admin":this.loadCSSFile("admin-styles","/build/assets/css/admin.css");break}}removeUnusedCSS(){document.querySelectorAll('link[rel="stylesheet"]').forEach(t=>{const s=t.href;this.isUnusedCSS(s)&&t.remove()})}isUnusedCSS(e){const t=this.getCurrentPageType();return({software:["admin","datatables","calendar"],home:["admin","datatables","calendar","editor"]}[t]||[]).some(a=>e.includes(a))}getCurrentPageType(){const e=window.location.pathname;return e.includes("/software")?"software":e.includes("/admin")?"admin":e.includes("/hotmail")?"hotmail":e==="/"?"home":"default"}optimizeForMobile(){this.isMobile()&&setTimeout(()=>{this.loadDeferredCSS()},500)}isMobile(){return window.innerWidth<=768||/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}monitorPerformance(){"performance"in window&&window.addEventListener("load",()=>{setTimeout(()=>{const t=performance.getEntriesByType("resource").filter(s=>s.name.includes(".css"));console.log("CSS Loading Performance:",{totalCSS:t.length,totalSize:t.reduce((s,n)=>s+(n.transferSize||0),0),loadTime:t.reduce((s,n)=>Math.max(s,n.responseEnd),0)})},1e3)})}}document.addEventListener("DOMContentLoaded",()=>{window.cssLoader=new o,(window.location.hostname==="localhost"||window.location.hostname.includes("127.0.0.1"))&&window.cssLoader.monitorPerformance()});window.CSSLoader=o;
