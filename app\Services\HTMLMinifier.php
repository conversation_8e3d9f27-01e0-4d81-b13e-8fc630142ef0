<?php

namespace App\Services;

class HTMLMinifier
{
    /**
     * Minify HTML content
     *
     * @param string $html
     * @return string
     */
    public static function minify(string $html): string
    {
        // Preserve important whitespace in pre, code, textarea tags
        $preserveTags = [];
        $html = preg_replace_callback(
            '/<(pre|code|textarea|script|style)[^>]*>.*?<\/\1>/is',
            function ($matches) use (&$preserveTags) {
                $placeholder = '___PRESERVE_' . count($preserveTags) . '___';
                $preserveTags[$placeholder] = $matches[0];
                return $placeholder;
            },
            $html
        );

        // Remove HTML comments (except IE conditional comments)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

        // Remove extra whitespace between tags
        $html = preg_replace('/>\s+</', '><', $html);

        // Remove extra whitespace at the beginning and end of lines
        $html = preg_replace('/^\s+|\s+$/m', '', $html);

        // Remove multiple consecutive whitespace characters
        $html = preg_replace('/\s{2,}/', ' ', $html);

        // Remove whitespace around block elements
        $blockElements = [
            'html', 'head', 'body', 'title', 'meta', 'link', 'script', 'style',
            'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'dl', 'dt', 'dd',
            'table', 'thead', 'tbody', 'tfoot', 'tr', 'td', 'th',
            'form', 'fieldset', 'legend',
            'header', 'footer', 'nav', 'section', 'article', 'aside',
            'main', 'figure', 'figcaption'
        ];

        foreach ($blockElements as $element) {
            $html = preg_replace('/\s*(<\/?' . $element . '[^>]*>)\s*/i', '$1', $html);
        }

        // Remove empty lines
        $html = preg_replace('/\n\s*\n/', "\n", $html);

        // Restore preserved content
        foreach ($preserveTags as $placeholder => $content) {
            $html = str_replace($placeholder, $content, $html);
        }

        return trim($html);
    }

    /**
     * Minify CSS content
     *
     * @param string $css
     * @return string
     */
    public static function minifyCSS(string $css): string
    {
        // Remove comments
        $css = preg_replace('/\/\*.*?\*\//s', '', $css);

        // Remove extra whitespace
        $css = preg_replace('/\s+/', ' ', $css);

        // Remove whitespace around specific characters
        $css = preg_replace('/\s*([{}:;,>+~])\s*/', '$1', $css);

        // Remove trailing semicolon before closing brace
        $css = preg_replace('/;+}/', '}', $css);

        // Remove empty rules
        $css = preg_replace('/[^{}]+{\s*}/', '', $css);

        return trim($css);
    }

    /**
     * Minify JavaScript content
     *
     * @param string $js
     * @return string
     */
    public static function minifyJS(string $js): string
    {
        // Remove single line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);

        // Remove multi-line comments
        $js = preg_replace('/\/\*.*?\*\//s', '', $js);

        // Remove extra whitespace
        $js = preg_replace('/\s+/', ' ', $js);

        // Remove whitespace around operators and punctuation
        $js = preg_replace('/\s*([=+\-*\/{}();,:])\s*/', '$1', $js);

        // Remove trailing semicolons before closing braces
        $js = preg_replace('/;+}/', '}', $js);

        return trim($js);
    }

    /**
     * Advanced HTML minification with more aggressive optimization
     *
     * @param string $html
     * @param array $options
     * @return string
     */
    public static function minifyAdvanced(string $html, array $options = []): string
    {
        $defaultOptions = [
            'remove_comments' => true,
            'remove_empty_attributes' => true,
            'remove_default_attributes' => true,
            'remove_redundant_attributes' => true,
            'minify_css' => true,
            'minify_js' => true,
        ];

        $options = array_merge($defaultOptions, $options);

        // Preserve important content
        $preserveTags = [];
        $html = preg_replace_callback(
            '/<(pre|code|textarea)[^>]*>.*?<\/\1>/is',
            function ($matches) use (&$preserveTags) {
                $placeholder = '___PRESERVE_' . count($preserveTags) . '___';
                $preserveTags[$placeholder] = $matches[0];
                return $placeholder;
            },
            $html
        );

        // Minify inline CSS
        if ($options['minify_css']) {
            $html = preg_replace_callback(
                '/<style[^>]*>(.*?)<\/style>/is',
                function ($matches) {
                    return '<style>' . self::minifyCSS($matches[1]) . '</style>';
                },
                $html
            );
        }

        // Minify inline JavaScript
        if ($options['minify_js']) {
            $html = preg_replace_callback(
                '/<script[^>]*>(.*?)<\/script>/is',
                function ($matches) {
                    if (strpos($matches[0], 'src=') === false) {
                        return '<script>' . self::minifyJS($matches[1]) . '</script>';
                    }
                    return $matches[0];
                },
                $html
            );
        }

        // Remove HTML comments
        if ($options['remove_comments']) {
            $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
        }

        // Remove empty attributes
        if ($options['remove_empty_attributes']) {
            $html = preg_replace('/\s+(?:class|id|style|title|alt)=["\']\s*["\']/i', '', $html);
        }

        // Remove default attributes
        if ($options['remove_default_attributes']) {
            $html = preg_replace('/\s+method=["\']get["\']/i', '', $html);
            $html = preg_replace('/\s+type=["\']text\/javascript["\']/i', '', $html);
            $html = preg_replace('/\s+type=["\']text\/css["\']/i', '', $html);
        }

        // Basic minification
        $html = self::minify($html);

        // Restore preserved content
        foreach ($preserveTags as $placeholder => $content) {
            $html = str_replace($placeholder, $content, $html);
        }

        return $html;
    }

    /**
     * Calculate compression ratio
     *
     * @param string $original
     * @param string $minified
     * @return array
     */
    public static function getCompressionStats(string $original, string $minified): array
    {
        $originalSize = strlen($original);
        $minifiedSize = strlen($minified);
        $savedBytes = $originalSize - $minifiedSize;
        $compressionRatio = $originalSize > 0 ? ($savedBytes / $originalSize) * 100 : 0;

        return [
            'original_size' => $originalSize,
            'minified_size' => $minifiedSize,
            'saved_bytes' => $savedBytes,
            'compression_ratio' => round($compressionRatio, 2),
        ];
    }
}
