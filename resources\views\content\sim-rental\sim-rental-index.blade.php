@extends('layouts/layoutMaster')

@section('title', '<PERSON><PERSON><PERSON> - <PERSON>ịch vụ thuê sim số')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss'])
@endsection

@section('page-style')
    @vite(['resources/assets/vendor/scss/pages/cards-advance.scss', 'resources/assets/scss/pages/sim-rental.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection

@section('page-script')
    @vite(['resources/assets/js/card/cards-advance.js'])
    <script>
        $(document).ready(function() {
            // Wait for cards-advance.js to initialize Select2, then override
            setTimeout(function() {
                // Override Select2 initialization for custom placeholders and templates
                $('#countrySelect').select2('destroy').select2({
                    placeholder: 'Chọn quốc gia...',
                    allowClear: true,
                    templateResult: formatCountry,
                    templateSelection: formatCountry
                });

                $('#serviceSelect').select2('destroy').select2({
                    placeholder: 'Chọn dịch vụ...',
                    allowClear: true,
                    templateResult: formatService,
                    templateSelection: formatService
                });

                console.log('Select2 re-initialized with custom settings');

                // Load data for first server on page load
                var firstServerId = $('.server-tab.active').data('server');
                if (firstServerId) {
                    loadServerData(firstServerId);
                }
            }, 100);

            // Format country options with flags
            function formatCountry(country) {
                if (!country.id) return country.text;

                var flag = $(country.element).data('flag');
                var $country = $(
                    '<span><img src="' + flag + '" class="country-flag me-2" /> ' + country.text + '</span>'
                );
                return $country;
            }

            // Format service options with icons
            function formatService(service) {
                if (!service.id) return service.text;

                var icon = $(service.element).data('icon');
                var $service = $(
                    '<span><i class="' + icon + ' me-2"></i> ' + service.text + '</span>'
                );
                return $service;
            }

            // Server tab switching
            $('.server-tab').click(function() {
                $('.server-tab').removeClass('active');
                $(this).addClass('active');

                var serverId = $(this).data('server');
                loadServerData(serverId);
            });

            // Load server data
            function loadServerData(serverId) {
                console.log('Loading data for server:', serverId);

                // Load countries for this server
                $.ajax({
                    url: `/sim-rental/countries/${serverId}`,
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            updateCountrySelect(response.data);
                        }
                    },
                    error: function(xhr) {
                        console.error('Error loading countries:', xhr);
                    }
                });

                // Clear service select
                $('#serviceSelect').empty().append('<option value="">Chọn dịch vụ...</option>');
            }

            // Update country select options
            function updateCountrySelect(countries) {
                var $countrySelect = $('#countrySelect');
                $countrySelect.empty().append('<option value="">Chọn quốc gia...</option>');

                countries.forEach(function(country) {
                    $countrySelect.append(`<option value="${country.id}">${country.name}</option>`);
                });

                // Trigger Select2 update
                $countrySelect.trigger('change');
            }

            // Load services when country changes
            $('#countrySelect').on('change', function() {
                var countryId = $(this).val();
                var serverId = $('.server-tab.active').data('server');

                if (countryId && serverId) {
                    $.ajax({
                        url: `/sim-rental/services/${serverId}/${countryId}`,
                        method: 'GET',
                        success: function(response) {
                            if (response.success) {
                                updateServiceSelect(response.data);
                            }
                        },
                        error: function(xhr) {
                            console.error('Error loading services:', xhr);
                        }
                    });
                } else {
                    $('#serviceSelect').empty().append('<option value="">Chọn dịch vụ...</option>');
                }
            });

            // Update service select options
            function updateServiceSelect(services) {
                var $serviceSelect = $('#serviceSelect');
                $serviceSelect.empty().append('<option value="">Chọn dịch vụ...</option>');

                services.forEach(function(service) {
                    var stockText = service.stock > 0 ? ` (${service.stock} có sẵn)` : ' (Hết hàng)';
                    var disabled = service.stock <= 0 ? 'disabled' : '';
                    $serviceSelect.append(`<option value="${service.id}" ${disabled}>${service.name} - ${service.price.toLocaleString()}đ${stockText}</option>`);
                });

                // Trigger Select2 update
                $serviceSelect.trigger('change');
            }

            // Filter functionality
            $('#countrySelect, #serviceSelect').change(function() {
                filterSimCards();
            });

            function filterSimCards() {
                var country = $('#countrySelect').val();
                var service = $('#serviceSelect').val();

                console.log('Filtering by country:', country, 'service:', service);
                // Here you would implement the filtering logic
            }

            // Rent Sim button functionality
            $('#rentSimBtn').click(function() {
                var countryId = $('#countrySelect').val();
                var serviceId = $('#serviceSelect').val();
                var serverId = $('.server-tab.active').data('server');

                if (!countryId) {
                    alert('Vui lòng chọn quốc gia!');
                    $('#countrySelect').focus();
                    return;
                }

                if (!serviceId) {
                    alert('Vui lòng chọn dịch vụ!');
                    $('#serviceSelect').focus();
                    return;
                }

                // Disable button and show loading
                $(this).prop('disabled', true).html(
                    '<i class="spinner-border spinner-border-sm me-2"></i>Đang thuê...');

                // Make API call to rent sim
                $.ajax({
                    url: '/sim-rental/rent',
                    method: 'POST',
                    data: {
                        server_id: serverId,
                        country_id: countryId,
                        service_id: serviceId,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            alert(`Thuê sim thành công!\nSố điện thoại: ${response.data.phone_number}\nID: ${response.data.sim_id}\nGiá: ${response.data.price.toLocaleString()}đ`);

                            // Add new row to history table
                            addSimToHistory(response.data);

                            // Reset selects
                            $('#countrySelect, #serviceSelect').val('').trigger('change');

                            // Reload current server data to update stock
                            loadServerData(serverId);
                        } else {
                            alert('Lỗi: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        var errorMessage = 'Có lỗi xảy ra khi thuê sim!';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        alert('Lỗi: ' + errorMessage);
                    },
                    complete: function() {
                        // Reset button
                        $('#rentSimBtn').prop('disabled', false).html(
                            '<i class="ti ti-plus me-2"></i>Thuê Sim Ngay');
                    }
                });
            });

            // Add sim to history table
            function addSimToHistory(simData) {
                var now = new Date();
                var timeStr = now.toLocaleString('vi-VN');

                var newRow = `
      <tr>
        <td><span class="badge bg-label-primary">#${simData.sim_id}</span></td>
        <td>
          <div class="d-flex align-items-center">
            <i class="ti ti-phone me-2 text-primary"></i>
            <span class="fw-medium">${simData.phone_number}</span>
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <span>${simData.country_name}</span>
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <span>${simData.service_name}</span>
          </div>
        </td>
        <td><span class="text-success fw-medium">${simData.price.toLocaleString()} VNĐ</span></td>
        <td><small class="text-muted">${timeStr}</small></td>
        <td><small class="text-muted">${simData.expires_at}</small></td>
        <td><span class="badge bg-primary">${simData.status}</span></td>
        <td><span class="text-muted">Đang chờ...</span></td>
        <td>
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
              <i class="ti ti-dots-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#"><i class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
              <li><a class="dropdown-item" href="#"><i class="ti ti-copy me-2"></i>Copy số</a></li>
              <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i>Làm mới</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item text-danger" href="#"><i class="ti ti-trash me-2"></i>Hủy sim</a></li>
            </ul>
          </div>
        </td>
      </tr>
    `;

                $('#simHistoryTable tbody').prepend(newRow);
            }

            // History table filter functionality
            $('[data-filter]').click(function(e) {
                e.preventDefault();
                var filter = $(this).data('filter');

                if (filter === 'all') {
                    $('#simHistoryTable tbody tr').show();
                } else {
                    $('#simHistoryTable tbody tr').hide();

                    var statusMap = {
                        'active': 'Đang hoạt động',
                        'completed': 'Hoàn thành',
                        'expired': 'Hết hạn',
                        'failed': 'Thất bại'
                    };

                    $('#simHistoryTable tbody tr').each(function() {
                        var status = $(this).find('td:nth-child(9) .badge').text().trim();
                        if (status === statusMap[filter]) {
                            $(this).show();
                        }
                    });
                }
            });
        });
    </script>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">🔢 Dịch vụ thuê Sim số</h4>
                            <p class="mb-0 text-muted">Chọn server, quốc gia và dịch vụ để thuê sim số phù hợp với nhu cầu
                                của bạn</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="badge bg-label-success fs-6 px-3 py-2">
                                <i class="ti ti-check me-1"></i>
                                Hệ thống hoạt động ổn định
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Selection Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3">Chọn Server</h5>
                    <div class="server-tabs">
                        @foreach($servers as $index => $server)
                            <button class="server-tab {{ $index === 0 ? 'active' : '' }}" data-server="{{ $server->id }}">
                                <i class="ti ti-server me-2"></i>
                                {{ $server->name }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="countrySelect" class="form-label fw-medium">Quốc gia</label>
                        <select id="countrySelect" class="select2 form-select">
                            <option value="">Chọn quốc gia...</option>
                            {{-- Countries will be loaded dynamically based on selected server --}}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="serviceSelect" class="form-label fw-medium">Dịch vụ</label>
                        <select id="serviceSelect" class="select2 form-select">
                            <option value="">Chọn dịch vụ...</option>
                            {{-- Services will be loaded dynamically based on selected country --}}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button id="rentSimBtn" class="btn btn-primary w-100 d-flex align-items-center justify-content-center" style="height: 42px;">
                            <i class="ti ti-plus me-2"></i>
                            Thuê Sim Ngay
                        </button>
                    </div>
                </div>
            </div>



            <!-- Sim Rental History -->
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">📋 Lịch sử thuê Sim</h5>
                        <p class="mb-0 text-muted">Danh sách các sim đã thuê và trạng thái hiện tại</p>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ti ti-filter me-1"></i>
                            Lọc
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all">Tất cả</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="active">Đang hoạt động</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="completed">Hoàn thành</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="expired">Hết hạn</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="failed">Thất bại</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="simHistoryTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Số điện thoại</th>
                                    <th>Quốc gia</th>
                                    <th>Dịch vụ</th>
                                    <th>Server</th>
                                    <th>Giá</th>
                                    <th>Thời gian thuê</th>
                                    <th>Hết hạn</th>
                                    <th>Trạng thái</th>
                                    <th>Mã OTP</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Row 1 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM001</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+84 987 654 321</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/vn.png" alt="VN" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Việt Nam</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-telegram text-primary me-2"></i>
                                            <span>Telegram</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">15,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 14:30</small></td>
                                    <td><small class="text-muted">2024-01-15 14:40</small></td>
                                    <td><span class="badge bg-success">Hoàn thành</span></td>
                                    <td><span class="badge bg-label-info">123456</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 2 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM002</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">****** 123 4567</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/us.png" alt="US" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Hoa Kỳ</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-whatsapp text-success me-2"></i>
                                            <span>WhatsApp</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-info">Server 2</span></td>
                                    <td><span class="text-success fw-medium">25,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 15:15</small></td>
                                    <td><small class="text-muted">2024-01-15 15:25</small></td>
                                    <td><span class="badge bg-warning">Đang chờ</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 3 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM003</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+44 20 7946 0958</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/gb.png" alt="UK" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Anh</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-facebook text-info me-2"></i>
                                            <span>Facebook</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-warning">Server 3</span></td>
                                    <td><span class="text-success fw-medium">35,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 13:45</small></td>
                                    <td><small class="text-muted">2024-01-15 13:55</small></td>
                                    <td><span class="badge bg-danger">Hết hạn</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thuê lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 4 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM004</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+81 3 1234 5678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/jp.png" alt="JP" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Nhật Bản</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-twitter text-info me-2"></i>
                                            <span>Twitter</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">20,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 16:20</small></td>
                                    <td><small class="text-muted">2024-01-15 16:30</small></td>
                                    <td><span class="badge bg-primary">Đang hoạt động</span></td>
                                    <td><span class="text-muted">Đang chờ...</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li><a class="dropdown-item text-danger" href="#"><i
                                                            class="ti ti-trash me-2"></i>Hủy sim</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 5 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM005</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+49 30 12345678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/de.png" alt="DE" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Đức</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-instagram text-danger me-2"></i>
                                            <span>Instagram</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-danger">Server 4</span></td>
                                    <td><span class="text-success fw-medium">45,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 12:10</small></td>
                                    <td><small class="text-muted">2024-01-15 12:20</small></td>
                                    <td><span class="badge bg-danger">Thất bại</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thử lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            Hiển thị 1-5 trong tổng số 23 kết quả
                        </div>
                        <nav aria-label="Sim history pagination">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">
                                        <i class="ti ti-chevron-left"></i>
                                    </a>
                                </li>
                                <li class="page-item active">
                                    <a class="page-link" href="#">1</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">
                                        <i class="ti ti-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
