@extends('layouts/layoutMaster')

@section('title', __('sim_rental.page_title'))

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss'])
@endsection

@section('page-style')
    @vite(['resources/assets/vendor/scss/pages/cards-advance.scss', 'resources/assets/scss/pages/sim-rental.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection

@section('page-script')
    @vite(['resources/assets/js/card/cards-advance.js', 'resources/assets/js/sim-rental.js'])
    <script>
        // Pass translations to JavaScript
        window.simRentalTranslations = {
            chooseCountry: '{{ __('sim_rental.choose_country') }}',
            chooseService: '{{ __('sim_rental.choose_service') }}',
            pleaseChooseCountry: '{{ __('sim_rental.please_choose_country') }}',
            pleaseChooseService: '{{ __('sim_rental.please_choose_service') }}',
            renting: '{{ __('sim_rental.renting') }}',
            rentSimNow: '{{ __('sim_rental.rent_sim_now') }}',
            rentalSuccess: '{{ __('sim_rental.rental_success') }}',
            rentalError: '{{ __('sim_rental.rental_error') }}',
            error: '{{ __('sim_rental.error') }}',
            waitingOtp: '{{ __('sim_rental.waiting_otp') }}',
            available: '{{ __('sim_rental.available') }}',
            outOfStock: '{{ __('sim_rental.out_of_stock') }}',
            currency: '{{ __('sim_rental.currency') }}',
            viewDetails: '{{ __('sim_rental.view_details') }}',
            copyNumber: '{{ __('sim_rental.copy_number') }}',
            refresh: '{{ __('sim_rental.refresh') }}',
            cancelSim: '{{ __('sim_rental.cancel_sim') }}',
            active: '{{ __('sim_rental.active') }}',
            completed: '{{ __('sim_rental.completed') }}',
            expired: '{{ __('sim_rental.expired') }}',
            failed: '{{ __('sim_rental.failed') }}'
        };
    </script>


@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">🔢 {{ __('sim_rental.page_title') }}</h4>
                            <p class="mb-0 text-muted">{{ __('sim_rental.page_subtitle') }}</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="badge bg-label-success fs-6 px-3 py-2">
                                <i class="ti ti-check me-1"></i>
                                {{ __('sim_rental.system_status') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Selection Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3">{{ __('sim_rental.choose_server') }}</h5>
                    <div class="server-tabs">
                        @foreach($servers as $index => $server)
                            <button class="server-tab {{ $index === 0 ? 'active' : '' }}" data-server="{{ $server->id }}">
                                <i class="ti ti-server me-2"></i>
                                {{ $server->name }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="countrySelect" class="form-label fw-medium">{{ __('sim_rental.country') }}</label>
                        <select id="countrySelect" class="select2 form-select">
                            <option value="">{{ __('sim_rental.choose_country') }}</option>
                            {{-- Countries will be loaded dynamically based on selected server --}}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="serviceSelect" class="form-label fw-medium">{{ __('sim_rental.service') }}</label>
                        <select id="serviceSelect" class="select2 form-select">
                            <option value="">{{ __('sim_rental.choose_service') }}</option>
                            {{-- Services will be loaded dynamically based on selected country --}}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button id="rentSimBtn" class="btn btn-primary w-100 d-flex align-items-center justify-content-center" style="height: 42px;">
                            <i class="ti ti-plus me-2"></i>
                            {{ __('sim_rental.rent_sim_now') }}
                        </button>
                    </div>
                </div>
            </div>



            <!-- Sim Rental History -->
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">📋 {{ __('sim_rental.rental_history') }}</h5>
                        <p class="mb-0 text-muted">{{ __('sim_rental.rental_history_subtitle') }}</p>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ti ti-filter me-1"></i>
                            {{ __('sim_rental.filter') }}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all">{{ __('sim_rental.all') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="active">{{ __('sim_rental.active') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="completed">{{ __('sim_rental.completed') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="expired">{{ __('sim_rental.expired') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="failed">{{ __('sim_rental.failed') }}</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="simHistoryTable">
                            <thead>
                                <tr>
                                    <th>{{ __('sim_rental.id') }}</th>
                                    <th>{{ __('sim_rental.phone_number') }}</th>
                                    <th>{{ __('sim_rental.country_name') }}</th>
                                    <th>{{ __('sim_rental.service_name') }}</th>
                                    <th>{{ __('sim_rental.server') }}</th>
                                    <th>{{ __('sim_rental.price') }}</th>
                                    <th>{{ __('sim_rental.rental_time') }}</th>
                                    <th>{{ __('sim_rental.expires_at') }}</th>
                                    <th>{{ __('sim_rental.status') }}</th>
                                    <th>{{ __('sim_rental.otp_code') }}</th>
                                    <th>{{ __('sim_rental.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Row 1 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM001</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+84 987 654 321</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/vn.png" alt="VN" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>{{ __('sim_rental.vietnam') }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-telegram text-primary me-2"></i>
                                            <span>{{ __('sim_rental.telegram') }}</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">15,000 {{ __('sim_rental.currency') }}</span></td>
                                    <td><small class="text-muted">2024-01-15 14:30</small></td>
                                    <td><small class="text-muted">2024-01-15 14:40</small></td>
                                    <td><span class="badge bg-success">{{ __('sim_rental.completed') }}</span></td>
                                    <td><span class="badge bg-label-info">123456</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 2 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM002</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">****** 123 4567</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/us.png" alt="US" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Hoa Kỳ</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-whatsapp text-success me-2"></i>
                                            <span>WhatsApp</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-info">Server 2</span></td>
                                    <td><span class="text-success fw-medium">25,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 15:15</small></td>
                                    <td><small class="text-muted">2024-01-15 15:25</small></td>
                                    <td><span class="badge bg-warning">Đang chờ</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 3 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM003</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+44 20 7946 0958</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/gb.png" alt="UK" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Anh</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-facebook text-info me-2"></i>
                                            <span>Facebook</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-warning">Server 3</span></td>
                                    <td><span class="text-success fw-medium">35,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 13:45</small></td>
                                    <td><small class="text-muted">2024-01-15 13:55</small></td>
                                    <td><span class="badge bg-danger">Hết hạn</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thuê lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 4 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM004</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+81 3 1234 5678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/jp.png" alt="JP" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Nhật Bản</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-twitter text-info me-2"></i>
                                            <span>Twitter</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">20,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 16:20</small></td>
                                    <td><small class="text-muted">2024-01-15 16:30</small></td>
                                    <td><span class="badge bg-primary">Đang hoạt động</span></td>
                                    <td><span class="text-muted">Đang chờ...</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li><a class="dropdown-item text-danger" href="#"><i
                                                            class="ti ti-trash me-2"></i>Hủy sim</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 5 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM005</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+49 30 12345678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/de.png" alt="DE" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Đức</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-instagram text-danger me-2"></i>
                                            <span>Instagram</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-danger">Server 4</span></td>
                                    <td><span class="text-success fw-medium">45,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 12:10</small></td>
                                    <td><small class="text-muted">2024-01-15 12:20</small></td>
                                    <td><span class="badge bg-danger">Thất bại</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thử lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            Hiển thị 1-5 trong tổng số 23 kết quả
                        </div>
                        <nav aria-label="Sim history pagination">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">
                                        <i class="ti ti-chevron-left"></i>
                                    </a>
                                </li>
                                <li class="page-item active">
                                    <a class="page-link" href="#">1</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">
                                        <i class="ti ti-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
