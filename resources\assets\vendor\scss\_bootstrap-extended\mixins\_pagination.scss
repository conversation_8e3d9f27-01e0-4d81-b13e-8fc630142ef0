// Pagination
// *******************************************************************************

// Basic Pagination
@mixin template-pagination-variant($parent, $background, $color: null) {
  $hover-background: if(
    $dark-style,
    shade-color($background, $pagination-hover-bg-scale, $rgba-to-hex-bg),
    tint-color($background, $pagination-hover-bg-scale, $rgba-to-hex-bg)
  );
  #{$parent} .page-item .page-link,
  #{$parent}.pagination li > a:not(.page-link) {
    &:hover,
    &:focus {
      background-color: $hover-background;
      color: $background;
    }
  }
  #{$parent} .page-item.active .page-link,
  #{$parent}.pagination li.active > a:not(.page-link) {
    box-shadow: 0 0.125rem 0.375rem 0 rgba($background, 0.3);
    &,
    &:hover,
    &:focus {
      border-color: $background;
      background-color: $background;
      color: if($color, $color, color-contrast($background));
    }
  }
  #{$parent} .page-item .page-link,
  #{$parent}.pagination li > a:not(.page-link) {
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($black, 0) 70%
        );
      }
    }
  }
}

// Pagination Outline Variant
@mixin template-pagination-outline-variant($parent, $background, $color: null) {
  #{$parent} .page-item.active .page-link,
  #{$parent}.pagination li.active > a:not(.page-link) {
    &,
    &:hover,
    &:focus {
      border-color: $background !important;
      color: $background !important;
      background-color: rgba-to-hex(rgba($background, 0.16), $card-bg) !important;
    }
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($black, 0) 70%
        );
      }
    }
  }
}

@mixin template-pagination-theme($background, $color: null) {
  @include template-pagination-variant('', $background, $color);
}
