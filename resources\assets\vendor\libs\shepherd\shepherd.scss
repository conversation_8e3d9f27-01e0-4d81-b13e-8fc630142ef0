@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@charset "UTF-8";
@import '../../scss/_custom-variables/libs';
@import 'shepherd.js/dist/css/shepherd';
@import './mixins';

$shepherd-header-content-padding-x: 1.25rem !default;
$shepherd-header-content-padding-y: 1.25rem !default;
$shepherd-btn-padding-x: 1.25rem !default;
$shepherd-container-width: 15rem !default;

.shepherd-element {
  .shepherd-arrow:before {
    border-right: 1px solid;
    border-bottom: 1px solid;
  }
  .shepherd-content {
    min-width: $shepherd-container-width;
    border-radius: light.$border-radius !important;
    .shepherd-header {
      padding: $shepherd-header-content-padding-y $shepherd-header-content-padding-x 0;
      .shepherd-title {
        font-weight: light.$font-weight-medium !important;
        font-size: light.$h5-font-size !important;
      }

      .shepherd-cancel-icon {
        font-size: 1.85rem !important;
        &:focus {
          outline: 0;
        }
      }
    }
    .shepherd-text {
      font-size: light.$font-size-base !important;
      padding: 1rem $shepherd-header-content-padding-x !important;
    }

    .shepherd-footer {
      .shepherd-button {
        &:not(:last-child) {
          margin-right: 0.75rem !important;
        }
      }
      padding: 0 $shepherd-header-content-padding-x $shepherd-header-content-padding-y !important;
    }
  }
  // Ask before submit
  &[data-popper-placement='bottom'] {
    margin-top: 0.8rem !important;
  }
  &[data-popper-placement='top'] {
    margin-top: -0.8rem !important;
  }
  &[data-popper-placement='left'] {
    margin-left: -0.8rem !important;
    .shepherd-arrow:before {
      border-bottom: 0;
      border-top: 1px solid;
    }
  }
  &[data-popper-placement='right'] {
    margin-left: 0.8rem !important;
    .shepherd-arrow:before {
      border-right: 0;
      border-left: 1px solid;
    }
  }
}

// Light style
@if $enable-light-style {
  .shepherd-element {
    box-shadow: light.$box-shadow;
    background-color: light.$card-bg !important;
    .shepherd-content {
      .shepherd-header {
        background: light.$card-bg !important;
        .shepherd-title {
          color: light.$headings-color !important;
        }
        .shepherd-cancel-icon {
          color: light.$text-muted !important;
        }
      }
      .shepherd-text {
        color: light.$body-color !important;
      }
    }
    .shepherd-arrow:before {
      background-color: light.$card-bg !important;
      border-color: light.$card-bg !important;
    }
  }
}

// Dark Style
@if $enable-dark-style {
  .dark-style {
    .shepherd-element {
      box-shadow: dark.$box-shadow;
      background: dark.$card-bg !important;
      .shepherd-content {
        .shepherd-header {
          background: dark.$card-bg !important;
          .shepherd-title {
            color: dark.$headings-color !important;
          }
          .shepherd-cancel-icon {
            color: dark.$text-muted !important;
          }
        }
        .shepherd-text {
          color: dark.$body-color !important;
        }
      }
    }
    .shepherd-arrow:before {
      background-color: dark.$card-bg !important;
      border-color: dark.$card-bg !important;
    }
  }
}

// RTL
@if $enable-rtl-support {
  [dir='rtl'] {
    .shepherd-element {
      .btn-next {
        margin-right: 0.75rem;
      }
      &[data-popper-placement='left'] {
        margin-left: -0.8rem !important;
      }
    }
  }
}

@include light.media-breakpoint-down(sm) {
  .shepherd-element {
    max-width: 300px !important;
  }
}
