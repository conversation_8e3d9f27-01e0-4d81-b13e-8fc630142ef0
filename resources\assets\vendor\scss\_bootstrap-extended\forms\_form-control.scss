// Form control
// *******************************************************************************

//? Form control (all size) padding calc due to border increase on focus
.form-control {
  &::placeholder,
  &:focus::placeholder {
    transition: all 0.2s ease;
  }
  padding: calc($input-padding-y - $input-border-width) calc($input-padding-x - $input-border-width);
  // border color on hover state
  &:hover {
    &:not([disabled]):not([focus]) {
      border-color: $input-border-hover-color;
    }
  }
  // ! FIX: wizard-ex input type number placeholder align issue
  &[type='number'] {
    .input-group & {
      line-height: 1.375rem;
      min-height: 2.375rem;
    }
    .input-group-lg & {
      line-height: 1.5rem;
      min-height: 3rem;
    }
    .input-group-sm & {
      min-height: 1.875rem;
    }
  }

  &:focus {
    border-width: $input-focus-border-width;
    padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-focus-border-width);
  }
  &.form-control-lg {
    padding: calc($input-padding-y-lg - $input-border-width) calc($input-padding-x-lg - $input-border-width);
    &:focus {
      padding: calc($input-padding-y-lg - $input-focus-border-width)
        calc($input-padding-x-lg - $input-focus-border-width);
    }
  }
  &.form-control-sm {
    padding: calc($input-padding-y-sm - $input-border-width) calc($input-padding-x-sm - $input-border-width);
    &:focus {
      padding: calc($input-padding-y-sm - $input-focus-border-width)
        calc($input-padding-x-sm - $input-focus-border-width);
    }
  }
}
.input-group:has(button) .form-control {
  padding: calc($input-padding-y - $input-border-width) calc($input-padding-x - $input-border-width) !important;
  border-width: $input-border-width !important;
}
// ltr only
@include ltr-only {
  .form-control:not([readonly]) {
    &:focus::placeholder {
      transform: translateX(4px);
    }
  }
}
// rtl only
@include rtl-only {
  input[type='tel'] {
    text-align: right;
  }
  .form-control:not([readonly]) {
    &:focus::placeholder {
      transform: translateX(-4px);
    }
  }
}
