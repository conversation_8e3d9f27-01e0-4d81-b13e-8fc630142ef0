/**
 * Sim Rental functionality
 */

'use strict';

import $ from 'jquery';
window.$ = window.jQuery = $;

$(document).ready(function() {
    // Wait for cards-advance.js to initialize Select2, then override
    var firstServerId = $('.server-tab.active').data('server');
    if (firstServerId) {
        loadServerData(firstServerId);
    }

    // Server tab switching
    $('.server-tab').click(function() {
        $('.server-tab').removeClass('active');
        $(this).addClass('active');

        var serverId = $(this).data('server');
        loadServerData(serverId);
    });

    // Load server data
    function loadServerData(serverId) {
        // Load countries for this server
        $.ajax({
            url: `/api/sim-rental/countries/${serverId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateCountrySelect(response.data);
                }
            },
            error: function(xhr) {
                console.error('Error loading countries:', xhr);
            }
        });

        // Clear service select
        $('#serviceSelect').empty().append('<option value="">Chọn dịch vụ...</option>');
    }
    // Update country select options
    function updateCountrySelect(countries) {
        var $countrySelect = $('#countrySelect');
        $countrySelect.empty().append('<option value="">Chọn quốc gia...</option>');

        countries.forEach(function(country) {
            $countrySelect.append(`<option value="${country.id}">${country.name}</option>`);
        });

        // Trigger Select2 update
        $countrySelect.trigger('change');
    }

    // Load services when country changes
    $('#countrySelect').on('change', function() {
        var countryId = $(this).val();
        var serverId = $('.server-tab.active').data('server');

        if (countryId && serverId) {
            $.ajax({
                url: `/api/sim-rental/services/${serverId}/${countryId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        updateServiceSelect(response.data);
                    }
                },
                error: function(xhr) {
                    console.error('Error loading services:', xhr);
                }
            });
        } else {
            $('#serviceSelect').empty().append('<option value="">Chọn dịch vụ...</option>');
        }
    });

    // Update service select options
    function updateServiceSelect(services) {
        var $serviceSelect = $('#serviceSelect');
        $serviceSelect.empty().append('<option value="">Chọn dịch vụ...</option>');

        services.forEach(function(service) {
            var stockText = service.stock > 0 ? ` (${service.stock} có sẵn)` : ' (Hết hàng)';
            var disabled = service.stock <= 0 ? 'disabled' : '';
            $serviceSelect.append(`<option value="${service.id}" ${disabled}>${service.name} - ${service.price.toLocaleString()}đ${stockText}</option>`);
        });

        // Trigger Select2 update
        $serviceSelect.trigger('change');
    }

    // Filter functionality
    $('#countrySelect, #serviceSelect').change(function() {
        filterSimCards();
    });

    function filterSimCards() {
        var country = $('#countrySelect').val();
        var service = $('#serviceSelect').val();

        console.log('Filtering by country:', country, 'service:', service);
        // Here you would implement the filtering logic
    }

    // Rent Sim button functionality
    $('#rentSimBtn').click(function() {
        var countryId = $('#countrySelect').val();
        var serviceId = $('#serviceSelect').val();
        var serverId = $('.server-tab.active').data('server');

        if (!countryId) {
            alert('Vui lòng chọn quốc gia!');
            $('#countrySelect').focus();
            return;
        }

        if (!serviceId) {
            alert('Vui lòng chọn dịch vụ!');
            $('#serviceSelect').focus();
            return;
        }

        // Disable button and show loading
        $(this).prop('disabled', true).html(
            '<i class="spinner-border spinner-border-sm me-2"></i>Đang thuê...');

        // Make API call to rent sim
        $.ajax({
            url: '/api/sim-rental/rent',
            method: 'POST',
            data: {
                server_id: serverId,
                country_id: countryId,
                service_id: serviceId,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(`Thuê sim thành công!\nSố điện thoại: ${response.data.phone_number}\nID: ${response.data.sim_id}\nGiá: ${response.data.price.toLocaleString()}đ`);

                    // Add new row to history table
                    addSimToHistory(response.data);

                    // Reset selects
                    $('#countrySelect, #serviceSelect').val('').trigger('change');

                    // Reload current server data to update stock
                    loadServerData(serverId);
                } else {
                    alert('Lỗi: ' + response.message);
                }
            },
            error: function(xhr) {
                var errorMessage = 'Có lỗi xảy ra khi thuê sim!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Lỗi: ' + errorMessage);
            },
            complete: function() {
                // Reset button
                $('#rentSimBtn').prop('disabled', false).html(
                    '<i class="ti ti-plus me-2"></i>Thuê Sim Ngay');
            }
        });
    });

    // Add sim to history table
    function addSimToHistory(simData) {
        var now = new Date();
        var timeStr = now.toLocaleString('vi-VN');

        var newRow = `
      <tr>
        <td><span class="badge bg-label-primary">#${simData.sim_id}</span></td>
        <td>
          <div class="d-flex align-items-center">
            <i class="ti ti-phone me-2 text-primary"></i>
            <span class="fw-medium">${simData.phone_number}</span>
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <span>${simData.country_name}</span>
          </div>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <span>${simData.service_name}</span>
          </div>
        </td>
        <td><span class="text-success fw-medium">${simData.price.toLocaleString()} VNĐ</span></td>
        <td><small class="text-muted">${timeStr}</small></td>
        <td><small class="text-muted">${simData.expires_at}</small></td>
        <td><span class="badge bg-primary">${simData.status}</span></td>
        <td><span class="text-muted">Đang chờ...</span></td>
        <td>
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
              <i class="ti ti-dots-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#"><i class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
              <li><a class="dropdown-item" href="#"><i class="ti ti-copy me-2"></i>Copy số</a></li>
              <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i>Làm mới</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item text-danger" href="#"><i class="ti ti-trash me-2"></i>Hủy sim</a></li>
            </ul>
          </div>
        </td>
      </tr>
    `;

        $('#simHistoryTable tbody').prepend(newRow);
    }

    // History table filter functionality
    $('[data-filter]').click(function(e) {
        e.preventDefault();
        var filter = $(this).data('filter');

        if (filter === 'all') {
            $('#simHistoryTable tbody tr').show();
        } else {
            $('#simHistoryTable tbody tr').hide();

            var statusMap = {
                'active': 'Đang hoạt động',
                'completed': 'Hoàn thành',
                'expired': 'Hết hạn',
                'failed': 'Thất bại'
            };

            $('#simHistoryTable tbody tr').each(function() {
                var status = $(this).find('td:nth-child(9) .badge').text().trim();
                if (status === statusMap[filter]) {
                    $(this).show();
                }
            });
        }
    });
});
