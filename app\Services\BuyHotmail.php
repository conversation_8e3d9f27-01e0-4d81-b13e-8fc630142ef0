<?php

namespace App\Services;

use App\Models\User;
use App\Models\TransactionLog;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Http;

class BuyHotmail
{
    /**
     * Trừ tiền từ tài khoản người dùng, có lock để tránh race condition
     */
    public static function BuyHotmail($quantity, $type, &$out)
    {
        // $out = json_decode('[{"Email":"<EMAIL>","Password":"cdz3dVhm1Lf","RefreshToken":"M.C518_SN1.0.U.-CnOuD6SjMm7vbjmnrjAdvkdRK*Xyky8IwQHGsA6hxtUPxc2HYpRWOMRToTD0K3sueAFMMpgYT9Mp!gpo8oCgDOGnDB57*5c6I18aGToesHU9jXD71Hi7uNG5Fn6nChWzYFraBR9a*sm3yJnfmxCVX0niTpWWp42ANpaXCjAqbR1JIj5gSXfaPVyn1CUABntN2dKV7!PZxnHx!JA!SDXQfLVpo4d3pt7gttmgd1ZjSyf0xFTvAejUpjK1ni8ZFDbchuqqgi4bhirkmZlfyUzZGdSWCuvZpFyH2wgm1zCI8AL6swgag1baQUt8BlVTRuQgSmahdApQmcT!Q5wRPItPeNRe28WocE9J*udjfHTSDMIbJPvIxcHkfsSUZkj4NIhVNg$$","AccessToken":null,"R_Expire":"9e5f94bc-e8a4-4e73-b8be-63364c29d753","ClientId":"9e5f94bc-e8a4-4e73-b8be-63364c29d753"},{"Email":"<EMAIL>","Password":"BF9k51pjnC","RefreshToken":"M.C550_SN1.0.U.-Cngg!4pRJELhK9c!u!VmkXmKRtwr4MEeNxqcSka4qxqoczr6GfRzJQBLo9n18tG!ACWZ0P!vD1su2D2Jd8xrkr1Dg*AJklgmKctgoyaB2qvFKPgvXvqLdqeh4!AWQAhTHprF0rnmHNHMk2ZAdRfl8mjIcJ5x7zArByFGH3YLhMrZ9dprNc0wkvRZtQq2Y22su1*GpbZF3wS7z017Q4PLWiHRKAGp0GlLcHdRm9nLrHD9zIh7mkpiRFJtPT83gYJgdr005FI!uZII9UTL8WrFE*ASGxuy37iPM0BqeL4LCaJykO9RLeB!z9br2uoH68ReZc4f30b2hgNT6RaaJCCEP3VtHdl3k1zLpkPswP61FwGplj*ca7WI5DrVFX!xFZ0t!Q$$","AccessToken":null,"R_Expire":"9e5f94bc-e8a4-4e73-b8be-63364c29d753","ClientId":"9e5f94bc-e8a4-4e73-b8be-63364c29d753"}]');
        // return true;
        try {
            $response = Http::timeout(15)->get('https://api.zeus-x.ru/purchase', [
                'apikey' => 'e886fbefc9394a61a74bd4ee3854afae',
                'accountcode' => $type,
                'quantity' => $quantity
            ]);

            if (!$response->successful()) {
                return false;
            }
            $data = $response->json();
            if ($data['Code'] == 0) {
                $out = $data['Data']['Accounts'];
                return true;
            } else {
                if (str_contains($data['Message'], 'INSUFFICIENT QUANTITY')) {
                    $out = __('messages.not_enough_qnt');
                } elseif (str_contains($data['Message'], 'ACCOUNT BALANCE')) {
                    $out = __('messages.call_admin');
                } else {
                    $out = __('messages.unknow_error');
                }
                return false;
            }
        } catch (Exception $e) {
            return false;
        }
    }
}
