<?php

namespace App\Http\Controllers\software;

use App\Http\Controllers\Controller;
use App\Models\Software;
use Illuminate\Http\Request;

class SoftwareShow extends Controller
{
    public function index($url)
    {
        $software = Software::with('tags')->where('url', $url)->first();
        if (!$software)
            return redirect()->route('software-all');
        return view('content.software.software-show',compact('software'));
    }
}
