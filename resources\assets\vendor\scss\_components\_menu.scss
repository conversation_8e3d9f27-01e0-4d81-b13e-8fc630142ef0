// Menu
// *******************************************************************************

.menu {
  display: flex;
  .app-brand {
    width: 100%;
    transition: padding 0.3s ease-in-out;
  }

  //PS Scrollbar
  .ps__thumb-y,
  .ps__rail-y {
    width: 0.125rem !important;
  }

  .ps__rail-y {
    right: 0.25rem !important;
    left: auto !important;
    background: none !important;

    @include rtl-style {
      right: auto !important;
      left: 0.25rem !important;
    }
  }

  .ps__rail-y:hover,
  .ps__rail-y:focus,
  .ps__rail-y.ps--clicking,
  .ps__rail-y:hover > .ps__thumb-y,
  .ps__rail-y:focus > .ps__thumb-y,
  .ps__rail-y.ps--clicking > .ps__thumb-y {
    width: 0.375rem !important;
  }
}

.menu-inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  height: 100%;
}
.menu-inner-shadow {
  display: none;
  position: absolute;
  top: $navbar-height - (($navbar-height - 3rem) * 0.5);
  @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    height: 3rem;
  }
  @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    height: 1.5rem;
  }
  width: 100%;
  pointer-events: none;
  z-index: 2;
  // Hide menu inner shadow in static layout
  html:not(.layout-menu-fixed) & {
    display: none !important;
  }
}

// Menu item

.menu-item {
  align-items: flex-start;
  justify-content: flex-start;

  &.menu-item-animating {
    transition: height $menu-animation-duration ease-in-out;
  }
}

.menu-item,
.menu-header,
.menu-divider,
.menu-block {
  flex: 0 0 auto;
  flex-direction: column;
  margin: 0;
  padding: 0;
  list-style: none;
}
.menu-header {
  opacity: 1;
  transition: opacity $menu-animation-duration ease-in-out;
  .menu-header-text {
    text-transform: uppercase;
    letter-spacing: 0.4px;
    white-space: nowrap;
    color: $text-muted;
  }
}

// Menu Icon
.menu-icon {
  flex-grow: 0;
  flex-shrink: 0;
  margin-right: $menu-icon-expanded-spacer;
  line-height: 1;
  font-size: $menu-icon-expanded-font-size;
  .menu:not(.menu-no-animation) & {
    transition: margin-right $menu-animation-duration ease;
  }

  @include rtl-style {
    margin-right: 0;
    margin-left: $menu-icon-expanded-spacer;
    .menu:not(.menu-no-animation) & {
      transition: margin-left $menu-animation-duration ease;
    }
  }
}

// Menu link
.menu-link {
  position: relative;
  display: flex;
  align-items: center;
  flex: 0 1 auto;
  margin: 0;

  .menu-item.disabled & {
    cursor: not-allowed !important;
  }
  // link hover animation
  .menu:not(.menu-no-animation) & {
    transition-duration: $menu-animation-duration;
    transition-property: color, background-color;
  }

  > :not(.menu-icon) {
    flex: 0 1 auto;
    opacity: 1;
    .menu:not(.menu-no-animation) & {
      transition: opacity $menu-animation-duration ease-in-out;
    }
  }
}

// Sub menu
.menu-sub {
  display: none;
  flex-direction: column;
  margin: 0;
  padding: 0;

  .menu:not(.menu-no-animation) & {
    transition: background-color $menu-animation-duration;
  }

  .menu-item.open > & {
    display: flex;
  }
}

// Menu toggle open/close arrow
.menu-toggle::after {
  position: absolute;
  top: 50%;
  display: block;
  font-family: 'tabler-icons';
  font-size: $menu-icon-expanded-font-size;
  transform: translateY(-50%);

  @include ltr-style {
    content: '\ea61';
  }
  @include rtl-style {
    content: '\ea60';
  }

  .menu:not(.menu-no-animation) & {
    transition-duration: $menu-animation-duration;
    transition-property: -webkit-transform, transform;
  }
}

// Menu divider
.menu-divider {
  width: 100%;
  border: 0;
  border-top: 1px solid;
}

// Vertical Menu
// *******************************************************************************

.menu-vertical {
  overflow: hidden;
  flex-direction: column;

  // menu expand collapse animation
  &:not(.menu-no-animation) {
    transition: width $menu-animation-duration;
  }

  &,
  .menu-block,
  .menu-inner > .menu-item,
  .menu-inner > .menu-header {
    width: $menu-width;
  }

  .menu-inner {
    flex-direction: column;
    flex: 1 1 auto;

    > .menu-item {
      margin: $menu-item-spacer 0 0;
      // menu-link spacing
      .menu-link {
        margin: 0 $menu-vertical-link-margin-x;
        border-radius: $border-radius;
      }
    }
  }

  .menu-header {
    padding: $menu-vertical-header-margin-x calc($menu-vertical-link-margin-x * 2) 0.375rem;
  }
  .menu-item .menu-link,
  .menu-block {
    padding: $menu-vertical-menu-link-padding-y $menu-vertical-link-padding-x;
  }
  .menu-item .menu-link {
    font-size: $menu-font-size;
    min-height: 38px;
    > div:not(.badge) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.467;
    }
  }

  .menu-item .menu-toggle {
    padding-right: calc(#{$menu-vertical-link-padding-x} + #{$caret-width * 3.2});

    @include rtl-style {
      padding-right: $menu-vertical-link-padding-x;
      padding-left: calc(#{$menu-vertical-link-padding-x} + #{$caret-width * 3.2});
    }

    &::after {
      right: $menu-vertical-link-padding-x;

      @include rtl-style {
        right: auto;
        left: $menu-vertical-link-padding-x;
      }
    }
  }

  .menu-item.open:not(.menu-item-closing) > .menu-link:after {
    transform: translateY(-50%) rotate(90deg);

    @include rtl-style {
      transform: translateY(-50%) rotate(-90deg);
    }
  }

  .menu-divider {
    margin-top: $menu-link-spacer-x;
    margin-bottom: $menu-link-spacer-x;
    padding: 0;
  }

  .menu-sub {
    .menu-link {
      padding-top: $menu-vertical-menu-link-padding-y;
      padding-bottom: $menu-vertical-menu-link-padding-y;
    }
    .menu-item {
      margin: $menu-item-spacer 0 0;
    }
  }

  .menu-icon {
    width: $menu-icon-expanded-width;
  }

  .menu-sub .menu-icon {
    margin-right: 0;

    @include rtl-style {
      margin-left: 0;
    }
  }

  .menu-horizontal-wrapper {
    flex: none;
  }

  // Levels
  //

  $menu-first-level-spacer: $menu-vertical-link-padding-x + $menu-icon-expanded-width + $menu-icon-expanded-spacer;

  .menu-sub .menu-link {
    padding-left: $menu-first-level-spacer;

    @include rtl-style {
      padding-right: $menu-first-level-spacer;
      padding-left: $menu-vertical-link-padding-x;
    }
  }
  // Menu levels loop for padding left/right
  @for $i from 2 through $menu-max-levels {
    $selector: '';

    @for $l from 1 through $i {
      $selector: '#{$selector} .menu-sub';
    }
    .layout-wrapper:not(.layout-horizontal) & {
      .menu-inner > .menu-item {
        #{$selector} .menu-link {
          padding-left: $menu-first-level-spacer + ($menu-vertical-menu-level-spacer * ($i)) - 0.225;
          &::before {
            left: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - 1.5;
            @include rtl-style {
              right: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - 1.5;
              left: inherit;
            }
          }
          @include rtl-style {
            padding-right: $menu-first-level-spacer + ($menu-vertical-menu-level-spacer * ($i)) - 0.225;
            padding-left: $menu-vertical-link-padding-x;
          }
        }
      }
    }
  }
}

// Vertical Menu Collapsed
// *******************************************************************************

@mixin layout-menu-collapsed() {
  width: $menu-collapsed-width;

  .menu-inner > .menu-item {
    width: $menu-collapsed-width;
  }

  .menu-inner > .menu-header,
  .menu-block {
    position: relative;
    margin-left: $menu-collapsed-width;
    padding-right: ($menu-vertical-link-padding-x * 2) - $menu-icon-expanded-spacer;
    padding-left: $menu-icon-expanded-spacer;
    width: $menu-width;
    .menu-header-text {
      overflow: hidden;
      opacity: 0;
    }

    &::before {
      content: '';
      position: absolute;
      left: -1 * ($menu-collapsed-width * 0.66);
      height: 1px;
      width: 1.375rem;
      background-color: $border-color;
      top: 50%;
    }
  }

  .app-brand {
    padding-left: $menu-vertical-link-padding-x + 0.38rem;
  }

  .menu-inner > .menu-item div:not(.menu-block) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    opacity: 0;
  }
  .menu-inner > .menu-item > .menu-sub,
  .menu-inner > .menu-item.open > .menu-sub {
    display: none;
  }
  .menu-inner > .menu-item > .menu-toggle::after {
    display: none;
  }

  &:not(.layout-menu-hover) {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-right: calc(#{$menu-vertical-link-padding-x} + #{$caret-width * 1.2});
    }
  }

  .menu-inner > .menu-item > .menu-link .menu-icon {
    text-align: center;
    margin-right: 0;
  }
}

@mixin layout-menu-collapsed-rtl() {
  .menu-block {
    width: $menu-collapsed-width !important;
  }
  .menu-inner > .menu-item > .menu-link {
    padding-left: $menu-vertical-link-padding-x;
  }

  .menu-inner > .menu-header,
  .menu-block {
    margin-right: $menu-collapsed-width;
    margin-left: 0;
    padding-right: $menu-icon-expanded-spacer;
    padding-left: ($menu-vertical-link-padding-x * 2) - $menu-icon-expanded-spacer;

    &::before {
      right: -1 * ($menu-collapsed-width * 0.66);
      left: auto;
    }
  }

  &:not(.layout-menu-hover) {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-inline: $menu-vertical-link-padding-x;
    }
  }

  .menu-inner > .menu-item > .menu-link .menu-icon {
    margin-left: 0;
  }
}
// Only for menu example
.menu-collapsed:not(:hover) {
  @include layout-menu-collapsed();

  @include rtl-style {
    @include layout-menu-collapsed-rtl();
  }
}

// Horizontal
// *******************************************************************************

.menu-horizontal {
  flex-direction: row;
  width: 100%;

  .menu-inner {
    overflow: hidden;
    flex-direction: row;
    flex: 0 1 100%;
    > .menu-item.active > .menu-link.menu-toggle {
      font-weight: $font-weight-medium;
    }
    .menu-item.active > .menu-link:not(.menu-toggle) {
      font-weight: $font-weight-medium;
    }
  }

  .menu-item .menu-link {
    padding: $menu-horizontal-link-padding-y $menu-horizontal-link-padding-x;
  }

  .menu-item .menu-toggle {
    padding-right: calc(#{$menu-horizontal-link-padding-x} + #{$caret-width * 3});

    @include rtl-style {
      padding-right: $menu-horizontal-link-padding-x;
      padding-left: calc(#{$menu-horizontal-link-padding-x} + #{$caret-width * 3});
    }

    &::after {
      right: calc(#{$menu-horizontal-link-padding-x} - #{0.2rem});

      @include rtl-style {
        right: auto;
        left: calc(#{$menu-horizontal-link-padding-x} - #{0.2rem});
      }
    }
  }

  .menu-inner > .menu-item > .menu-toggle {
    &::after {
      transform: translateY(-50%) rotate(90deg);

      @include rtl-style {
        transform: translateY(-50%) rotate(-90deg);
      }
    }
    &::before {
      position: absolute;
      block-size: $menu-vertical-header-margin-y;
      content: '';
      inline-size: 100%;
      inset-block-start: 100%;
      inset-inline-start: 0;
      z-index: 2;
      pointer-events: auto;
    }
  }
  .menu-inner > .menu-item > .menu-sub {
    margin-top: $menu-vertical-header-margin-y;
  }

  .menu-inner > .menu-item:not(.menu-item-closing).open .menu-item.open {
    position: relative;
  }

  .menu-header,
  .menu-divider {
    display: none !important;
  }

  .menu-sub {
    position: absolute;
    width: $menu-sub-width;
    padding: calc($menu-horizontal-item-spacer + $menu-item-spacer) 0;
    box-shadow: $box-shadow-lg;
    .menu-item {
      padding: 1px $menu-link-spacer-x;
      &.open .menu-link > div::after {
        position: absolute;
        content: '';
        z-index: 2;
        pointer-events: auto;
        width: 1.0625rem;
        height: 100%;
        right: -1.0625rem;
      }
    }

    .menu-sub {
      position: absolute;
      left: 100%;
      top: 0;
      width: 100%;

      @include rtl-style {
        left: -100%;
      }
    }

    .menu-link {
      padding-top: $menu-horizontal-menu-link-padding-y;
      padding-bottom: $menu-horizontal-menu-link-padding-y;
      border-radius: $border-radius;
    }
  }

  .menu-inner > .menu-item {
    .menu-sub {
      @include border-radius($border-radius);
    }
    > .menu-sub {
      .menu-sub {
        margin: 0 $menu-horizontal-spacer-x;
      }
    }
  }

  &:not(.menu-no-animation) .menu-inner .menu-item.open .menu-sub {
    animation: menuDropdownShow $menu-animation-duration ease-in-out;
  }

  // Sub menu link padding left
  .menu-sub .menu-link {
    padding-left: $menu-horizontal-menu-level-spacer;
    min-height: 2.375rem;

    @include rtl-style {
      padding-right: $menu-horizontal-menu-level-spacer;
      padding-left: $menu-horizontal-link-padding-x;
    }
  }
  @include media-breakpoint-down(lg) {
    & {
      display: none;
    }
  }
}

.menu-horizontal-wrapper {
  overflow: hidden;
  flex: 0 1 100%;
  width: 0;

  .menu:not(.menu-no-animation) & .menu-inner {
    transition: margin $menu-animation-duration;
  }
}

.menu-horizontal-prev,
.menu-horizontal-next {
  position: relative;
  display: block;
  flex: 0 0 auto;
  width: $menu-control-width;

  &::after {
    content: '\ea61';
    position: absolute;
    top: 50%;
    display: block;
    font-family: 'tabler-icons';
    font-size: $menu-icon-expanded-font-size;
    transform: translateY(-50%);
  }

  &.disabled {
    cursor: not-allowed !important;
  }
}

.menu-horizontal-prev::after {
  border-right: 0;
  transform: translate(0, -50%) rotate(180deg);

  @include rtl-style {
    transform: translate(0, -50%) rotate(360deg);
  }
}

.menu-horizontal-next::after {
  border-left: 0;
  transform: translate(50%, -50%) rotate(360deg);

  @include rtl-style {
    transform: translate(-50%, -50%) rotate(180deg);
  }
}

@include keyframes(menuDropdownShow) {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Menu light/dark color mode
// *******************************************************************************

.menu-light {
  color: $navbar-light-color;

  .menu-link,
  .menu-horizontal-prev,
  .menu-horizontal-next {
    color: $navbar-light-color;

    &:hover,
    &:focus {
      color: $navbar-light-hover-color;
    }

    &.active {
      color: $navbar-light-active-color;
    }
  }

  .menu-item.disabled .menu-link {
    color: $navbar-light-disabled-color !important;
  }

  .menu-item.open:not(.menu-item-closing) > .menu-toggle,
  .menu-item.active > .menu-link {
    color: $navbar-light-active-color;
  }

  .menu-item.active > .menu-link:not(.menu-toggle) {
    background: $menu-light-menu-bg;
  }

  .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-sub,
  .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
    color: $navbar-light-color;
  }

  .menu-text {
    color: $navbar-light-active-color;
  }

  .menu-header {
    color: $navbar-light-color;
  }

  hr,
  .menu-divider,
  .menu-inner > .menu-item.open > .menu-sub::before {
    border-color: $menu-light-border-color !important;
  }

  .menu-inner > .menu-header::before,
  .menu-block::before {
    background-color: $navbar-light-disabled-color;
  }

  .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before {
    background-color: $menu-light-border-color;
  }

  .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
    background-color: $navbar-light-active-color;
  }

  .ps__thumb-y {
    background: $navbar-light-color !important;
  }
}

.menu-dark {
  color: $navbar-dark-color;

  .menu-link,
  .menu-horizontal-prev,
  .menu-horizontal-next {
    color: $navbar-dark-color;

    &:hover,
    &:focus {
      color: $navbar-dark-hover-color;
    }

    &.active {
      color: $navbar-dark-active-color;
    }
  }

  .menu-item.disabled .menu-link {
    color: $navbar-dark-disabled-color !important;
  }

  .menu-item.open:not(.menu-item-closing) > .menu-toggle,
  .menu-item.active > .menu-link {
    color: $navbar-dark-active-color;
  }

  .menu-item.active > .menu-link:not(.menu-toggle) {
    background: $menu-dark-menu-bg;
  }

  .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-sub,
  .menu-inner > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
    color: $navbar-dark-color;
  }

  .menu-text {
    color: $navbar-dark-active-color;
  }

  .menu-header {
    color: $navbar-dark-color;
  }

  hr,
  .menu-divider,
  .menu-inner > .menu-item.open > .menu-sub::before {
    border-color: $menu-dark-border-color !important;
  }

  .menu-inner > .menu-header::before,
  .menu-block::before {
    background-color: $navbar-dark-disabled-color;
  }

  .menu-inner > .menu-item.open .menu-item.open > .menu-toggle::before {
    background-color: $menu-dark-border-color;
  }

  .menu-inner > .menu-item.open .menu-item.active > .menu-link::before {
    background-color: $navbar-dark-active-color;
  }

  .ps__thumb-y {
    background: $navbar-dark-color !important;
  }
}
