<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Middleware\CacheGuestPages;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;

class GuestCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'guest-cache {action : The action to perform (clear, clear-expired, stats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage guest page cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'clear':
                $this->clearAllCache();
                break;
            case 'clear-expired':
                $this->clearExpiredCache();
                break;
            case 'stats':
                $this->showStats();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info("Available actions: clear, clear-expired, stats");
                return 1;
        }

        return 0;
    }

    /**
     * Clear all cache files
     */
    private function clearAllCache()
    {
        $this->info('Clearing all guest cache files...');
        $cleared = CacheGuestPages::clearAllCache();
        $this->info("Cleared {$cleared} cache files.");
    }

    /**
     * Clear expired cache files
     */
    private function clearExpiredCache()
    {
        $this->info('Clearing expired guest cache files...');
        $cleared = CacheGuestPages::clearExpiredCache();
        $this->info("Cleared {$cleared} expired cache files.");
    }

    /**
     * Show cache statistics
     */
    private function showStats()
    {
        $cacheDir = public_path(Config::get('guest-cache.directory', 'cache/guest-pages'));
        
        if (!File::exists($cacheDir)) {
            $this->info('Cache directory does not exist.');
            return;
        }

        $files = File::files($cacheDir);
        $totalFiles = count($files);
        $totalSize = 0;
        $expiredFiles = 0;
        $cacheDuration = Config::get('guest-cache.duration', 10);
        $expiryThreshold = time() - ($cacheDuration * 60);

        foreach ($files as $file) {
            $totalSize += $file->getSize();
            if ($file->getMTime() < $expiryThreshold) {
                $expiredFiles++;
            }
        }

        $this->info('Guest Cache Statistics:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Files', $totalFiles],
                ['Total Size', $this->formatBytes($totalSize)],
                ['Expired Files', $expiredFiles],
                ['Valid Files', $totalFiles - $expiredFiles],
                ['Cache Duration', $cacheDuration . ' minutes'],
                ['Cache Directory', $cacheDir],
            ]
        );
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
