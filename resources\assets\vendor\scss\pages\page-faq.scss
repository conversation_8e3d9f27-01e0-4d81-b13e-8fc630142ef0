// * FAQ
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

.faq-header {
  .input-wrapper {
    position: relative;
    width: 100%;
    max-width: calc(100% - 45%);
  }

  @include light.media-breakpoint-down(sm) {
    .input-wrapper {
      max-width: calc(100% - 30%);
    }
  }
}

.faq-banner-img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: left;
}

@if $enable-light-style {
  .light-style {
    .bg-faq-section {
      background-color: light.$gray-50;
    }
    .faq-header {
      .input-wrapper {
        .input-group-text,
        .form-control {
          background-color: light.$card-bg;
        }
      }
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .bg-faq-section {
      background-color: dark.$gray-50;
    }
    .faq-header {
      .input-wrapper {
        .input-group-text,
        .form-control {
          background-color: dark.$card-bg;
        }
      }
    }
  }
}
