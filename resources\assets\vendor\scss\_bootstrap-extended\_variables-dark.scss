// Variables
//
// Variables should follow the `$component-state-property-size` formula for
// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.
//
// (C) Custom variables for extended components of bootstrap only

// ! _variable-dark.scss file overrides _variable.scss file.

// * Colors
// *******************************************************************************

// scss-docs-start gray-color-variables
$white: #fff !default;
$black: #2f3349 !default;

$base: #e1def5 !default;
$gray-25: rgba($base, 0.025) !default; // (C)
$gray-50: rgba($base, 0.06) !default; // (C)
$gray-75: rgba($base, 0.08) !default; // (C)
$gray-100: rgba($base, 0.1) !default;
$gray-200: rgba($base, 0.12) !default;
$gray-300: rgba($base, 0.3) !default;
$gray-400: rgba($base, 0.4) !default;
$gray-500: rgba($base, 0.5) !default;
$gray-600: rgba($base, 0.6) !default;
$gray-700: rgba($base, 0.7) !default;
$gray-800: rgba($base, 0.8) !default;
$gray-900: rgba($base, 0.9) !default;
// scss-docs-end gray-color-variables

// scss-docs-start gray-colors-map
$grays: (
  '25': $gray-25,
  '50': $gray-50
) !default;
// scss-docs-end gray-colors-map

// scss-docs-start color-variables
$blue: #007bff !default;
$indigo: #6610f2 !default;
$purple: #7367f0 !default;
$pink: #e83e8c !default;
$red: #ff4c51 !default;
$orange: #fd7e14 !default;
$yellow: #ff9f43 !default;
$green: #28c76f !default;
$teal: #20c997 !default;
$cyan: #00bad1 !default;
// scss-docs-end color-variables

// scss-docs-start theme-color-variables
$primary: $purple !default;
$secondary: #808390 !default;
$success: $green !default;
$info: $cyan !default;
$warning: $yellow !default;
$danger: $red !default;
$light: #44475b !default;
$dark: #d7d8de !default;
$gray: $gray-500 !default; // (C)
// scss-docs-end theme-color-variables

// scss-docs-start theme-colors-map
$theme-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark,
  'gray': $gray
) !default;
// scss-docs-end theme-colors-map

$color-scheme: 'dark' !default; // (C)

// * Body
// *******************************************************************************

$body-bg: #25293c !default;
$rgba-to-hex-bg: $black !default; // (C)
$body-color: rgba-to-hex($gray-700, $rgba-to-hex-bg) !default;
$rgba-to-hex-bg-inverted: rgb(160, 149, 149) !default; // (C)

// * Components
// *******************************************************************************

$alert-border-scale: -84% !default;
$alert-color-scale: 0% !default;

$border-color: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;

// scss-docs-start box-shadow-variables
$shadow-bg: #131120 !default; // (C)
$box-shadow: 0 0.1875rem 0.75rem 0 rgba($shadow-bg, 0.2) !default;
$box-shadow-xs: 0 0.0625rem 0.375rem 0 rgba($shadow-bg, 0.16) !default;
$box-shadow-sm: 0 0.125rem 0.5rem 0 rgba($shadow-bg, 0.18) !default;
$box-shadow-lg: 0 0.25rem 1.125rem 0 rgba($shadow-bg, 0.22) !default;
$box-shadow-xl: 0 0.3125rem 1.875rem 0 rgba($shadow-bg, 0.24) !default;
// scss-docs-end box-shadow-variables

$floating-component-border-color: rgba($white, 0.05) !default; // (C)
$floating-component-shadow: 0 0.31rem 1.25rem 0 rgba($black, 0.4) !default; // (C)

// * Typography
// *******************************************************************************

$text-muted: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;
$text-muted-hover: rgba-to-hex($white, $rgba-to-hex-bg) !default; // (C)

$text-light: rgba-to-hex($gray-500, $rgba-to-hex-bg) !default; // (C)
$text-lighter: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default; // (C)
$text-lightest: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)

$headings-color: rgba-to-hex($gray-900, $rgba-to-hex-bg) !default;

// * Cards
// *******************************************************************************

$card-bg: #2f3349 !default;
$card-subtitle-color: rgba-to-hex(rgba($base, 0.55), $rgba-to-hex-bg) !default;

// * Tables
// *******************************************************************************

$table-bg-scale: -84% !default;
$table-hover-bg-factor: 0.06 !default;
$table-hover-bg: rgba($base, $table-hover-bg-factor) !default;

$table-striped-bg-factor: 0.06 !default;
$table-striped-bg: rgba-to-hex(rgba($base, $table-striped-bg-factor), $rgba-to-hex-bg) !default;

$table-active-color: $body-color !default;
$table-active-bg-factor: 0.08 !default;
$table-active-bg: rgba($primary, $table-active-bg-factor) !default;

$table-hover-bg-factor: 0.06 !default;
$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;

$table-border-color: $border-color !default;
$table-group-separator-color: $table-border-color !default;

// * Accordion
// *******************************************************************************
$accordion-bg: $card-bg !default;
$accordion-border-color: $border-color !default;

$accordion-button-color: $headings-color !default;

// * Tooltips
// *******************************************************************************
$tooltip-bg: #f7f4ff !default;
$tooltip-color: $black !default;

// Buttons
// *******************************************************************************

$btn-box-shadow: 0px 2px 4px rgba(15, 20, 34, 0.4) !default;

// * Forms
// *******************************************************************************

$input-bg: transparent !default;

$input-disabled-border-color: rgba-to-hex(rgba($base, 0.23), $rgba-to-hex-bg) !default;

$input-border-hover-color: rgba-to-hex($gray-600, $rgba-to-hex-bg) !default; // (C)

$input-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;

$form-select-bg: $input-bg !default;
$form-select-indicator: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="#{$base}" fill-opacity="0.9"/></svg>') !default;

$form-range-thumb-bg: $primary !default;

// * Navs
// *******************************************************************************

$nav-tabs-link-active-bg: $card-bg !default;
$nav-tabs-link-active-border-color: $nav-tabs-link-active-bg !default;
$nav-pills-link-hover-bg: rgba-to-hex(rgba($primary, 0.16), $card-bg) !default; // (C)

// * Navbar
// *******************************************************************************

$navbar-light-hover-color: #4e5155 !default;
$navbar-light-active-color: #4e5155 !default;
$navbar-light-disabled-color: rgba($black, 0.2) !default;
$navbar-dropdown-hover-bg: rgba-to-hex(rgba($base, 0.06), $rgba-to-hex-bg) !default; // (C)
$navbar-dropdown-icon-bg: rgba-to-hex(rgba($base, 0.08), $rgba-to-hex-bg) !default; // (C)

// * Dropdowns
// *******************************************************************************

$dropdown-bg: $card-bg !default;
$dropdown-divider-bg: $border-color !default;

$dropdown-link-hover-bg: $gray-50 !default;

// * Pagination
// *******************************************************************************

$pagination-bg: $gray-75 !default;
$pagination-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;
$pagination-disabled-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;

// * Modal
// *******************************************************************************
$modal-content-bg: $card-bg !default;
$modal-backdrop-bg: #171925 !default;
$modal-backdrop-opacity: 0.6 !default;

// * Progress bars
// *******************************************************************************
$progress-bg: $gray-75 !default;

// * List group
// *******************************************************************************

$list-group-border-color: $border-color !default;
$list-group-item-bg-hover-scale: 6% !default; //  (c)
$list-group-active-bg: rgba-to-hex(rgba($primary, 0.16), $rgba-to-hex-bg) !default;
$list-group-hover-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default;

// * Close
// *******************************************************************************
$btn-close-color: $white !default;

$kbd-color: $dark !default;

// * Config
$rtl-support: false !default;
$dark-style: true !default;
