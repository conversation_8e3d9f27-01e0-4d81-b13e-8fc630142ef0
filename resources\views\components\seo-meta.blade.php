@props([
    'page' => 'default',
    'data' => [],
    'structuredData' => true
])

@php
    $seoData = \App\Services\SEOService::getMetaData($page, $data);
    $structuredDataJson = $structuredData ? \App\Services\SEOService::getStructuredData($page, $data) : null;
@endphp

{{-- SEO Meta Tags --}}
<meta name="description" content="{{ $seoData['description'] }}">
<meta name="keywords" content="{{ $seoData['keywords'] }}">
<link rel="canonical" href="{{ $seoData['canonical'] }}">

{{-- Open Graph Meta Tags --}}
<meta property="og:title" content="{{ $seoData['title'] }}">
<meta property="og:description" content="{{ $seoData['description'] }}">
<meta property="og:type" content="{{ $seoData['og_type'] ?? 'website' }}">
<meta property="og:url" content="{{ $seoData['canonical'] }}">
<meta property="og:image" content="{{ $seoData['og_image'] ?? asset('assets/img/ppresent-og.jpg') }}">
<meta property="og:site_name" content="{{ config('variables.templateName', 'PPresent') }}">

{{-- Twitter Card Meta Tags --}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $seoData['title'] }}">
<meta name="twitter:description" content="{{ $seoData['description'] }}">
<meta name="twitter:image" content="{{ $seoData['og_image'] ?? asset('assets/img/ppresent-og.jpg') }}">

{{-- Additional SEO Meta Tags --}}
<meta name="robots" content="index, follow">
<meta name="author" content="{{ config('variables.creatorName', 'PPresent') }}">
<meta name="generator" content="{{ config('variables.templateName', 'PPresent') }} {{ config('variables.templateVersion', '2.0.0') }}">

{{-- Structured Data (JSON-LD) --}}
@if($structuredDataJson)
<script type="application/ld+json">
{!! json_encode($structuredDataJson, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
@endif
