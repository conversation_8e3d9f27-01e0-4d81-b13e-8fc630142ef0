<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class CriticalCSSExtractor
{
    /**
     * Extract critical CSS for above-the-fold content
     * 
     * @param string $page
     * @return string
     */
    public static function getCriticalCSS($page = 'default')
    {
        $cacheKey = "critical_css_{$page}";
        
        return Cache::remember($cacheKey, 3600, function () use ($page) {
            return self::extractCriticalCSS($page);
        });
    }
    
    /**
     * Extract critical CSS based on page type
     * 
     * @param string $page
     * @return string
     */
    private static function extractCriticalCSS($page)
    {
        switch ($page) {
            case 'software':
                return self::getSoftwareCriticalCSS();
            case 'hotmail':
                return self::getHotmailCriticalCSS();
            case 'home':
                return self::getHomeCriticalCSS();
            default:
                return self::getDefaultCriticalCSS();
        }
    }
    
    /**
     * Get critical CSS for software pages
     * 
     * @return string
     */
    private static function getSoftwareCriticalCSS()
    {
        return '
        /* Critical CSS for Software Pages */
        body {
            font-family: "Public Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.5;
            /* Accessibility: Support zoom up to 200% */
            overflow-x: auto;
        }
        .layout-wrapper { display: flex; min-height: 100vh; }
        .layout-container { flex: 1; display: flex; }
        .layout-page { flex: 1; }
        .content-wrapper { padding: 1.5rem; }
        .container-fluid, .container-xxl { 
            width: 100%; 
            padding-right: 1.5rem; 
            padding-left: 1.5rem; 
            margin-right: auto; 
            margin-left: auto; 
        }
        .row { 
            display: flex; 
            flex-wrap: wrap; 
            margin-right: -0.75rem; 
            margin-left: -0.75rem; 
        }
        .col-md-6, .col-xxl-4 { 
            position: relative; 
            width: 100%; 
            padding-right: 0.75rem; 
            padding-left: 0.75rem; 
        }
        @media (min-width: 768px) {
            .col-md-6 { flex: 0 0 50%; max-width: 50%; }
        }
        @media (min-width: 1400px) {
            .col-xxl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        }
        .card { 
            position: relative; 
            display: flex; 
            flex-direction: column; 
            min-width: 0; 
            background-color: #fff; 
            border: 1px solid rgba(67, 89, 113, 0.12); 
            border-radius: 0.5rem; 
            box-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
        }
        .card-body { 
            flex: 1 1 auto; 
            padding: 1.5rem; 
        }
        .img-tool { 
            width: 100%; 
            height: 30vh; 
            object-fit: cover; 
            border-radius: 0.375rem;
        }
        .btn { 
            display: inline-block; 
            font-weight: 500; 
            text-align: center; 
            vertical-align: middle; 
            border: 1px solid transparent; 
            padding: 0.5rem 1rem; 
            font-size: 0.9375rem; 
            line-height: 1.53; 
            border-radius: 0.375rem; 
            text-decoration: none;
        }
        .btn-primary { 
            color: #fff; 
            background-color: #696cff; 
            border-color: #696cff; 
        }
        .w-100 { width: 100% !important; }
        .mb-6 { margin-bottom: 2.25rem !important; }
        .text-center { text-align: center !important; }
        .position-relative { position: relative !important; }
        .position-absolute { position: absolute !important; }
        .badge { 
            display: inline-block; 
            padding: 0.35em 0.65em; 
            font-size: 0.75em; 
            font-weight: 500; 
            line-height: 1; 
            text-align: center; 
            white-space: nowrap; 
            vertical-align: baseline; 
            border-radius: 0.375rem; 
        }
        .bg-warning { background-color: #ffab00 !important; }
        .bg-success { background-color: #71dd37 !important; }
        .bg-danger { background-color: #ff3e1d !important; }
        .text-danger { color: #ff3e1d !important; }
        .fs-6 { font-size: 1rem !important; }
        .p-3 { padding: 1rem !important; }
        h5 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
            font-size: 1.125rem;
        }

        /* Accessibility: Better zoom support */
        @media (max-width: 576px) {
            .container-fluid, .container-xxl {
                padding-right: 1rem;
                padding-left: 1rem;
            }
            .card-body {
                padding: 1rem;
            }
            h5 {
                font-size: 1rem;
            }
        }

        /* High zoom levels support */
        @media (max-width: 320px) {
            .row {
                margin-right: -0.5rem;
                margin-left: -0.5rem;
            }
            .col-md-6, .col-xxl-4 {
                padding-right: 0.5rem;
                padding-left: 0.5rem;
            }
        }
        ';
    }
    
    /**
     * Get critical CSS for hotmail pages
     * 
     * @return string
     */
    private static function getHotmailCriticalCSS()
    {
        return self::getSoftwareCriticalCSS(); // Similar layout
    }
    
    /**
     * Get critical CSS for home page
     * 
     * @return string
     */
    private static function getHomeCriticalCSS()
    {
        return '
        /* Critical CSS for Home Page */
        body { 
            font-family: "Public Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            margin: 0; 
            padding: 0; 
            line-height: 1.5;
        }
        .layout-wrapper { display: flex; min-height: 100vh; }
        .navbar { 
            position: relative; 
            display: flex; 
            flex-wrap: wrap; 
            align-items: center; 
            justify-content: space-between; 
            padding: 0.5rem 1rem; 
        }
        .hero-section { 
            padding: 3rem 0; 
            text-align: center; 
        }
        .container { 
            width: 100%; 
            padding-right: 1.5rem; 
            padding-left: 1.5rem; 
            margin-right: auto; 
            margin-left: auto; 
        }
        h1 { 
            margin-top: 0; 
            margin-bottom: 0.5rem; 
            font-weight: 600; 
            line-height: 1.2; 
            font-size: 2.5rem; 
        }
        .btn { 
            display: inline-block; 
            font-weight: 500; 
            text-align: center; 
            vertical-align: middle; 
            border: 1px solid transparent; 
            padding: 0.75rem 1.5rem; 
            font-size: 1rem; 
            line-height: 1.5; 
            border-radius: 0.375rem; 
            text-decoration: none;
        }
        .btn-primary { 
            color: #fff; 
            background-color: #696cff; 
            border-color: #696cff; 
        }
        ';
    }
    
    /**
     * Get default critical CSS
     * 
     * @return string
     */
    private static function getDefaultCriticalCSS()
    {
        return '
        /* Default Critical CSS */
        * { box-sizing: border-box; }
        body { 
            font-family: "Public Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            margin: 0; 
            padding: 0; 
            line-height: 1.5;
            color: #566a7f;
            background-color: #f5f5f9;
        }
        .layout-wrapper { 
            display: flex; 
            min-height: 100vh; 
            flex-direction: column;
        }
        .layout-container { 
            flex: 1; 
            display: flex; 
        }
        .layout-page { 
            flex: 1; 
            display: flex; 
            flex-direction: column;
        }
        .content-wrapper { 
            flex: 1; 
            padding: 1.5rem; 
        }
        .container-fluid, .container-xxl { 
            width: 100%; 
            padding-right: 1.5rem; 
            padding-left: 1.5rem; 
            margin-right: auto; 
            margin-left: auto; 
        }
        .navbar { 
            position: relative; 
            display: flex; 
            flex-wrap: wrap; 
            align-items: center; 
            justify-content: space-between; 
            padding: 0.5rem 1rem; 
            background-color: #fff;
            border-bottom: 1px solid rgba(67, 89, 113, 0.12);
        }
        ';
    }
    
    /**
     * Get critical CSS for specific route
     * 
     * @param string $route
     * @return string
     */
    public static function getCriticalCSSForRoute($route)
    {
        if (str_contains($route, 'software')) {
            return self::getCriticalCSS('software');
        }
        
        if (str_contains($route, 'hotmail')) {
            return self::getCriticalCSS('hotmail');
        }
        
        if ($route === '/' || $route === 'home') {
            return self::getCriticalCSS('home');
        }
        
        return self::getCriticalCSS('default');
    }
    
    /**
     * Clear critical CSS cache
     * 
     * @return void
     */
    public static function clearCache()
    {
        $pages = ['default', 'software', 'hotmail', 'home'];
        
        foreach ($pages as $page) {
            Cache::forget("critical_css_{$page}");
        }
    }
}
