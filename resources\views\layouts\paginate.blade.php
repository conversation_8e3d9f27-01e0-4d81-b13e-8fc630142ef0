<!-- Pagination Sizes -->
<div class="row">
    <div class="col-lg-12">
        <div class="demo-inline-spacing">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <!-- Previous Page Link -->
                    <li class="page-item {{ $paginate->onFirstPage() ? 'disabled' : '' }}">
                        <a class="page-link" href="{{ $paginate->previousPageUrl() ?? '#' }}"
                           aria-label="Go to previous page"
                           @if($paginate->onFirstPage()) aria-disabled="true" onclick="return false;" @endif>
                            <i class="ti ti-chevrons-left ti-sm"></i>
                        </a>
                    </li>

                    <!-- Pagination Elements -->
                    @php
                        $start = max(1, $paginate->currentPage() - 2);
                        $end = min($paginate->lastPage(), $paginate->currentPage() + 2);
                    @endphp

                    @if ($start > 1)
                        <li class="page-item">
                            <a class="page-link" href="{{ $paginate->url(1) }}">1</a>
                        </li>
                        @if ($start > 2)
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        @endif
                    @endif

                    @for ($i = $start; $i <= $end; $i++)
                        <li class="page-item {{ $i == $paginate->currentPage() ? 'active' : '' }}">
                            <a class="page-link" href="{{ $paginate->url($i) }}">{{ $i }}</a>
                        </li>
                    @endfor

                    @if ($end < $paginate->lastPage())
                        @if ($end < $paginate->lastPage() - 1)
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        @endif
                        <li class="page-item">
                            <a class="page-link"
                                href="{{ $paginate->url($paginate->lastPage()) }}">{{ $paginate->lastPage() }}</a>
                        </li>
                    @endif

                    <!-- Next Page Link -->
                    <li class="page-item {{ $paginate->hasMorePages() ? '' : 'disabled' }}">
                        <a class="page-link" href="{{ $paginate->nextPageUrl() ?? '#' }}"
                           aria-label="Go to next page"
                           @if(!$paginate->hasMorePages()) aria-disabled="true" onclick="return false;" @endif>
                            <i class="ti ti-chevrons-right ti-sm"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
