<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use App\Http\Middleware\CacheGuestPages;

class GuestCacheMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure cache directory exists and is clean
        $cacheDir = public_path('cache/guest-pages-test');
        if (File::exists($cacheDir)) {
            File::deleteDirectory($cacheDir);
        }
        File::makeDirectory($cacheDir, 0755, true);
        
        // Set test config
        Config::set('guest-cache.directory', 'cache/guest-pages-test');
        Config::set('guest-cache.enabled', true);
        Config::set('guest-cache.duration', 1); // 1 minute for testing
    }

    protected function tearDown(): void
    {
        // Clean up test cache directory
        $cacheDir = public_path('cache/guest-pages-test');
        if (File::exists($cacheDir)) {
            File::deleteDirectory($cacheDir);
        }
        
        parent::tearDown();
    }

    /** @test */
    public function it_caches_guest_pages()
    {
        // First request - should create cache
        $response1 = $this->get('/');
        $response1->assertStatus(200);
        $response1->assertHeaderMissing('X-Cache-Status');

        // Check if cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(1, $files);

        // Second request - should serve from cache
        $response2 = $this->get('/');
        $response2->assertStatus(200);
        $response2->assertHeader('X-Cache-Status', 'HIT');
    }

    /** @test */
    public function it_does_not_cache_authenticated_users()
    {
        // Create and authenticate a user
        $user = \App\Models\User::factory()->create();
        $this->actingAs($user);

        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertHeaderMissing('X-Cache-Status');

        // Check that no cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }

    /** @test */
    public function it_does_not_cache_post_requests()
    {
        $response = $this->post('/', ['test' => 'data']);
        
        // Check that no cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }

    /** @test */
    public function it_respects_excluded_paths()
    {
        Config::set('guest-cache.excluded_paths', ['/admin/*', '/api/*']);

        $response = $this->get('/admin/dashboard');
        
        // Check that no cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }

    /** @test */
    public function it_handles_query_parameters_correctly()
    {
        Config::set('guest-cache.allowed_query_params', ['utm_source', 'ref']);

        // Allowed query param - should cache
        $response1 = $this->get('/?utm_source=google');
        $response1->assertStatus(200);

        // Check cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(1, $files);

        // Disallowed query param - should not cache
        $response2 = $this->get('/?page=2');
        
        // Should still only have 1 cache file
        $files = File::files($cacheDir);
        $this->assertCount(1, $files);
    }

    /** @test */
    public function it_clears_expired_cache()
    {
        // Create a cache file
        $this->get('/');
        
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(1, $files);

        // Manually set file modification time to past
        $file = $files[0];
        touch($file->getPathname(), time() - 120); // 2 minutes ago

        // Clear expired cache
        $cleared = CacheGuestPages::clearExpiredCache();
        $this->assertEquals(1, $cleared);

        // Check file was deleted
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }

    /** @test */
    public function it_can_clear_all_cache()
    {
        // Create multiple cache files
        $this->get('/');
        $this->get('/?utm_source=google');
        
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(2, $files);

        // Clear all cache
        $cleared = CacheGuestPages::clearAllCache();
        $this->assertEquals(2, $cleared);

        // Check all files were deleted
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }

    /** @test */
    public function it_respects_cache_disabled_config()
    {
        Config::set('guest-cache.enabled', false);

        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertHeaderMissing('X-Cache-Status');

        // Check that no cache file was created
        $cacheDir = public_path('cache/guest-pages-test');
        $files = File::files($cacheDir);
        $this->assertCount(0, $files);
    }
}
