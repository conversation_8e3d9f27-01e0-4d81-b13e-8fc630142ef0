<!-- BEGIN: Theme CSS-->
<!-- Fonts - Optimized for Performance -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<!-- Load fonts with display=swap and async loading -->
<link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
<noscript><link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet"></noscript>

{{-- Load only essential icons immediately --}}
@vite(['resources/assets/vendor/fonts/tabler-icons.scss'])

{{-- Defer non-critical icon fonts using JavaScript --}}
{!! \App\Helpers\ViteHelper::generateDeferredCSSScript(['fontawesome', 'flag-icons', 'node-waves']) !!}

{{-- Load essential CSS directly to avoid 404 errors --}}
@vite([
  'resources/assets/vendor/fonts/fontawesome.scss',
  'resources/assets/vendor/libs/node-waves/node-waves.scss',
  'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss',
])
<!-- Core CSS -->
@vite(['resources/assets/vendor/scss'.$configData['rtlSupport'].'/core' .($configData['style'] !== 'light' ? '-' . $configData['style'] : '') .'.scss',
'resources/assets/vendor/scss'.$configData['rtlSupport'].'/' .$configData['theme'] .($configData['style'] !== 'light' ? '-' . $configData['style'] : '') .'.scss',
'resources/assets/css/demo.css'])


<!-- Vendor Styles - Load directly for now -->
{{-- Temporarily disabled deferred loading to avoid 404 errors --}}
@yield('vendor-style')

<!-- Page Styles -->
@yield('page-style')

<!-- Font Loading Optimization -->
<script>
// Optimize font loading
(function() {
    // Add font-display: swap to any @font-face rules
    var style = document.createElement('style');
    style.textContent = `
        @font-face {
            font-display: swap;
        }
    `;
    document.head.appendChild(style);
})();
</script>
