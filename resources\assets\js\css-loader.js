/**
 * CSS Loader - Optimize CSS loading for better performance
 * Loads non-critical CSS after page load
 */

'use strict';

class CSSLoader {
    constructor() {
        this.loadedCSS = new Set();
        this.init();
    }

    init() {
        // Load deferred CSS after page load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadDeferredCSS();
            });
        } else {
            this.loadDeferredCSS();
        }

        // Load CSS on user interaction
        this.bindInteractionEvents();
    }

    /**
     * Load deferred CSS files
     */
    loadDeferredCSS() {
        // Wait a bit to ensure critical content is rendered
        setTimeout(() => {
            this.loadCSSFile('fontawesome', '/build/assets/vendor/fonts/fontawesome.scss');
            this.loadCSSFile('flag-icons', '/build/assets/vendor/fonts/flag-icons.scss');
            this.loadCSSFile('node-waves', '/build/assets/vendor/libs/node-waves/node-waves.scss');
        }, 100);
    }

    /**
     * Load CSS file asynchronously
     */
    loadCSSFile(name, href) {
        if (this.loadedCSS.has(name)) {
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'all';
        
        // Add to head
        document.head.appendChild(link);
        this.loadedCSS.add(name);

        // Optional: Add loading indicator
        this.showCSSLoadingIndicator(name);
    }

    /**
     * Bind events to load CSS on user interaction
     */
    bindInteractionEvents() {
        // Load additional CSS on first user interaction
        const events = ['click', 'scroll', 'keydown', 'touchstart'];
        
        const loadOnInteraction = () => {
            this.loadInteractionCSS();
            
            // Remove event listeners after first interaction
            events.forEach(event => {
                document.removeEventListener(event, loadOnInteraction, { passive: true });
            });
        };

        events.forEach(event => {
            document.addEventListener(event, loadOnInteraction, { passive: true });
        });
    }

    /**
     * Load CSS that's only needed on user interaction
     */
    loadInteractionCSS() {
        // Load CSS for interactive elements
        this.loadCSSFile('perfect-scrollbar', '/build/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss');
        this.loadCSSFile('typeahead-js', '/build/assets/vendor/libs/typeahead-js/typeahead.scss');
    }

    /**
     * Show loading indicator for CSS
     */
    showCSSLoadingIndicator(name) {
        // Optional: Show subtle loading indicator
        console.log(`Loading CSS: ${name}`);
    }

    /**
     * Preload CSS for next page
     */
    preloadCSS(href) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
    }

    /**
     * Load CSS based on page type
     */
    loadPageSpecificCSS(pageType) {
        switch (pageType) {
            case 'software':
                this.loadCSSFile('datatables', '/build/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss');
                break;
            case 'admin':
                this.loadCSSFile('admin-styles', '/build/assets/css/admin.scss');
                break;
        }
    }

    /**
     * Remove unused CSS
     */
    removeUnusedCSS() {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        
        stylesheets.forEach(stylesheet => {
            const href = stylesheet.href;
            
            // Remove CSS that's not needed for current page
            if (this.isUnusedCSS(href)) {
                stylesheet.remove();
            }
        });
    }

    /**
     * Check if CSS is unused for current page
     */
    isUnusedCSS(href) {
        const currentPage = this.getCurrentPageType();
        const unusedPatterns = {
            'software': [
                'admin',
                'datatables', // Only if not using datatables
                'calendar'
            ],
            'home': [
                'admin',
                'datatables',
                'calendar',
                'editor'
            ]
        };

        const patterns = unusedPatterns[currentPage] || [];
        return patterns.some(pattern => href.includes(pattern));
    }

    /**
     * Get current page type
     */
    getCurrentPageType() {
        const path = window.location.pathname;
        
        if (path.includes('/software')) return 'software';
        if (path.includes('/admin')) return 'admin';
        if (path.includes('/hotmail')) return 'hotmail';
        if (path === '/') return 'home';
        
        return 'default';
    }

    /**
     * Optimize CSS loading for mobile
     */
    optimizeForMobile() {
        if (this.isMobile()) {
            // Delay non-critical CSS loading on mobile
            setTimeout(() => {
                this.loadDeferredCSS();
            }, 500);
        }
    }

    /**
     * Check if device is mobile
     */
    isMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * Monitor CSS loading performance
     */
    monitorPerformance() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const entries = performance.getEntriesByType('resource');
                    const cssEntries = entries.filter(entry => entry.name.includes('.css'));
                    
                    console.log('CSS Loading Performance:', {
                        totalCSS: cssEntries.length,
                        totalSize: cssEntries.reduce((sum, entry) => sum + (entry.transferSize || 0), 0),
                        loadTime: cssEntries.reduce((max, entry) => Math.max(max, entry.responseEnd), 0)
                    });
                }, 1000);
            });
        }
    }
}

// Initialize CSS loader
document.addEventListener('DOMContentLoaded', () => {
    window.cssLoader = new CSSLoader();
    
    // Monitor performance in development
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
        window.cssLoader.monitorPerformance();
    }
});

// Export for use in other scripts
window.CSSLoader = CSSLoader;
