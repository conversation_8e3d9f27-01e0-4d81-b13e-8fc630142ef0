<?php

namespace App\Http\Controllers\hotmail;

use App\Enums\PurchaseType;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\TransactionLog;
use App\Models\User;
use App\Services\BuyHotmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class HotmailPurchase extends Controller
{
    public function purchase(Request $request)
    {
        $types = array_column(config('hotmail'), 'AccountCode');
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
            'type' => ['required', 'in:' . implode(',', $types)],
        ]);
        if ($validator->fails()) {
            return response()->json(['message' => $validator->errors()->first()], 422);
        }
        $userId = auth()->id();
        $quantity = $request->quantity;
        $type = collect(config('hotmail'))->firstWhere('AccountCode', $request->type);
        $price = $type['ViPrice'];
        $amount = round($quantity * $price, 4);
        DB::beginTransaction();
        try {
            $user = User::where('id', $userId)->lockForUpdate()->first();
            if (!$user || $user->balance < $amount) {
                DB::rollBack();
                return response()->json(['message' => __('messages.not_enough_bal')], 400);
            }
            if (BuyHotmail::BuyHotmail($quantity, $type['AccountCode'], $data)) {
                $user->balance -= $amount;
                $user->save();
                TransactionLog::create([
                    'user_id'     => $user->id,
                    'type'        => 'purchase',
                    'amount'      => $amount,
                    'description' => "Mua {$quantity} x " . $type['Name'],
                ]);
                Order::create([
                    'user_purchase_id' => $userId,
                    'type' => PurchaseType::Hotmail,
                    'amount' => $amount,
                    'data' => json_encode($data),
                ]);
                DB::commit();
                return response()->json(['success' => true, 'message' => __('messages.purchase_successful'), 'data' => $data]);
            } else {
                DB::rollBack();
                return response()->json(['success' => false, 'message' => $data]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => __('messages.unknow_error')]);
        }
    }
}
