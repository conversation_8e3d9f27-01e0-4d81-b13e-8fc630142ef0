import{t as l}from"./toastr-KXWBn683.js";/* empty css                   */import"./_commonjsHelpers-BosuxZz1.js";import"./jquery-Czc5UB_B.js";l.options={closeButton:!0,progressBar:!0,positionClass:"toast-top-right",timeOut:"5000"};$(".downloadTxt").on("click",function(){var e=$(this).data("download"),r=Object.keys(e[0]),t=[r.join("|")];e.forEach(d=>{var c=r.map(i=>d[i]).join("|");t.push(c)});var o=new Blob([t.join(`
`)],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(o),a=document.createElement("a");a.href=n,a.download="data.txt",document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(n)});$(".downloadJson").on("click",function(){var e=JSON.stringify($(this).data("download")),r=new Blob([e],{type:"application/json;charset=utf-8"}),t=URL.createObjectURL(r),o=document.createElement("a");o.href=t,o.download="data.json",document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(t)});
