@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

.ui-timepicker-wrapper {
  max-height: 10rem;
  overflow-y: auto;
  margin: 0.125rem 0;
  background: light.$white;
  background-clip: padding-box;
  outline: none;
}

.ui-timepicker-list {
  list-style: none;
  padding: 0.125rem 0;
  margin: 0;
}

.ui-timepicker-duration {
  margin-left: 0.25rem;

  @include app-rtl {
    margin-left: 0;
    margin-right: 0.25rem;
  }
}

.ui-timepicker-list li {
  padding: 0.25rem 0.75rem;
  margin: 0.125rem 0.875rem;
  white-space: nowrap;
  cursor: pointer;
  list-style: none;
  border-radius: light.$dropdown-border-radius;

  &.ui-timepicker-disabled,
  &.ui-timepicker-selected.ui-timepicker-disabled {
    background: light.$white !important;
    cursor: default !important;
  }
}

@if $enable-light-style {
  .light-style {
    .ui-timepicker-wrapper {
      padding: light.$dropdown-padding-y;
      z-index: light.$zindex-popover;
      background: light.$dropdown-bg;
      box-shadow: light.$card-box-shadow;
      border: light.$dropdown-border-width solid light.$dropdown-border-color;

      @include light.border-radius(light.$border-radius);
    }

    .ui-timepicker-list li {
      color: light.$dropdown-link-color;

      &:hover {
        background: light.$dropdown-link-hover-bg;
      }
      &:not(.ui-timepicker-selected) {
        .ui-timepicker-duration {
          color: light.$text-muted;

          .ui-timepicker-list:hover & {
            color: light.$text-muted;
          }
        }
      }
    }

    .ui-timepicker-list li.ui-timepicker-disabled,
    .ui-timepicker-list li.ui-timepicker-selected.ui-timepicker-disabled {
      background: light.$dropdown-bg !important;
      color: light.$dropdown-link-disabled-color !important;
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .ui-timepicker-wrapper {
      border: dark.$dropdown-border-width solid dark.$dropdown-border-color;
      padding: dark.$dropdown-padding-y 0;
      z-index: dark.$zindex-popover;
      background: dark.$dropdown-bg;
      box-shadow: dark.$card-box-shadow;

      @include dark.border-radius(dark.$border-radius);
    }

    .ui-timepicker-list li {
      color: dark.$dropdown-link-color;

      &:hover {
        background: dark.$dropdown-link-hover-bg;
      }
      &:not(.ui-timepicker-selected) {
        .ui-timepicker-duration {
          color: dark.$text-muted;

          .ui-timepicker-list:hover & {
            color: dark.$text-muted;
          }
        }
      }
    }

    .ui-timepicker-list li.ui-timepicker-disabled,
    .ui-timepicker-list li.ui-timepicker-selected.ui-timepicker-disabled {
      color: dark.$dropdown-link-disabled-color !important;
      background: dark.$dropdown-bg !important;
    }
  }
}
