// * Logistics Overview
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

// Vehicles overview progress labels
.vehicles-progress-labels {
  .vehicles-progress-label {
    position: relative;
    padding-bottom: 15px;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      display: inline-block;
      height: 10px;
      width: 2px;
      [dir='rtl'] & {
        left: unset;
        right: 0;
      }
    }
  }
}

// Vehicles overview progress
.vehicles-overview-progress {
  .bg-gray-900 {
    color: light.$white;
  }
}

// Shipment statistics chart legend
#shipmentStatisticsChart,
#carrierPerformance {
  .apexcharts-legend-series {
    padding: 4px 12px;
    padding-inline-start: 17px;
    border-radius: light.$border-radius;
    height: 83%;
  }
}

//Light style
@if $enable-light-style {
  .light-style {
    .vehicles-progress-labels {
      .vehicles-progress-label {
        &::after {
          background-color: light.$border-color;
        }
      }
    }
    .vehicles-overview-progress {
      .snackbar {
        background-color: #2f2b3d;
      }
    }

    #shipmentStatisticsChart,
    #carrierPerformance {
      .apexcharts-legend-series {
        border: 1px solid light.$border-color;
      }
    }
  }
}

//Dark style
@if $enable-dark-style {
  .dark-style {
    .vehicles-progress-labels {
      .vehicles-progress-label {
        &::after {
          background-color: dark.$border-color;
        }
      }
    }
    .vehicles-overview-progress {
      .snackbar {
        background-color: #f7f4ff;
      }
    }
    #shipmentStatisticsChart,
    #carrierPerformance {
      .apexcharts-legend-series {
        border: 1px solid dark.$border-color;
      }
    }
  }
}
