<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SmsServer;
use App\Models\SmsCountry;
use App\Models\SmsService;

class SimRentalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Servers
        $servers = [
            ['name' => 'Server 1 - Premium', 'description' => 'Server cao cấp với tốc độ nhanh nhất', 'sort_order' => 1],
            ['name' => 'Server 2 - Standard', 'description' => 'Server tiêu chuẩn với giá cả hợp lý', 'sort_order' => 2],
            ['name' => 'Server 3 - Economy', 'description' => 'Server tiết kiệm cho người dùng cơ bản', 'sort_order' => 3],
            ['name' => 'Server 4 - Express', 'description' => 'Server nhanh chóng cho nhu cầu khẩn cấp', 'sort_order' => 4],
        ];

        foreach ($servers as $serverData) {
            SmsServer::create($serverData);
        }

        // Create Countries for each server
        $countryTemplates = [
            ['name' => 'Việt Nam', 'code' => 'VN', 'sort_order' => 1],
            ['name' => 'Hoa Kỳ', 'code' => 'US', 'sort_order' => 2],
            ['name' => 'Anh', 'code' => 'UK', 'sort_order' => 3],
            ['name' => 'Đức', 'code' => 'DE', 'sort_order' => 4],
            ['name' => 'Pháp', 'code' => 'FR', 'sort_order' => 5],
            ['name' => 'Nhật Bản', 'code' => 'JP', 'sort_order' => 6],
            ['name' => 'Hàn Quốc', 'code' => 'KR', 'sort_order' => 7],
            ['name' => 'Trung Quốc', 'code' => 'CN', 'sort_order' => 8],
        ];

        $servers = SmsServer::all();
        foreach ($servers as $server) {
            foreach ($countryTemplates as $countryData) {
                $countryData['server_id'] = $server->id;
                SmsCountry::create($countryData);
            }
        }

        // Create Services for each country
        $serviceTemplates = [
            ['name' => 'Telegram', 'code' => 'telegram', 'sort_order' => 1],
            ['name' => 'WhatsApp', 'code' => 'whatsapp', 'sort_order' => 2],
            ['name' => 'Facebook', 'code' => 'facebook', 'sort_order' => 3],
            ['name' => 'Instagram', 'code' => 'instagram', 'sort_order' => 4],
            ['name' => 'Twitter', 'code' => 'twitter', 'sort_order' => 5],
            ['name' => 'TikTok', 'code' => 'tiktok', 'sort_order' => 6],
        ];

        $countries = SmsCountry::all();
        foreach ($countries as $country) {
            foreach ($serviceTemplates as $serviceData) {
                $basePrice = match($serviceData['code']) {
                    'telegram' => 5000,
                    'whatsapp' => 7000,
                    'facebook' => 8000,
                    'instagram' => 9000,
                    'twitter' => 6000,
                    'tiktok' => 10000,
                    default => 5000,
                };

                // Adjust price based on server type
                $multiplier = match($country->server->name) {
                    'Server 1 - Premium' => 1.5,
                    'Server 2 - Standard' => 1.0,
                    'Server 3 - Economy' => 0.8,
                    'Server 4 - Express' => 1.3,
                    default => 1.0,
                };

                $serviceData['country_id'] = $country->id;
                $serviceData['price'] = $basePrice * $multiplier;
                $serviceData['stock'] = rand(10, 100);

                SmsService::create($serviceData);
            }
        }


    }
}
