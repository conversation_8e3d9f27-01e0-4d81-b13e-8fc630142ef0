<?php

namespace App\Http\Controllers;

use App\Models\Software;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap
     */
    public function index()
    {
        $urls = [];
        
        // Add main pages
        $urls[] = [
            'url' => url('/'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0'
        ];
        
        $urls[] = [
            'url' => url('/software/all'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '0.9'
        ];
        
        $urls[] = [
            'url' => url('/hotmail'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'weekly',
            'priority' => '0.8'
        ];
        
        // Add software pages
        $softwares = Software::where('status', 1)->get();
        foreach ($softwares as $software) {
            $urls[] = [
                'url' => url('/software/' . $software->url),
                'lastmod' => $software->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.7'
            ];
        }
        
        $xml = $this->generateSitemapXML($urls);
        
        return response($xml, 200)
            ->header('Content-Type', 'application/xml');
    }
    
    /**
     * Generate sitemap XML
     */
    private function generateSitemapXML($urls)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['url']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
}
