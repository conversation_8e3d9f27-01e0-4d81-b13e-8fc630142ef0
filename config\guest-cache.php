<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Guest Page Cache Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the guest page caching
    | middleware. You can enable/disable caching and configure various
    | options to optimize performance for non-authenticated users.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Enabled
    |--------------------------------------------------------------------------
    |
    | This option controls whether the guest page caching is enabled.
    | Set to false to disable caching entirely.
    |
    */

    'enabled' => env('GUEST_CACHE_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Cache Duration
    |--------------------------------------------------------------------------
    |
    | The duration (in minutes) for which cached pages should be stored.
    | Default is 10 minutes.
    |
    */

    'duration' => env('GUEST_CACHE_DURATION', 10),

    /*
    |--------------------------------------------------------------------------
    | Cache Directory
    |--------------------------------------------------------------------------
    |
    | The directory where cached files will be stored relative to public path.
    | Default is 'cache/guest-pages'.
    |
    */

    'directory' => env('GUEST_CACHE_DIRECTORY', 'cache/guest-pages'),

    /*
    |--------------------------------------------------------------------------
    | Allowed Query Parameters
    |--------------------------------------------------------------------------
    |
    | Query parameters that are allowed in cached URLs. URLs with other
    | query parameters will not be cached.
    | Thêm nhiều params cho SEO và tracking.
    |
    */

    'allowed_query_params' => [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'ref',
        'source',
        'fbclid',
        'gclid',
        'msclkid',        // Microsoft Ads
        'dclid',          // Google Display & Video 360
        'wbraid',         // Google Ads enhanced conversions
        'gbraid',         // Google Ads enhanced conversions
        '_ga',            // Google Analytics
        '_gl',            // Google Analytics cross-domain
        'mc_cid',         // Mailchimp campaign ID
        'mc_eid',         // Mailchimp email ID
        'pk_campaign',    // Matomo/Piwik
        'pk_kwd',         // Matomo/Piwik
        'pk_source',      // Matomo/Piwik
        'pk_medium',      // Matomo/Piwik
    ],

    /*
    |--------------------------------------------------------------------------
    | Excluded Paths
    |--------------------------------------------------------------------------
    |
    | Paths that should never be cached. Use wildcards (*) for pattern matching.
    |
    */

    'excluded_paths' => [
        '/admin/*',
        '/api/*',
        '/login',
        '/auth/login',
        'auth/login',
        '/register',
        '/auth/register',
        '/logout',
        '/auth/logout',
        '/password/*',
        '/email/*',
        '/sitemap.xml',
        '/robots.txt',
        '/favicon.ico',
        '/*.json',
        '/*.xml',
        '/feed',
        '/rss',
    ],

    /*
    |--------------------------------------------------------------------------
    | Excluded User Agents
    |--------------------------------------------------------------------------
    |
    | User agents that should not receive cached content.
    | Chỉ loại trừ các bot xấu/scraper, cho phép SEO bots và social media bots.
    | Cache 10 phút là hợp lý cho tất cả bot crawl.
    |
    */

    'excluded_user_agents' => [
        'scrapy',           // Scraping framework
        'wget',             // Command line downloader
        'python-requests',  // Python scraping
        'go-http-client',   // Go scraping
        'java/',            // Java scraping
        'apache-httpclient', // Apache HTTP client
        'okhttp',           // OkHttp client
        'headless',         // Headless browsers
        'phantom',          // PhantomJS
        'selenium',         // Selenium automation
        'puppeteer',        // Puppeteer automation
        'playwright',       // Playwright automation
        // Cho phép: googlebot, bingbot, facebookexternalhit, twitterbot, etc.
    ],

    /*
    |--------------------------------------------------------------------------
    | HTML Minification Options
    |--------------------------------------------------------------------------
    |
    | Options for HTML minification when caching pages.
    |
    */

    'minification' => [
        'enabled' => env('GUEST_CACHE_MINIFY', true),
        'remove_comments' => true,
        'remove_empty_attributes' => true,
        'remove_default_attributes' => true,
        'remove_redundant_attributes' => true,
        'minify_css' => true,
        'minify_js' => true,
        'preserve_line_breaks' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Headers
    |--------------------------------------------------------------------------
    |
    | HTTP headers to add to cached responses.
    |
    */

    'headers' => [
        'Cache-Control' => 'public, max-age=' . (env('GUEST_CACHE_DURATION', 10) * 60),
        'X-Cache-Status' => 'HIT', // Will be overridden by middleware
        'Vary' => 'Accept-Encoding',
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Cleanup
    |--------------------------------------------------------------------------
    |
    | Automatically clean up expired cache files.
    |
    */

    'auto_cleanup' => [
        'enabled' => env('GUEST_CACHE_AUTO_CLEANUP', true),
        'probability' => 2, // 2% chance on each request
        'max_files_per_cleanup' => 100,
    ],

    /*
    |--------------------------------------------------------------------------
    | Compression
    |--------------------------------------------------------------------------
    |
    | Enable gzip compression for cached files.
    |
    */

    'compression' => [
        'enabled' => env('GUEST_CACHE_COMPRESSION', false),
        'level' => 6, // Compression level 1-9
    ],

    /*
    |--------------------------------------------------------------------------
    | Debug Mode
    |--------------------------------------------------------------------------
    |
    | Enable debug mode to add additional headers and logging.
    |
    */

    'debug' => env('GUEST_CACHE_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Statistics
    |--------------------------------------------------------------------------
    |
    | Enable cache statistics tracking.
    |
    */

    'statistics' => [
        'enabled' => env('GUEST_CACHE_STATS', false),
        'file' => storage_path('logs/guest-cache-stats.log'),
    ],

];
