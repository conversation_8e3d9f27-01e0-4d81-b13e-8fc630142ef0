// Progress
// *******************************************************************************

.progress:has(:only-child) {
  overflow: visible;
}

@each $color, $value in $theme-colors {
  @if $color != primary {
    @include template-progress-shadow-theme('.progress-bar.bg-#{$color}', $value);
  }
}
@include ltr-only {
  .progress {
    .progress-bar:last-child {
      border-top-right-radius: $progress-border-radius;
      border-bottom-right-radius: $progress-border-radius;
    }
    .progress-bar:first-child {
      border-top-left-radius: $progress-border-radius;
      border-bottom-left-radius: $progress-border-radius;
    }
  }
}

// RTL
// *******************************************************************************

@include rtl-only {
  .progress-bar-animated {
    animation-direction: reverse;
  }
  .progress {
    //  border radius for first and last child
    .progress-bar:last-child {
      border-top-left-radius: $progress-border-radius;
      border-bottom-left-radius: $progress-border-radius;
    }
    .progress-bar:first-child {
      border-top-right-radius: $progress-border-radius;
      border-bottom-right-radius: $progress-border-radius;
    }
  }
}
