<?php

namespace App\Http\Controllers\authentications;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LogoutBasic extends Controller
{
    public function logout(Request $request)
    {
        if (auth()->check()) {
            auth()->logout();
        }
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('software-all');
    }
}
