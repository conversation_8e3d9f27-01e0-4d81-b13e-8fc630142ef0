@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Hotmail')

@section('vendor-style')
    @vite([])
@endsection

@section('page-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
@endsection
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="fw-bold py-3 mb-4"><span class="">{{ __('messages.auto_deposit') }}</h4>
        <div class="card">
            <div class="row mt-2">
                <!-- First column-->
                <div class="col-12 col-lg-6">
                    <!-- Product Information -->
                    <div class="card mb-6 h-100">
                        <div class="card-header pb-0">
                            <h5 class="card-tile mb-0 text-center">MBbank</h5>
                        </div>
                        <div class="card-body">

                            <div class="mt-3 mb-5 text-center">
                                <img class="card-img-top"
                                    src="{{ 'https://api.vietqr.io/image/970422-816357-Ouy4zgk.jpg?accountName=NGUYEN%20VIET%20PHUONG&addInfo=naptien_mariana_' . auth()->id() }}"
                                    alt="Basic Image" />
                            </div>
                            <h4 class="card-title text-center text-capitalize mb-1">Nguyen Viet Phuong</h4>
                            <p class="text-center mb-5">816357</p>
                            <div class="mb-4 row">
                                <label for="html5-text-input" class="col-md-2 col-form-label">Lời nhắn</label>
                                <div class="col-md-10">
                                    <input class="form-control" type="text"
                                        value="{{ 'naptien_mariana_' . auth()->id() }}" id="html5-text-input" readonly />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-lg-6">
                    <!-- Pricing Card -->
                    <div class="card mb-3 h-100">
                        <div class="card-header pb-0">
                            <h5 class="card-title mb-0 text-center">VietcomBank</h5>
                        </div>
                        <div class="card-body">
                            <div class="mt-3 mb-5 text-center">
                                <img class="card-img-top"
                                    src="{{ 'https://api.vietqr.io/image/970436-*************-Ouy4zgk.jpg?accountName=NGUYEN%20VIET%20PHUONG&addInfo=naptien_mariana_' . auth()->id() }}"
                                    alt="Basic Image" />
                            </div>
                            <h4 class="card-title text-center text-capitalize mb-1">Nguyen Viet Phuong</h4>
                            <p class="text-center mb-5">*************</p>
                            <div class="mb-4 row">
                                <label for="html5-text-input" class="col-md-2 col-form-label">Lời nhắn</label>
                                <div class="col-md-10">
                                    <input class="form-control" type="text"
                                        value="{{ 'naptien_mariana_' . auth()->id() }}" id="html5-text-input" readonly />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <h5 class="card-header">Lịch sử nạp tiền</h5>
            <div class="table-responsive text-nowrap">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Stt</th>
                            <th>{{ __('messages.time') }}</th>
                            <th>{{ __('messages.amount') }}</th>
                            <th>{{ __('messages.type') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($deposits as $deposit)
                            <tr>
                                <td>{{ $loop->index + 1 }}</td>
                                <td>
                                    <strong>{{ $deposit->created_at }}</strong>
                                </td>
                                <td>{{ number_format($deposit->amount) }}đ</td>
                                <td>
                                    {{ $deposit->type }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-border-bottom-0">
                        <tr>
                            <th>Stt</th>
                            <th>{{ __('messages.time') }}</th>
                            <th>{{ __('messages.amount') }}</th>
                            <th>{{ __('messages.type') }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            @include('layouts.paginate', ['paginate' => $deposits])
        </div>

        {{-- @include('layouts.learner.paginate') --}}
    </div>
@endsection
@push('js')
@endpush
