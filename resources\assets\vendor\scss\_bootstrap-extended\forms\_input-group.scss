// Input groups
// *******************************************************************************

$validation-messages: '';
@each $state in map-keys($form-validation-states) {
  $validation-messages: $validation-messages +
    ':not(.' +
    unquote($state) +
    '-tooltip)' +
    ':not(.' +
    unquote($state) +
    '-feedback)';
}

// Using :focus-within to apply focus/validation border and shadow to default and merged input-group
.input-group {
  border-radius: $input-border-radius;
  // Input group (Default)
  .input-group-text {
    padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
    @include transition($input-transition);
  }

  //? Info :focus-within to apply focus/validation border and shadow to default and merged input & input-group
  &:focus-within {
    .input-group-text {
      border-width: $input-focus-border-width;
      padding: calc($input-padding-y - calc($input-focus-border-width + $input-border-width))
        calc($input-padding-x - $input-focus-border-width);
      .was-validated &,
      .fv-plugins-bootstrap5-row-invalid & {
        padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
      }
    }
    .form-control,
    .form-select {
      border-width: $input-focus-border-width;
      padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
      &:first-child {
        padding-inline-start: calc($input-padding-x - $input-focus-border-width);
      }
    }
  }
  // Input group (lg)
  &.input-group-lg {
    .input-group-text {
      padding: calc($input-padding-y-lg - $input-border-width) calc($input-padding-x-lg - $input-border-width);
    }
    &:focus-within {
      .input-group-text {
        padding: calc($input-padding-y-lg - $input-border-width) calc($input-padding-x-lg - $input-focus-border-width);
      }
      .form-control:not(:first-child),
      .form-select:not(:first-child) {
        padding: calc($input-padding-y-lg - $input-border-width) calc($input-padding-x-lg);
      }
    }
  }
  // Input group (sm)
  &.input-group-sm {
    .form-control,
    .form-select {
      padding-inline: calc($input-padding-x-sm - $input-border-width);
    }
    .input-group-text {
      padding: calc($input-padding-y-sm - $input-border-width) calc($input-padding-x-sm - $input-border-width);
    }
    &:focus-within {
      .input-group-text {
        padding: calc($input-padding-y-sm - $input-focus-border-width)
          calc($input-padding-x-sm - $input-focus-border-width);
      }
      .form-control,
      .form-select {
        padding: calc($input-padding-y-sm - $input-border-width) calc($input-padding-x-sm);
      }
    }
  }

  > :not(:first-child):not(.dropdown-menu):not(.btn):not(.dropdown-menu + .form-control):not(
      .btn + .form-control
    )#{$validation-messages} {
    margin-inline-start: calc($input-focus-border-width - 3px);
  }

  // Input group merge
  &.input-group-merge {
    > :not(:first-child):not(.dropdown-menu):not(.btn):not(.dropdown-menu + .form-control):not(
        .btn + .form-control
      )#{$validation-messages} {
      margin-inline-start: calc(($input-focus-border-width + $input-border-width) * -1);
    }
    &:focus-within {
      > .form-control:first-child,
      > .form-select:first-child {
        padding-inline: calc($input-padding-x - $input-focus-border-width);
      }
    }
    &.input-group-sm {
      &:focus-within {
        > .form-control:first-child,
        > .form-select:first-child {
          padding-inline: calc($input-padding-x - $input-focus-border-width);
        }
      }
    }
  }

  // Input group on focus-within use margin-left same as as focus border width
  &:focus-within {
    > :not(:first-child):not(.dropdown-menu):not(.btn):not(.dropdown-menu + .form-control):not(
        .btn + .form-control
      )#{$validation-messages} {
      margin-inline-start: calc($input-focus-border-width * -1);
    }
  }

  // Rounded pill option
  &.rounded-pill {
    .input-group-text,
    .form-control {
      @include border-radius($border-radius-pill);
    }
  }
  &:hover {
    .input-group-text,
    .form-control {
      border-color: $input-border-hover-color;
    }
  }

  &:focus-within {
    box-shadow: $input-focus-box-shadow;
    .form-control,
    .input-group-text {
      box-shadow: none;
    }
  }
  .was-validated & {
    &:has(.is-invalid),
    &:has(.is-valid),
    &:has(:valid),
    &:has(:invalid) {
      box-shadow: none;
    }
  }
  &.has-validation,
  &.is-invalid {
    &:has(.is-invalid),
    &:has(.is-valid),
    &:has(:invalid),
    &:has(.form-control.is-invalid) {
      box-shadow: none;
    }
  }
  // For disabled input group
  &.disabled {
    .input-group-text {
      background-color: $input-disabled-bg;
    }
  }
  // ! FIX: Formvalidation border radius issue
  &.has-validation {
    > .input-group-text:first-child {
      @include border-end-radius(0);
    }
    > .form-control:first-child {
      @include border-end-radius(0);
    }
    > .form-control:not(:first-child):not(:last-child) {
      @include border-radius(0);
    }
  }
}

// input-group-text icon size
.input-group-text {
  background-clip: padding-box;
  i {
    @include font-size(1.25rem);
  }
}
.input-group-lg > .input-group-text {
  i {
    @include font-size(1.375rem);
  }
}
.input-group-sm > .input-group-text {
  i {
    @include font-size(1.125rem);
  }
}

// Merge input

// Input group merge .form-control border & padding
@include ltr-only {
  .input-group-merge {
    .input-group-text {
      &:first-child {
        border-right: 0;
      }
      &:last-child {
        border-left: 0;
      }
    }
    &.disabled {
      > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(
          .invalid-feedback
        ) {
        margin-left: 0 !important;
      }
    }
    .form-control:not(textarea) {
      &:not(:first-child) {
        padding-left: 0 !important;
        border-left: 0;
      }
      &:not(:last-child) {
        padding-right: 0 !important;
        border-right: 0;
      }
    }
    .form-control:not(textarea) {
      &:not(:first-child) {
        padding-left: 0;
      }
      &:not(:last-child) {
        padding-right: 0;
      }
    }
  }
}

// Adding transition (On focus border color change)
.input-group-text {
  @include transition($input-transition);
}

// RTL Style
// *******************************************************************************

@include rtl-only {
  .input-group {
    // Rounded input field
    &.rounded-pill {
      .input-group-text {
        border-top-right-radius: $border-radius-pill !important;
        border-bottom-right-radius: $border-radius-pill !important;
      }
      .form-control {
        border-top-left-radius: $border-radius-pill !important;
        border-bottom-left-radius: $border-radius-pill !important;
      }
    }

    // Input group with dropdown rounded corners, while not validate
    &:not(.has-validation) {
      > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
      > .dropdown-toggle:nth-last-child(n + 3) {
        @include border-start-radius(0);
        @include border-end-radius($input-border-radius);
      }
    }
    &.input-group-lg {
      &:not(.has-validation) {
        > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
        > .dropdown-toggle:nth-last-child(n + 3) {
          @include border-end-radius($input-border-radius-lg);
        }
      }
    }
    &.input-group-sm {
      &:not(.has-validation) {
        > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
        > .dropdown-toggle:nth-last-child(n + 3) {
          @include border-end-radius($input-border-radius-sm);
        }
      }
    }

    // Input group with dropdown rounded corners, while validate
    &.has-validation {
      > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),
      > .dropdown-toggle:nth-last-child(n + 4) {
        @include border-start-radius(0);
        @include border-end-radius($input-border-radius);
      }
    }
    &.input-group-lg {
      > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),
      > .dropdown-toggle:nth-last-child(n + 4) {
        @include border-end-radius($input-border-radius-lg);
      }
    }
    &.input-group-sm {
      > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),
      > .dropdown-toggle:nth-last-child(n + 4) {
        @include border-end-radius($input-border-radius-sm);
      }
    }

    // Input group border radius
    $validation-messages: '';
    @each $state in map-keys($form-validation-states) {
      $validation-messages: $validation-messages +
        ':not(.' +
        unquote($state) +
        '-tooltip)' +
        ':not(.' +
        unquote($state) +
        '-feedback)';
    }

    > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {
      margin-right: calc(#{$input-border-width} * -1);
      @include border-end-radius(0);
      margin-left: 0px;
      @include border-start-radius($input-border-radius);
    }
    &.input-group-lg {
      > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {
        @include border-start-radius($input-border-radius-lg);
      }
    }
    &.input-group-sm {
      > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {
        @include border-start-radius($input-border-radius-sm);
      }
    }

    // ? We apply border radius from the above styles
    // Remove border radius from first and last element
    > :not(:first-child):not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
    > .dropdown-toggle:nth-last-child(n + 3):not(:first-child) {
      @include border-radius(0 !important);
    }

    // ! FIX: Formvalidation border radius issue
    &.has-validation {
      > .input-group-text:first-child {
        @include border-start-radius(0);
        @include border-end-radius($input-border-radius);
      }
      > .form-control:first-child {
        @include border-start-radius(0);
        @include border-end-radius($input-border-radius);
      }
    }
  }

  // Input group merge .input-group-text border & .form-control border & padding
  // Merge input
  .input-group-merge {
    .input-group-text {
      &:first-child {
        border-left: 0;
      }
      &:last-child {
        border-right: 0;
      }
    }
    .form-control {
      &:not(:first-child) {
        padding-right: 0 !important;
        border-right: 0;
      }
      &:not(:last-child) {
        padding-left: 0 !important;
        border-left: 0;
      }
    }
  }
}

//! FIX: Formvalidation : .input-group-text valid/invalid border color, If .input-group has .input-group-text first.
.fv-plugins-bootstrap5-row-invalid {
  .input-group.has-validation,
  .input-group.has-validation:focus-within {
    .input-group-text {
      border-color: $form-feedback-invalid-color !important;
    }
  }
}
// ? UnComment If eleValidClass is not blank i.e form-validation.js Line no. ~208
// .fv-plugins-bootstrap5-row-valid {
//   .input-group,
//   .input-group:focus-within {
//     .input-group-text {
//       border-color: $form-feedback-valid-color;
//     }
//   }
// }
