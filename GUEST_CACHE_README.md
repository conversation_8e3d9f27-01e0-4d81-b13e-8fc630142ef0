# Guest Page Cache Middleware

Middleware để cache HTML minify cho người dùng chưa đăng nhập nhằm tăng tốc độ truy cập website.

## Tính năng

- ✅ Cache HTML cho guest users (người dùng chưa đăng nhập)
- ✅ Minify HTML, CSS, JavaScript inline
- ✅ Thời hạn cache có thể cấu hình (mặc định 10 phút)
- ✅ Tự động dọn dẹp cache hết hạn
- ✅ Loại trừ các path không cần cache
- ✅ Loại trừ các user agent (bot, crawler)
- ✅ Hỗ trợ query parameters được phép
- ✅ Debug mode với headers chi tiết
- ✅ Command line để quản lý cache

## Cài đặt

Middleware đã được tự động đăng ký trong `bootstrap/app.php` và sẽ hoạt động cho tất cả các route web.

## C<PERSON>u hình

### File .env

Thêm các cấu hình sau vào file `.env`:

```env
# Guest Page Cache Settings
GUEST_CACHE_ENABLED=true
GUEST_CACHE_DURATION=10
GUEST_CACHE_DIRECTORY=cache/guest-pages
GUEST_CACHE_MINIFY=true
GUEST_CACHE_AUTO_CLEANUP=true
GUEST_CACHE_COMPRESSION=false
GUEST_CACHE_DEBUG=false
GUEST_CACHE_STATS=false
```

### File config/guest-cache.php

File cấu hình chi tiết với các tùy chọn:

- `enabled`: Bật/tắt cache
- `duration`: Thời hạn cache (phút)
- `directory`: Thư mục lưu cache
- `allowed_query_params`: Query parameters được phép
- `excluded_paths`: Đường dẫn không cache
- `excluded_user_agents`: User agents không cache
- `minification`: Tùy chọn minify HTML
- `auto_cleanup`: Tự động dọn dẹp cache

## Cách hoạt động

1. **Kiểm tra điều kiện**: Middleware kiểm tra xem có nên cache không:
   - Chỉ cache cho guest users
   - Chỉ cache GET requests
   - Không cache các path bị loại trừ
   - Không cache các user agent bị loại trừ

2. **Tạo cache key**: Tạo key duy nhất từ URL và query parameters

3. **Kiểm tra cache**: Nếu cache tồn tại và còn hạn, trả về ngay

4. **Xử lý request**: Nếu không có cache, xử lý request bình thường

5. **Lưu cache**: Minify HTML và lưu vào file cache

6. **Tự động dọn dẹp**: Ngẫu nhiên dọn dẹp cache hết hạn

## Sử dụng Command Line

### Xem thống kê cache
```bash
php artisan guest-cache stats
```

### Xóa cache hết hạn
```bash
php artisan guest-cache clear-expired
```

### Xóa tất cả cache
```bash
php artisan guest-cache clear
```

## Headers Response

Khi cache hoạt động, response sẽ có các headers:

- `X-Cache-Status`: `HIT` (từ cache) hoặc `MISS` (tạo mới)
- `X-Cache-Time`: Thời gian tạo cache
- `Cache-Control`: Thời hạn cache cho browser
- `X-Cache-File`: Tên file cache (chỉ khi debug=true)
- `X-Cache-Size`: Kích thước cache (chỉ khi debug=true)

## Tối ưu hóa

### 1. Cấu hình Nginx/Apache

Để tối ưu hơn, có thể cấu hình web server serve trực tiếp cache files:

**Nginx:**
```nginx
location / {
    # Thử serve cache file trước
    try_files /cache/guest-pages/$uri.html @laravel;
}

location @laravel {
    # Fallback to Laravel
    try_files $uri $uri/ /index.php?$query_string;
}
```

### 2. Gzip Compression

Bật compression trong config:
```env
GUEST_CACHE_COMPRESSION=true
```

### 3. CDN Integration

Cache files có thể được đẩy lên CDN để phân phối toàn cầu.

## Monitoring

### Debug Mode

Bật debug mode để xem thông tin chi tiết:
```env
GUEST_CACHE_DEBUG=true
```

### Statistics

Bật statistics để theo dõi hiệu suất:
```env
GUEST_CACHE_STATS=true
```

Log file: `storage/logs/guest-cache-stats.log`

## Testing

Chạy tests để đảm bảo middleware hoạt động đúng:

```bash
php artisan test tests/Feature/GuestCacheMiddlewareTest.php
php artisan test tests/Unit/HTMLMinifierTest.php
```

## Troubleshooting

### Cache không hoạt động

1. Kiểm tra `GUEST_CACHE_ENABLED=true`
2. Kiểm tra quyền ghi thư mục `public/cache/guest-pages`
3. Kiểm tra path không bị loại trừ
4. Kiểm tra user agent không bị loại trừ

### Performance Issues

1. Giảm `GUEST_CACHE_DURATION` nếu cache quá lâu
2. Tăng `auto_cleanup.probability` để dọn dẹp thường xuyên hơn
3. Bật compression nếu file cache lớn

### Memory Issues

1. Giảm `auto_cleanup.max_files_per_cleanup`
2. Chạy `php artisan guest-cache clear` định kỳ
3. Cấu hình cron job để dọn dẹp tự động

## Cron Job

Thêm vào crontab để dọn dẹp cache định kỳ:

```bash
# Dọn dẹp cache hết hạn mỗi giờ
0 * * * * cd /path/to/project && php artisan guest-cache clear-expired

# Dọn dẹp tất cả cache mỗi ngày
0 0 * * * cd /path/to/project && php artisan guest-cache clear
```

## Security

- Cache files được lưu trong `public/` nên có thể truy cập trực tiếp
- Không cache các trang có thông tin nhạy cảm
- Luôn loại trừ admin paths và API endpoints
- Kiểm tra user agents để tránh cache cho bots

## Image Optimization

### Responsive Images Support

Hệ thống hỗ trợ tối ưu hóa cho nhiều loại hình ảnh:

#### 1. YouTube Thumbnails
```php
// Tự động tạo srcset với multiple resolutions
$imageUrl = 'https://i.ytimg.com/vi/VIDEO_ID/hqdefault.jpg';
// Generates: mqdefault.jpg (320w), hqdefault.jpg (480w), sddefault.jpg (640w), maxresdefault.jpg (1280w)
```

#### 2. Local Images
```php
// Hỗ trợ multiple sizes nếu có
$imageUrl = '/assets/img/sample.jpg';
// Looks for: sample-small.jpg, sample-medium.jpg, sample-large.jpg
```

#### 3. External Images
```php
// Giữ nguyên URL gốc với responsive sizes
$imageUrl = 'https://example.com/image.jpg';
```

### Sử dụng Responsive Image Component

```blade
<x-responsive-image
    :src="$imageUrl"
    alt="Description"
    class="img-fluid"
    width="140"
    height="200"
    :critical="$loop->first"
    sizes="(max-width: 576px) 280px, (max-width: 768px) 360px, 480px"
/>
```

### ImageHelper Utility

```php
use App\Helpers\ImageHelper;

// Detect image type
$type = ImageHelper::detectImageType($imageUrl); // 'youtube', 'local', 'external'

// Get optimized URL for specific size
$optimizedUrl = ImageHelper::getOptimizedImageUrl($imageUrl, 'medium');

// Generate responsive attributes
$attributes = ImageHelper::getResponsiveAttributes($imageUrl, true, false);
```

## Performance Metrics

Với cấu hình mặc định, middleware có thể:

- Giảm 60-80% thời gian response cho guest users
- Giảm 30-50% kích thước HTML nhờ minification
- Giảm 40-50% LCP time nhờ image optimization
- Giảm tải cho database và server
- Cải thiện Core Web Vitals scores
