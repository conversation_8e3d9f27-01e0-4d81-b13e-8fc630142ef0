<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

class SEOService
{
    /**
     * Get SEO meta data for specific page
     * 
     * @param string $page
     * @param array $data
     * @return array
     */
    public static function getMetaData($page = 'default', $data = [])
    {
        $cacheKey = "seo_meta_{$page}";
        
        return Cache::remember($cacheKey, 3600, function () use ($page, $data) {
            return self::generateMetaData($page, $data);
        });
    }
    
    /**
     * Generate meta data based on page type
     * 
     * @param string $page
     * @param array $data
     * @return array
     */
    private static function generateMetaData($page, $data = [])
    {
        $baseTitle = config('variables.templateName', 'PPresent');
        $baseSuffix = config('variables.templateSuffix', 'Software Development');
        
        switch ($page) {
            case 'software':
                return [
                    'title' => 'Software Collection - ' . $baseTitle,
                    'description' => 'Discover our comprehensive collection of professional software tools and applications. Download premium software solutions for development, productivity, and business needs.',
                    'keywords' => 'software download, programming tools, development software, applications, premium software, developer tools, productivity apps',
                    'canonical' => url('/software/all'),
                    'og_type' => 'website',
                    'og_image' => asset('assets/img/software-collection.jpg'),
                ];
                
            case 'software_detail':
                $software = $data['software'] ?? null;
                if ($software) {
                    return [
                        'title' => $software->name . ' - Download & Features - ' . $baseTitle,
                        'description' => $software->description ? strip_tags($software->description) : 'Download ' . $software->name . ' - Professional software solution with advanced features and reliable performance.',
                        'keywords' => $software->tags . ', software download, ' . $software->name . ', programming tools',
                        'canonical' => url('/software/' . $software->id),
                        'og_type' => 'article',
                        'og_image' => asset($software->image),
                    ];
                }
                break;
                
            case 'hotmail':
                return [
                    'title' => 'Hotmail Accounts - ' . $baseTitle,
                    'description' => 'Access premium Hotmail accounts and email solutions. Secure, reliable email services for personal and business use.',
                    'keywords' => 'hotmail accounts, email services, premium email, secure email, business email solutions',
                    'canonical' => url('/hotmail'),
                    'og_type' => 'website',
                    'og_image' => asset('assets/img/hotmail-services.jpg'),
                ];
                
            case 'home':
                return [
                    'title' => $baseTitle . ' - ' . $baseSuffix,
                    'description' => 'PPresent - Your premier destination for professional software development tools, applications, and digital solutions. Discover premium software, development resources, and innovative technology solutions.',
                    'keywords' => 'software development, programming tools, developer platform, applications, PPresent, software solutions, development tools, coding platform, premium software',
                    'canonical' => url('/'),
                    'og_type' => 'website',
                    'og_image' => asset('assets/img/ppresent-og.jpg'),
                ];
                
            default:
                return [
                    'title' => $baseTitle . ' - ' . $baseSuffix,
                    'description' => config('variables.templateDescription', 'Professional software development platform offering premium tools and solutions.'),
                    'keywords' => config('variables.templateKeyword', 'software development, programming tools, developer platform'),
                    'canonical' => url()->current(),
                    'og_type' => 'website',
                    'og_image' => asset('assets/img/ppresent-default.jpg'),
                ];
        }
        
        // Fallback
        return self::generateMetaData('default', $data);
    }
    
    /**
     * Generate structured data (JSON-LD)
     * 
     * @param string $type
     * @param array $data
     * @return array
     */
    public static function getStructuredData($type = 'website', $data = [])
    {
        $baseData = [
            '@context' => 'https://schema.org',
            'url' => url()->current(),
            'name' => config('variables.templateName', 'PPresent'),
            'description' => config('variables.templateDescription', 'Professional software development platform'),
        ];
        
        switch ($type) {
            case 'website':
                return array_merge($baseData, [
                    '@type' => 'WebSite',
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => url('/search?q={search_term_string}'),
                        'query-input' => 'required name=search_term_string'
                    ]
                ]);
                
            case 'software':
                $software = $data['software'] ?? null;
                if ($software) {
                    return [
                        '@context' => 'https://schema.org',
                        '@type' => 'SoftwareApplication',
                        'name' => $software->name,
                        'description' => strip_tags($software->description ?? ''),
                        'url' => url('/software/' . $software->id),
                        'image' => asset($software->image),
                        'applicationCategory' => 'DeveloperApplication',
                        'operatingSystem' => 'Windows, macOS, Linux',
                        'offers' => [
                            '@type' => 'Offer',
                            'price' => $software->is_free ? '0' : ($software->price_1_m ?? '0'),
                            'priceCurrency' => 'USD',
                            'availability' => 'https://schema.org/InStock'
                        ]
                    ];
                }
                break;
                
            case 'organization':
                return [
                    '@context' => 'https://schema.org',
                    '@type' => 'Organization',
                    'name' => config('variables.templateName', 'PPresent'),
                    'url' => url('/'),
                    'logo' => asset('assets/img/logo.png'),
                    'description' => config('variables.templateDescription', 'Professional software development platform'),
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'contactType' => 'customer service',
                        'url' => config('variables.support', 'https://t.me/present2k')
                    ]
                ];
                
            case 'breadcrumb':
                $breadcrumbs = $data['breadcrumbs'] ?? [];
                if (!empty($breadcrumbs)) {
                    $itemList = [];
                    foreach ($breadcrumbs as $index => $breadcrumb) {
                        $itemList[] = [
                            '@type' => 'ListItem',
                            'position' => $index + 1,
                            'name' => $breadcrumb['name'],
                            'item' => $breadcrumb['url']
                        ];
                    }
                    
                    return [
                        '@context' => 'https://schema.org',
                        '@type' => 'BreadcrumbList',
                        'itemListElement' => $itemList
                    ];
                }
                break;
        }
        
        return $baseData;
    }
    
    /**
     * Clear SEO cache
     * 
     * @return void
     */
    public static function clearCache()
    {
        $pages = ['default', 'software', 'hotmail', 'home'];
        
        foreach ($pages as $page) {
            Cache::forget("seo_meta_{$page}");
        }
    }
}
