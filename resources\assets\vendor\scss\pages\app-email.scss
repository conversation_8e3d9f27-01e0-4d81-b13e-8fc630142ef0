// * App Email
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_components/include';
@import '../_custom-variables/pages';

$email-sidebar-width: 16.25rem;
$email-app-height: calc(100vh - 12rem);
$email-app-height-with-no-navbar: calc(100vh - 8.5rem);
$email-app-horizontal-height-diff: 2rem;
$email-list-item-meta-padding-left: 2rem;
$email-view-width: 16.2rem;
$email-padding-x: 1rem;
$email-padding-y: 0.875rem;
$email-filter-padding-y: 0.375rem;
$email-filter-padding-x: 1.5rem;

.app-email {
  position: relative;
  height: $email-app-height !important;
  overflow: hidden;
  @include light.media-breakpoint-down('md') {
    height: calc(100vh - 11rem) !important;
  }
  .layout-navbar-hidden & {
    height: $email-app-height-with-no-navbar !important;
  }
  .layout-horizontal & {
    height: calc($email-app-height + calc($email-app-horizontal-height-diff / 2.66)) !important;
    @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
      height: calc($email-app-height - $email-app-horizontal-height-diff) !important;
    }
  }

  // Email sidebar
  .app-email-sidebar {
    position: absolute;
    left: calc(-#{$email-sidebar-width} - 1.2rem);
    width: $email-sidebar-width;
    height: 100%;
    z-index: 4;
    flex-basis: $email-sidebar-width;
    transition: all 0.2s;

    .btn-compost-wrapper {
      padding: 1.5rem;
    }

    &.show {
      left: 0;
    }

    .email-filters {
      height: calc(100vh - 16.6rem);
      .layout-navbar-hidden & {
        height: calc(100vh - 12.6rem);
      }
      .layout-horizontal & {
        height: calc(100vh - 15.6rem);
        @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
          height: calc(100vh - 16.6rem - $email-app-horizontal-height-diff);
        }
      }

      .email-filter-folders {
        li {
          // margin: $email-filter-spacer 0;
          &:first-child {
            margin-top: 0;
          }
          &:last-child {
            margin-bottom: 0;
          }
          &.active {
            border-color: light.$primary;
          }
        }
      }
      li {
        padding: $email-filter-padding-y $email-filter-padding-x;
        padding-inline-start: calc($email-filter-padding-x - 3px);
        border-left: 3px solid transparent;

        h6 {
          font-size: 1rem;
        }
      }
    }

    .email-filter-labels .badge-dot {
      width: 11px;
      height: 11px;
    }
  }

  // Email compose
  .app-email-compose {
    .modal-dialog {
      position: fixed;
      bottom: 0;
      right: 0;
      width: 100%;
    }
    .email-compose-to {
      .select2-selection {
        border: none;
        padding-inline: calc(light.$form-select-padding-x - light.$input-border-width);
        .select2-selection__choice {
          padding-top: 0.2rem;
          padding-bottom: 0.2rem;
          display: flex;
          align-items: center;
          line-height: 0;
        }
        &__choice__remove {
          top: unset;
        }
      }
      .select2-container--default {
        &.select2-container--focus {
          .select2-search--inline {
            .select2-search__field {
              margin-top: 6px;
            }
          }
        }
      }
    }

    .email-compose-toggle-wrapper {
      width: 80px;
    }

    .ql-editor {
      height: 10rem;
      min-height: 10rem;
      padding-inline: $email-filter-padding-x + 0.25;
    }
    .ql-snow.ql-toolbar {
      padding: 0.5rem 1rem;
    }
    .ql-editor.ql-blank {
      padding-inline: 1.5rem;
      &::before {
        padding-left: 1.5rem;
      }
    }
  }

  // Email list
  .app-emails-list {
    .emails-list-header {
      .emails-list-header-hr {
        margin-top: 0rem;
      }
    }
    .email-list {
      @include light.media-breakpoint-up(lg) {
        height: calc(100vh - 19rem);
      }
      @include light.media-breakpoint-down(lg) {
        height: calc(100vh - 19rem);
      }
      @include light.media-breakpoint-down(md) {
        height: calc(100vh - 18rem);
      }
      .layout-navbar-hidden & {
        @include light.media-breakpoint-up(lg) {
          height: calc(100vh - 15.5rem);
        }
        @include light.media-breakpoint-down(lg) {
          height: calc(100vh - 15.5rem);
        }
      }
      .layout-horizontal & {
        height: calc(100vh - 18.2rem);
        @include light.media-breakpoint-up(lg) {
          height: calc(100vh - 17.75rem);
        }
        @include light.media-breakpoint-up(xl) {
          height: calc(100vh - 18.75rem - $email-app-horizontal-height-diff) !important;
        }
      }

      li.email-list-item {
        padding: $email-padding-y $email-padding-x;
        min-height: 4.375rem;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
        z-index: 1;

        .email-list-item-username {
          font-weight: light.$font-weight-medium;
        }
        .email-list-item-time {
          width: 60px;
          display: inline-block;
          text-align: right;
        }

        .email-list-item-meta {
          .email-list-item-actions {
            display: none;

            li {
              padding: 0;
            }
          }
        }
        .list-inline-item {
          &:not(:last-child) {
            margin-inline-end: 0.25rem;
          }
        }

        &.email-list-item:not(.list-inline-item):hover {
          z-index: 5;
          transform: translateY(-2px);
        }
      }
    }
  }

  // Email view
  .app-email-view {
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: $email-app-height;
    z-index: -1;
    transition: all 0.25s ease;
    @include light.media-breakpoint-down(md) {
      height: calc($email-app-height + 2rem);
    }
    .layout-navbar-hidden & {
      height: $email-app-height-with-no-navbar;
    }
    .layout-horizontal & {
      height: calc($email-app-height + 1rem);
      @include light.media-breakpoint-up(lg) {
        height: calc($email-app-height + 1rem);
      }
      @include light.media-breakpoint-up(xl) {
        height: calc($email-app-height - $email-app-horizontal-height-diff) !important;
      }
    }

    &.show {
      right: -1px;
      z-index: 4;
    }

    .app-email-view-header {
      padding-bottom: 1.3rem;
    }

    .app-email-view-content {
      @include light.media-breakpoint-up(md) {
        height: calc(100vh - 19.6rem);
      }
      @include light.media-breakpoint-down(md) {
        height: calc(100vh - 17.65rem);
      }
      .layout-horizontal & {
        height: calc(100vh - 18rem);
        @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
          height: calc(100vh - 19.2rem - $email-app-horizontal-height-diff) !important;
        }
      }

      .ql-container {
        border: 0;

        .ql-editor {
          height: 7rem;
          min-height: 7rem;
        }
      }
      .ql-editor,
      .ql-editor.ql-blank::before {
        padding-left: 0.5rem;
      }

      .email-card-prev {
        display: none;
      }

      .email-card-last {
        transition: all 0.25s ease-in-out;

        &:before {
          position: absolute;
          bottom: 1rem;
          left: 2.5rem;
          right: 2.5rem;
          top: -2rem;
          border-radius: 0.375rem;
          z-index: -1;
          content: '';
        }

        &:after {
          position: absolute;
          bottom: 0.5rem;
          left: 1rem;
          right: 1rem;
          top: -1rem;
          border-radius: 0.375rem;
          z-index: -1;
          content: '';
        }

        &.hide-pseudo {
          &:before,
          &:after {
            display: none !important;
          }
        }
      }
    }
  }

  .app-email-compose .email-compose-actions .email-send-btn {
    &::after {
      display: none;
    }
  }

  .email-compose-form {
    .form-control {
      padding: $input-padding-y $input-padding-x;
    }
  }

  // Responsive style
  @media (min-width: 1199px) {
    .email-list li {
      .email-list-item-meta {
        margin-right: 0.45rem;
      }
    }
  }

  @media (min-width: 992px) {
    .app-email-sidebar {
      position: static;
      height: auto;
    }

    .email-list {
      li.email-list-item:hover {
        .email-list-item-meta {
          .email-list-item-attachment,
          .email-list-item-time,
          .email-list-item-label {
            display: none !important;
          }

          .email-list-item-actions {
            display: block;
          }
        }
      }
    }

    .app-email-view {
      width: calc(100% - #{$email-view-width});
    }
  }

  @media (max-width: 576px) {
    .app-emails-list {
      .emails-list-header {
        padding: 1rem;
      }

      .email-list {
        li.email-list-item {
          padding: 1rem;

          .email-list-item-username {
            font-size: 0.85rem;
          }
          .email-list-item-user {
            min-width: auto !important;
            margin-right: 1rem;
          }
        }
      }
    }

    .app-email-view {
      .email-list-item-username {
        font-size: 1rem;
      }
    }
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    .app-email {
      .app-email-sidebar {
        background-color: light.$card-bg;

        ul {
          li {
            &:not(.active) {
              a {
                color: light.$headings-color;
              }
            }
          }
        }
      }

      .email-list {
        li.email-list-item {
          border-bottom: 1px solid light.$border-color;
          .email-list-item-user {
            min-width: 12rem;
          }
          &.email-marked-read {
            background-color: light.$gray-50;
          }

          &:hover {
            box-shadow: light.$box-shadow-sm;
            border-bottom-color: transparent;
          }

          .email-list-item-actions li {
            box-shadow: none;
          }

          &[data-starred='true'] {
            .email-list-item-bookmark {
              color: light.$warning;
            }
          }
        }
      }

      .app-email-view {
        .email-card-last {
          border: 1px solid light.$card-border-color;
          &:before {
            background-color: rgba(light.$card-bg, 0.4);
            box-shadow: light.$card-box-shadow;
            border: 1px solid light.$card-border-color;
          }

          &:after {
            background-color: rgba(light.$card-bg, 0.7);
            box-shadow: light.$card-box-shadow;
            border: 1px solid light.$card-border-color;
          }
        }
        .email-reply {
          border: 1px solid light.$card-border-color;
        }
        .email-attachment-title {
          font-weight: 500;
        }
      }
      .app-email-compose .modal-dialog {
        .modal-header {
          background-color: light.$gray-50;
        }
        .modal-content {
          box-shadow: light.$box-shadow-xl;
        }
      }
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    .app-email {
      .app-email-sidebar {
        background-color: dark.$card-bg;
        ul {
          li {
            &:not(.active) {
              a {
                color: dark.$headings-color;
              }
            }
          }
        }
      }

      .emails-list-header {
        .email-search-input,
        .input-group-text {
          background-color: transparent;
        }
      }

      .email-list {
        li.email-list-item {
          border-bottom: 1px solid dark.$border-color;
          &.email-marked-read {
            background-color: dark.$gray-50;
          }

          &:hover {
            box-shadow: dark.$box-shadow-sm;
            border-bottom-color: transparent;
          }

          .email-list-item-actions li {
            box-shadow: none;
          }
          &[data-starred='true'] {
            .email-list-item-bookmark {
              color: dark.$warning;
            }
          }
        }
      }

      .app-email-view {
        .email-card-last {
          border: 1px solid dark.$card-border-color;
          &:before {
            background-color: rgba(dark.$card-bg, 0.4);
            box-shadow: dark.$card-box-shadow;
            border: 1px solid dark.$card-border-color;
          }

          &:after {
            background-color: rgba(dark.$card-bg, 0.7);
            box-shadow: dark.$card-box-shadow;
            border: 1px solid dark.$card-border-color;
          }
        }
        .email-reply {
          border: 1px solid dark.$card-border-color;
        }
        .app-email-view-header {
          background-color: dark.$card-bg;
        }
      }
      .app-email-compose .modal-dialog {
        .modal-header {
          background-color: dark.$gray-50;
        }
        .modal-content {
          box-shadow: dark.$box-shadow-xl;
        }
      }
    }
  }
}

// RTL
@if $enable-rtl-support {
  [dir='rtl'] {
    .app-email {
      .app-emails-list {
        .emails-list-header {
          .dropdown-menu-end {
            right: auto !important;
            left: 0 !important;
          }
        }
      }

      .app-email-sidebar {
        right: calc(-#{$email-sidebar-width} - 1.2rem);
        left: auto;
        &.show {
          right: 0;
          left: auto;
        }

        .email-filters {
          li {
            border-left: 0;
            border-right: 3px solid transparent;
          }
        }
      }

      .app-email-compose {
        .modal-dialog {
          left: 0;
          right: auto;
        }
      }

      .app-email-view {
        right: auto;
        left: -100%;

        &.show {
          right: auto;
          left: -1px;
        }

        .app-email-view-header {
          .ti-chevron-right,
          .ti-chevron-left {
            transform: rotate(180deg);
          }
        }
      }
    }

    @media (min-width: 1199px) {
      .email-list li {
        .email-list-item-meta {
          text-align: left;
        }
      }
    }

    @media (max-width: 576px) {
      .app-emails-list {
        .email-list {
          li.email-list-item {
            .email-list-item-user {
              margin-left: 1rem;
            }
          }
        }
      }
    }
  }
}
