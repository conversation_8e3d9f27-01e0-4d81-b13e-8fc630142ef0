import{c as zi,g as <PERSON>}from"./_commonjsHelpers-BosuxZz1.js";var zt={exports:{}};/*! shepherd.js 11.2.0 */(function(st,Gi){(function(rt,Be){st.exports=Be()})(zi,function(){var rt=function(t){return Be(t)&&!Kt(t)};function Be(e){return!!e&&typeof e=="object"}function Kt(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||Gt(e)}var Xt=typeof Symbol=="function"&&Symbol.for,$t=Xt?Symbol.for("react.element"):60103;function Gt(e){return e.$$typeof===$t}function Zt(e){return Array.isArray(e)?[]:{}}function xe(e,t){return t.clone!==!1&&t.isMergeableObject(e)?le(Zt(e),e,t):e}function Jt(e,t,n){return e.concat(t).map(function(i){return xe(i,n)})}function Qt(e,t){if(!t.customMerge)return le;var n=t.customMerge(e);return typeof n=="function"?n:le}function en(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function lt(e){return Object.keys(e).concat(en(e))}function ct(e,t){try{return t in e}catch{return!1}}function tn(e,t){return ct(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function nn(e,t,n){var i={};return n.isMergeableObject(e)&&lt(e).forEach(function(o){i[o]=xe(e[o],n)}),lt(t).forEach(function(o){tn(e,o)||(ct(e,o)&&n.isMergeableObject(t[o])?i[o]=Qt(o,n)(e[o],t[o],n):i[o]=xe(t[o],n))}),i}function le(e,t,n){n=n||{},n.arrayMerge=n.arrayMerge||Jt,n.isMergeableObject=n.isMergeableObject||rt,n.cloneUnlessOtherwiseSpecified=xe;var i=Array.isArray(t),o=Array.isArray(e),r=i===o;return r?i?n.arrayMerge(e,t,n):nn(e,t,n):xe(t,n)}le.all=function(t,n){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(i,o){return le(i,o,n)},{})};var on=le,Ve=on;function sn(e){return e instanceof Element}function We(e){return e instanceof HTMLElement}function G(e){return typeof e=="function"}function ve(e){return typeof e=="string"}function A(e){return e===void 0}class Ue{on(t,n,i,o=!1){return A(this.bindings)&&(this.bindings={}),A(this.bindings[t])&&(this.bindings[t]=[]),this.bindings[t].push({handler:n,ctx:i,once:o}),this}once(t,n,i){return this.on(t,n,i,!0)}off(t,n){return A(this.bindings)||A(this.bindings[t])?this:(A(n)?delete this.bindings[t]:this.bindings[t].forEach((i,o)=>{i.handler===n&&this.bindings[t].splice(o,1)}),this)}trigger(t,...n){return!A(this.bindings)&&this.bindings[t]&&this.bindings[t].forEach((i,o)=>{const{ctx:r,handler:s,once:l}=i,c=r||this;s.apply(c,n),l&&this.bindings[t].splice(o,1)}),this}}function at(e){const t=Object.getOwnPropertyNames(e.constructor.prototype);for(let n=0;n<t.length;n++){const i=t[n],o=e[i];i!=="constructor"&&typeof o=="function"&&(e[i]=o.bind(e))}return e}function rn(e,t){return n=>{if(t.isOpen()){const i=t.el&&n.currentTarget===t.el;(!A(e)&&n.currentTarget.matches(e)||i)&&t.tour.next()}}}function ln(e){const{event:t,selector:n}=e.options.advanceOn||{};if(t){const i=rn(n,e);let o;try{o=document.querySelector(n)}catch{}if(!A(n)&&!o)return console.error(`No element was found for the selector supplied to advanceOn: ${n}`);o?(o.addEventListener(t,i),e.on("destroy",()=>o.removeEventListener(t,i))):(document.body.addEventListener(t,i,!0),e.on("destroy",()=>document.body.removeEventListener(t,i,!0)))}else return console.error("advanceOn was defined, but no event name was passed.")}function ft(e){return!ve(e)||e===""?"":e.charAt(e.length-1)!=="-"?`${e}-`:e}function cn(e){const t=e.options.attachTo||{},n=Object.assign({},t);if(G(n.element)&&(n.element=n.element.call(e)),ve(n.element)){try{n.element=document.querySelector(n.element)}catch{}n.element||console.error(`The element for this Shepherd step was not found ${t.element}`)}return n}function ut(e){return e==null?!0:!e.element||!e.on}function Ye(){let e=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const n=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(t=="x"?n:n&3|8).toString(16)})}function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},T.apply(this,arguments)}function ht(e,t){if(e==null)return{};var n={},i=Object.keys(e),o,r;for(r=0;r<i.length;r++)o=i[r],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}const ce=Math.min,Z=Math.max,Ce=Math.round,Ie=Math.floor,z=e=>({x:e,y:e}),an={left:"right",right:"left",bottom:"top",top:"bottom"},fn={start:"end",end:"start"};function qe(e,t,n){return Z(e,ce(t,n))}function ae(e,t){return typeof e=="function"?e(t):e}function J(e){return e.split("-")[0]}function Le(e){return e.split("-")[1]}function ze(e){return e==="x"?"y":"x"}function Ke(e){return e==="y"?"height":"width"}function Pe(e){return["top","bottom"].includes(J(e))?"y":"x"}function Xe(e){return ze(Pe(e))}function un(e,t,n){n===void 0&&(n=!1);const i=Le(e),o=Xe(e),r=Ke(o);let s=o==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(s=Re(s)),[s,Re(s)]}function hn(e){const t=Re(e);return[$e(e),t,$e(t)]}function $e(e){return e.replace(/start|end/g,t=>fn[t])}function dn(e,t,n){const i=["left","right"],o=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:i:t?i:o;case"left":case"right":return t?r:s;default:return[]}}function pn(e,t,n,i){const o=Le(e);let r=dn(J(e),n==="start",i);return o&&(r=r.map(s=>s+"-"+o),t&&(r=r.concat(r.map($e)))),r}function Re(e){return e.replace(/left|right|bottom|top/g,t=>an[t])}function mn(e){return T({top:0,right:0,bottom:0,left:0},e)}function dt(e){return typeof e!="number"?mn(e):{top:e,right:e,bottom:e,left:e}}function ke(e){return T({},e,{top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height})}const gn=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],bn=["mainAxis","crossAxis","limiter"];function pt(e,t,n){let{reference:i,floating:o}=e;const r=Pe(t),s=Xe(t),l=Ke(s),c=J(t),a=r==="y",u=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,f=i[l]/2-o[l]/2;let p;switch(c){case"top":p={x:u,y:i.y-o.height};break;case"bottom":p={x:u,y:i.y+i.height};break;case"right":p={x:i.x+i.width,y:h};break;case"left":p={x:i.x-o.width,y:h};break;default:p={x:i.x,y:i.y}}switch(Le(t)){case"start":p[s]-=f*(n&&a?-1:1);break;case"end":p[s]+=f*(n&&a?-1:1);break}return p}const yn=async(e,t,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:r=[],platform:s}=n,l=r.filter(Boolean),c=await(s.isRTL==null?void 0:s.isRTL(t));let a=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:h}=pt(a,i,c),f=i,p={},m=0;for(let b=0;b<l.length;b++){const{name:w,fn:y}=l[b],{x,y:O,data:g,reset:d}=await y({x:u,y:h,initialPlacement:i,placement:f,strategy:o,middlewareData:p,rects:a,platform:s,elements:{reference:e,floating:t}});if(u=x??u,h=O??h,p=T({},p,{[w]:T({},p[w],g)}),d&&m<=50){m++,typeof d=="object"&&(d.placement&&(f=d.placement),d.rects&&(a=d.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):d.rects),{x:u,y:h}=pt(a,f,c)),b=-1;continue}}return{x:u,y:h,placement:f,strategy:o,middlewareData:p}};async function mt(e,t){var n;t===void 0&&(t={});const{x:i,y:o,platform:r,rects:s,elements:l,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:h="floating",altBoundary:f=!1,padding:p=0}=ae(t,e),m=dt(p),w=l[f?h==="floating"?"reference":"floating":h],y=ke(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(w)))==null||n?w:w.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:a,rootBoundary:u,strategy:c})),x=h==="floating"?T({},s.floating,{x:i,y:o}):s.reference,O=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),g=await(r.isElement==null?void 0:r.isElement(O))?await(r.getScale==null?void 0:r.getScale(O))||{x:1,y:1}:{x:1,y:1},d=ke(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({rect:x,offsetParent:O,strategy:c}):x);return{top:(y.top-d.top+m.top)/g.y,bottom:(d.bottom-y.bottom+m.bottom)/g.y,left:(y.left-d.left+m.left)/g.x,right:(d.right-y.right+m.right)/g.x}}const wn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:i,placement:o,rects:r,platform:s,elements:l}=t,{element:c,padding:a=0}=ae(e,t)||{};if(c==null)return{};const u=dt(a),h={x:n,y:i},f=Xe(o),p=Ke(f),m=await s.getDimensions(c),b=f==="y",w=b?"top":"left",y=b?"bottom":"right",x=b?"clientHeight":"clientWidth",O=r.reference[p]+r.reference[f]-h[f]-r.floating[p],g=h[f]-r.reference[f],d=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c));let _=d?d[x]:0;(!_||!await(s.isElement==null?void 0:s.isElement(d)))&&(_=l.floating[x]||r.floating[p]);const I=O/2-g/2,M=_/2-m[p]/2-1,H=ce(u[w],M),se=ce(u[y],M),Y=H,re=_-m[p]-se,C=_/2-m[p]/2+I,ye=qe(Y,C,re),q=Le(o)!=null&&C!=ye&&r.reference[p]/2-(C<Y?H:se)-m[p]/2<0?C<Y?Y-C:re-C:0;return{[f]:h[f]-q,data:{[f]:ye,centerOffset:C-ye+q}}}}),_n=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(n){var i;const{placement:o,middlewareData:r,rects:s,initialPlacement:l,platform:c,elements:a}=n,u=ae(t,n),{mainAxis:h=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:w=!0}=u,y=ht(u,gn),x=J(o),O=J(l)===l,g=await(c.isRTL==null?void 0:c.isRTL(a.floating)),d=p||(O||!w?[Re(l)]:hn(l));!p&&b!=="none"&&d.push(...pn(l,w,b,g));const _=[l,...d],I=await mt(n,y),M=[];let H=((i=r.flip)==null?void 0:i.overflows)||[];if(h&&M.push(I[x]),f){const C=un(o,s,g);M.push(I[C[0]],I[C[1]])}if(H=[...H,{placement:o,overflows:M}],!M.every(C=>C<=0)){var se,Y;const C=(((se=r.flip)==null?void 0:se.index)||0)+1,ye=_[C];if(ye)return{data:{index:C,overflows:H},reset:{placement:ye}};let we=(Y=H.filter(q=>q.overflows[0]<=0).sort((q,_e)=>q.overflows[1]-_e.overflows[1])[0])==null?void 0:Y.placement;if(!we)switch(m){case"bestFit":{var re;const q=(re=H.map(_e=>[_e.placement,_e.overflows.filter(Te=>Te>0).reduce((Te,qi)=>Te+qi,0)]).sort((_e,Te)=>_e[1]-Te[1])[0])==null?void 0:re[0];q&&(we=q);break}case"initialPlacement":we=l;break}if(o!==we)return{reset:{placement:we}}}return{}}}},xn=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(n){const{x:i,y:o,placement:r}=n,s=ae(t,n),{mainAxis:l=!0,crossAxis:c=!1,limiter:a={fn:x=>{let{x:O,y:g}=x;return{x:O,y:g}}}}=s,u=ht(s,bn),h={x:i,y:o},f=await mt(n,u),p=Pe(J(r)),m=ze(p);let b=h[m],w=h[p];if(l){const x=m==="y"?"top":"left",O=m==="y"?"bottom":"right",g=b+f[x],d=b-f[O];b=qe(g,b,d)}if(c){const x=p==="y"?"top":"left",O=p==="y"?"bottom":"right",g=w+f[x],d=w-f[O];w=qe(g,w,d)}const y=a.fn(T({},n,{[m]:b,[p]:w}));return T({},y,{data:{x:y.x-i,y:y.y-o}})}}},vn=function(t){return t===void 0&&(t={}),{options:t,fn(n){const{x:i,y:o,placement:r,rects:s,middlewareData:l}=n,{offset:c=0,mainAxis:a=!0,crossAxis:u=!0}=ae(t,n),h={x:i,y:o},f=Pe(r),p=ze(f);let m=h[p],b=h[f];const w=ae(c,n),y=typeof w=="number"?{mainAxis:w,crossAxis:0}:T({mainAxis:0,crossAxis:0},w);if(a){const g=p==="y"?"height":"width",d=s.reference[p]-s.floating[g]+y.mainAxis,_=s.reference[p]+s.reference[g]-y.mainAxis;m<d?m=d:m>_&&(m=_)}if(u){var x,O;const g=p==="y"?"width":"height",d=["top","left"].includes(J(r)),_=s.reference[f]-s.floating[g]+(d&&((x=l.offset)==null?void 0:x[f])||0)+(d?0:y.crossAxis),I=s.reference[f]+s.reference[g]+(d?0:((O=l.offset)==null?void 0:O[f])||0)-(d?y.crossAxis:0);b<_?b=_:b>I&&(b=I)}return{[p]:m,[f]:b}}}};function K(e){return gt(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function N(e){var t;return(t=(gt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function gt(e){return e instanceof Node||e instanceof L(e).Node}function B(e){return e instanceof Element||e instanceof L(e).Element}function F(e){return e instanceof HTMLElement||e instanceof L(e).HTMLElement}function bt(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof L(e).ShadowRoot}function Oe(e){const{overflow:t,overflowX:n,overflowY:i,display:o}=R(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(o)}function On(e){return["table","td","th"].includes(K(e))}function Ge(e){const t=Ze(),n=R(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(i=>(n.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(n.contain||"").includes(i))}function En(e){let t=fe(e);for(;F(t)&&!Me(t);){if(Ge(t))return t;t=fe(t)}return null}function Ze(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Me(e){return["html","body","#document"].includes(K(e))}function R(e){return L(e).getComputedStyle(e)}function je(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function fe(e){if(K(e)==="html")return e;const t=e.assignedSlot||e.parentNode||bt(e)&&e.host||N(e);return bt(t)?t.host:t}function yt(e){const t=fe(e);return Me(t)?e.ownerDocument?e.ownerDocument.body:e.body:F(t)&&Oe(t)?t:yt(t)}function Fe(e,t){var n;t===void 0&&(t=[]);const i=yt(e),o=i===((n=e.ownerDocument)==null?void 0:n.body),r=L(i);return o?t.concat(r,r.visualViewport||[],Oe(i)?i:[]):t.concat(i,Fe(i))}function wt(e){const t=R(e);let n=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const o=F(e),r=o?e.offsetWidth:n,s=o?e.offsetHeight:i,l=Ce(n)!==r||Ce(i)!==s;return l&&(n=r,i=s),{width:n,height:i,$:l}}function Je(e){return B(e)?e:e.contextElement}function ue(e){const t=Je(e);if(!F(t))return z(1);const n=t.getBoundingClientRect(),{width:i,height:o,$:r}=wt(t);let s=(r?Ce(n.width):n.width)/i,l=(r?Ce(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const Sn=z(0);function _t(e){const t=L(e);return!Ze()||!t.visualViewport?Sn:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function An(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==L(e)?!1:t}function Q(e,t,n,i){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=Je(e);let s=z(1);t&&(i?B(i)&&(s=ue(i)):s=ue(e));const l=An(r,n,i)?_t(r):z(0);let c=(o.left+l.x)/s.x,a=(o.top+l.y)/s.y,u=o.width/s.x,h=o.height/s.y;if(r){const f=L(r),p=i&&B(i)?L(i):i;let m=f.frameElement;for(;m&&i&&p!==f;){const b=ue(m),w=m.getBoundingClientRect(),y=R(m),x=w.left+(m.clientLeft+parseFloat(y.paddingLeft))*b.x,O=w.top+(m.clientTop+parseFloat(y.paddingTop))*b.y;c*=b.x,a*=b.y,u*=b.x,h*=b.y,c+=x,a+=O,m=L(m).frameElement}}return ke({width:u,height:h,x:c,y:a})}function Tn(e){let{rect:t,offsetParent:n,strategy:i}=e;const o=F(n),r=N(n);if(n===r)return t;let s={scrollLeft:0,scrollTop:0},l=z(1);const c=z(0);if((o||!o&&i!=="fixed")&&((K(n)!=="body"||Oe(r))&&(s=je(n)),F(n))){const a=Q(n);l=ue(n),c.x=a.x+n.clientLeft,c.y=a.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-s.scrollLeft*l.x+c.x,y:t.y*l.y-s.scrollTop*l.y+c.y}}function Cn(e){return Array.from(e.getClientRects())}function xt(e){return Q(N(e)).left+je(e).scrollLeft}function In(e){const t=N(e),n=je(e),i=e.ownerDocument.body,o=Z(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),r=Z(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let s=-n.scrollLeft+xt(e);const l=-n.scrollTop;return R(i).direction==="rtl"&&(s+=Z(t.clientWidth,i.clientWidth)-o),{width:o,height:r,x:s,y:l}}function Ln(e,t){const n=L(e),i=N(e),o=n.visualViewport;let r=i.clientWidth,s=i.clientHeight,l=0,c=0;if(o){r=o.width,s=o.height;const a=Ze();(!a||a&&t==="fixed")&&(l=o.offsetLeft,c=o.offsetTop)}return{width:r,height:s,x:l,y:c}}function Pn(e,t){const n=Q(e,!0,t==="fixed"),i=n.top+e.clientTop,o=n.left+e.clientLeft,r=F(e)?ue(e):z(1),s=e.clientWidth*r.x,l=e.clientHeight*r.y,c=o*r.x,a=i*r.y;return{width:s,height:l,x:c,y:a}}function vt(e,t,n){let i;if(t==="viewport")i=Ln(e,n);else if(t==="document")i=In(N(e));else if(B(t))i=Pn(t,n);else{const o=_t(e);i=T({},t,{x:t.x-o.x,y:t.y-o.y})}return ke(i)}function Ot(e,t){const n=fe(e);return n===t||!B(n)||Me(n)?!1:R(n).position==="fixed"||Ot(n,t)}function Rn(e,t){const n=t.get(e);if(n)return n;let i=Fe(e).filter(l=>B(l)&&K(l)!=="body"),o=null;const r=R(e).position==="fixed";let s=r?fe(e):e;for(;B(s)&&!Me(s);){const l=R(s),c=Ge(s);!c&&l.position==="fixed"&&(o=null),(r?!c&&!o:!c&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Oe(s)&&!c&&Ot(e,s))?i=i.filter(u=>u!==s):o=l,s=fe(s)}return t.set(e,i),i}function kn(e){let{element:t,boundary:n,rootBoundary:i,strategy:o}=e;const s=[...n==="clippingAncestors"?Rn(t,this._c):[].concat(n),i],l=s[0],c=s.reduce((a,u)=>{const h=vt(t,u,o);return a.top=Z(h.top,a.top),a.right=ce(h.right,a.right),a.bottom=ce(h.bottom,a.bottom),a.left=Z(h.left,a.left),a},vt(t,l,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Mn(e){return wt(e)}function jn(e,t,n){const i=F(t),o=N(t),r=n==="fixed",s=Q(e,!0,r,t);let l={scrollLeft:0,scrollTop:0};const c=z(0);if(i||!i&&!r)if((K(t)!=="body"||Oe(o))&&(l=je(t)),i){const a=Q(t,!0,r,t);c.x=a.x+t.clientLeft,c.y=a.y+t.clientTop}else o&&(c.x=xt(o));return{x:s.left+l.scrollLeft-c.x,y:s.top+l.scrollTop-c.y,width:s.width,height:s.height}}function Et(e,t){return!F(e)||R(e).position==="fixed"?null:t?t(e):e.offsetParent}function St(e,t){const n=L(e);if(!F(e))return n;let i=Et(e,t);for(;i&&On(i)&&R(i).position==="static";)i=Et(i,t);return i&&(K(i)==="html"||K(i)==="body"&&R(i).position==="static"&&!Ge(i))?n:i||En(e)||n}const Fn=async function(t){let{reference:n,floating:i,strategy:o}=t;const r=this.getOffsetParent||St,s=this.getDimensions;return{reference:jn(n,await r(i),o),floating:T({x:0,y:0},await s(i))}};function Dn(e){return R(e).direction==="rtl"}const Hn={convertOffsetParentRelativeRectToViewportRelativeRect:Tn,getDocumentElement:N,getClippingRect:kn,getOffsetParent:St,getElementRects:Fn,getClientRects:Cn,getDimensions:Mn,getScale:ue,isElement:B,isRTL:Dn};function Nn(e,t){let n=null,i;const o=N(e);function r(){clearTimeout(i),n&&n.disconnect(),n=null}function s(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),r();const{left:a,top:u,width:h,height:f}=e.getBoundingClientRect();if(l||t(),!h||!f)return;const p=Ie(u),m=Ie(o.clientWidth-(a+h)),b=Ie(o.clientHeight-(u+f)),w=Ie(a),x={rootMargin:-p+"px "+-m+"px "+-b+"px "+-w+"px",threshold:Z(0,ce(1,c))||1};let O=!0;function g(d){const _=d[0].intersectionRatio;if(_!==c){if(!O)return s();_?s(!1,_):i=setTimeout(()=>{s(!1,1e-7)},100)}O=!1}try{n=new IntersectionObserver(g,T({},x,{root:o.ownerDocument}))}catch{n=new IntersectionObserver(g,x)}n.observe(e)}return s(!0),r}function Bn(e,t,n,i){i===void 0&&(i={});const{ancestorScroll:o=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=i,a=Je(e),u=o||r?[...a?Fe(a):[],...Fe(t)]:[];u.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),r&&y.addEventListener("resize",n)});const h=a&&l?Nn(a,n):null;let f=-1,p=null;s&&(p=new ResizeObserver(y=>{let[x]=y;x&&x.target===a&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{p&&p.observe(t)})),n()}),a&&!c&&p.observe(a),p.observe(t));let m,b=c?Q(e):null;c&&w();function w(){const y=Q(e);b&&(y.x!==b.x||y.y!==b.y||y.width!==b.width||y.height!==b.height)&&n(),b=y,m=requestAnimationFrame(w)}return n(),()=>{u.forEach(y=>{o&&y.removeEventListener("scroll",n),r&&y.removeEventListener("resize",n)}),h&&h(),p&&p.disconnect(),p=null,c&&cancelAnimationFrame(m)}}const Vn=(e,t,n)=>{const i=new Map,o=T({platform:Hn},n),r=T({},o.platform,{_c:i});return yn(e,t,T({},o,{platform:r}))};function Wn(e){e.cleanup&&e.cleanup();const t=e._getResolvedAttachToOptions();let n=t.element;const i=Xn(t,e),o=ut(t);return o&&(n=document.body,e.shepherdElementComponent.getElement().classList.add("shepherd-centered")),e.cleanup=Bn(n,e.el,()=>{if(!e.el){e.cleanup();return}qn(n,e,i,o)}),e.target=t.element,i}function Un(e,t){return{floatingUIOptions:Ve(e.floatingUIOptions||{},t.floatingUIOptions||{})}}function Yn(e){e.cleanup&&e.cleanup(),e.cleanup=null}function qn(e,t,n,i){return Vn(e,t.el,n).then(zn(t,i)).then(o=>new Promise(r=>{setTimeout(()=>r(o),300)})).then(o=>{o&&o.el&&o.el.focus({preventScroll:!0})})}function zn(e,t){return({x:n,y:i,placement:o,middlewareData:r})=>(e.el&&(t?Object.assign(e.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(e.el.style,{position:"absolute",left:`${n}px`,top:`${i}px`}),e.el.dataset.popperPlacement=o,Kn(e.el,r)),e)}function Kn(e,t){const n=e.querySelector(".shepherd-arrow");if(n&&t.arrow){const{x:i,y:o}=t.arrow;Object.assign(n.style,{left:i!=null?`${i}px`:"",top:o!=null?`${o}px`:""})}}function Xn(e,t){const n={strategy:"absolute",middleware:[]},i=$n(t);return ut(e)||(n.middleware.push(_n(),xn({limiter:vn(),crossAxis:!0})),i&&n.middleware.push(wn({element:i})),n.placement=e.on),Ve(t.options.floatingUIOptions||{},n)}function $n(e){return e.options.arrow&&e.el?e.el.querySelector(".shepherd-arrow"):!1}function P(){}function Gn(e,t){for(const n in t)e[n]=t[n];return e}function At(e){return e()}function Tt(){return Object.create(null)}function Ee(e){e.forEach(At)}function Qe(e){return typeof e=="function"}function V(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Zn(e){return Object.keys(e).length===0}function he(e,t){e.appendChild(t)}function j(e,t,n){e.insertBefore(t,n||null)}function k(e){e.parentNode&&e.parentNode.removeChild(e)}function Jn(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function D(e){return document.createElement(e)}function Ct(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function It(e){return document.createTextNode(e)}function De(){return It(" ")}function Qn(){return It("")}function He(e,t,n,i){return e.addEventListener(t,n,i),()=>e.removeEventListener(t,n,i)}function E(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}const ei=["width","height"];function Lt(e,t){const n=Object.getOwnPropertyDescriptors(e.__proto__);for(const i in t)t[i]==null?e.removeAttribute(i):i==="style"?e.style.cssText=t[i]:i==="__value"?e.value=e[i]=t[i]:n[i]&&n[i].set&&ei.indexOf(i)===-1?e[i]=t[i]:E(e,i,t[i])}function ti(e){return Array.from(e.childNodes)}function de(e,t,n){e.classList[n?"add":"remove"](t)}let Se;function Ae(e){Se=e}function Pt(){if(!Se)throw new Error("Function called outside component initialization");return Se}function ni(e){Pt().$$.on_mount.push(e)}function et(e){Pt().$$.after_update.push(e)}const pe=[],me=[];let ge=[];const Rt=[],ii=Promise.resolve();let tt=!1;function oi(){tt||(tt=!0,ii.then(kt))}function nt(e){ge.push(e)}const it=new Set;let be=0;function kt(){if(be!==0)return;const e=Se;do{try{for(;be<pe.length;){const t=pe[be];be++,Ae(t),si(t.$$)}}catch(t){throw pe.length=0,be=0,t}for(Ae(null),pe.length=0,be=0;me.length;)me.pop()();for(let t=0;t<ge.length;t+=1){const n=ge[t];it.has(n)||(it.add(n),n())}ge.length=0}while(pe.length);for(;Rt.length;)Rt.pop()();tt=!1,it.clear(),Ae(e)}function si(e){if(e.fragment!==null){e.update(),Ee(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(nt)}}function ri(e){const t=[],n=[];ge.forEach(i=>e.indexOf(i)===-1?t.push(i):n.push(i)),n.forEach(i=>i()),ge=t}const Ne=new Set;let ee;function te(){ee={r:0,c:[],p:ee}}function ne(){ee.r||Ee(ee.c),ee=ee.p}function v(e,t){e&&e.i&&(Ne.delete(e),e.i(t))}function S(e,t,n,i){if(e&&e.o){if(Ne.has(e))return;Ne.add(e),ee.c.push(()=>{Ne.delete(e),i&&(n&&e.d(1),i())}),e.o(t)}else i&&i()}function li(e,t){const n={},i={},o={$$scope:1};let r=e.length;for(;r--;){const s=e[r],l=t[r];if(l){for(const c in s)c in l||(i[c]=1);for(const c in l)o[c]||(n[c]=l[c],o[c]=1);e[r]=l}else for(const c in s)o[c]=1}for(const s in i)s in n||(n[s]=void 0);return n}function ie(e){e&&e.c()}function X(e,t,n,i){const{fragment:o,after_update:r}=e.$$;o&&o.m(t,n),i||nt(()=>{const s=e.$$.on_mount.map(At).filter(Qe);e.$$.on_destroy?e.$$.on_destroy.push(...s):Ee(s),e.$$.on_mount=[]}),r.forEach(nt)}function $(e,t){const n=e.$$;n.fragment!==null&&(ri(n.after_update),Ee(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function ci(e,t){e.$$.dirty[0]===-1&&(pe.push(e),oi(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function W(e,t,n,i,o,r,s,l=[-1]){const c=Se;Ae(e);const a=e.$$={fragment:null,ctx:[],props:r,update:P,not_equal:o,bound:Tt(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(c?c.$$.context:[])),callbacks:Tt(),dirty:l,skip_bound:!1,root:t.target||c.$$.root};let u=!1;if(a.ctx=n?n(e,t.props||{},(h,f,...p)=>{const m=p.length?p[0]:f;return a.ctx&&o(a.ctx[h],a.ctx[h]=m)&&(!a.skip_bound&&a.bound[h]&&a.bound[h](m),u&&ci(e,h)),f}):[],a.update(),u=!0,Ee(a.before_update),a.fragment=i?i(a.ctx):!1,t.target){if(t.hydrate){const h=ti(t.target);a.fragment&&a.fragment.l(h),h.forEach(k)}else a.fragment&&a.fragment.c();t.intro&&v(e.$$.fragment),X(e,t.target,t.anchor,t.customElement),kt()}Ae(c)}class U{$destroy(){$(this,1),this.$destroy=P}$on(t,n){if(!Qe(n))return P;const i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(n),()=>{const o=i.indexOf(n);o!==-1&&i.splice(o,1)}}$set(t){this.$$set&&!Zn(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}function ai(e){let t,n,i,o,r;return{c(){t=D("button"),E(t,"aria-label",n=e[3]?e[3]:null),E(t,"class",i=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`),t.disabled=e[2],E(t,"tabindex","0")},m(s,l){j(s,t,l),t.innerHTML=e[5],o||(r=He(t,"click",function(){Qe(e[0])&&e[0].apply(this,arguments)}),o=!0)},p(s,[l]){e=s,l&32&&(t.innerHTML=e[5]),l&8&&n!==(n=e[3]?e[3]:null)&&E(t,"aria-label",n),l&18&&i!==(i=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`)&&E(t,"class",i),l&4&&(t.disabled=e[2])},i:P,o:P,d(s){s&&k(t),o=!1,r()}}}function fi(e,t,n){let{config:i,step:o}=t,r,s,l,c,a,u;function h(f){return G(f)?f=f.call(o):f}return e.$$set=f=>{"config"in f&&n(6,i=f.config),"step"in f&&n(7,o=f.step)},e.$$.update=()=>{e.$$.dirty&192&&(n(0,r=i.action?i.action.bind(o.tour):null),n(1,s=i.classes),n(2,l=i.disabled?h(i.disabled):!1),n(3,c=i.label?h(i.label):null),n(4,a=i.secondary),n(5,u=i.text?h(i.text):null))},[r,s,l,c,a,u,i,o]}class ui extends U{constructor(t){super(),W(this,t,fi,ai,V,{config:6,step:7})}}function Mt(e,t,n){const i=e.slice();return i[2]=t[n],i}function jt(e){let t,n,i=e[1],o=[];for(let s=0;s<i.length;s+=1)o[s]=Ft(Mt(e,i,s));const r=s=>S(o[s],1,1,()=>{o[s]=null});return{c(){for(let s=0;s<o.length;s+=1)o[s].c();t=Qn()},m(s,l){for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(s,l);j(s,t,l),n=!0},p(s,l){if(l&3){i=s[1];let c;for(c=0;c<i.length;c+=1){const a=Mt(s,i,c);o[c]?(o[c].p(a,l),v(o[c],1)):(o[c]=Ft(a),o[c].c(),v(o[c],1),o[c].m(t.parentNode,t))}for(te(),c=i.length;c<o.length;c+=1)r(c);ne()}},i(s){if(!n){for(let l=0;l<i.length;l+=1)v(o[l]);n=!0}},o(s){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)S(o[l]);n=!1},d(s){Jn(o,s),s&&k(t)}}}function Ft(e){let t,n;return t=new ui({props:{config:e[2],step:e[0]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&2&&(r.config=i[2]),o&1&&(r.step=i[0]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function hi(e){let t,n,i=e[1]&&jt(e);return{c(){t=D("footer"),i&&i.c(),E(t,"class","shepherd-footer")},m(o,r){j(o,t,r),i&&i.m(t,null),n=!0},p(o,[r]){o[1]?i?(i.p(o,r),r&2&&v(i,1)):(i=jt(o),i.c(),v(i,1),i.m(t,null)):i&&(te(),S(i,1,1,()=>{i=null}),ne())},i(o){n||(v(i),n=!0)},o(o){S(i),n=!1},d(o){o&&k(t),i&&i.d()}}}function di(e,t,n){let i,{step:o}=t;return e.$$set=r=>{"step"in r&&n(0,o=r.step)},e.$$.update=()=>{e.$$.dirty&1&&n(1,i=o.options.buttons)},[o,i]}class pi extends U{constructor(t){super(),W(this,t,di,hi,V,{step:0})}}function mi(e){let t,n,i,o,r;return{c(){t=D("button"),n=D("span"),n.textContent="×",E(n,"aria-hidden","true"),E(t,"aria-label",i=e[0].label?e[0].label:"Close Tour"),E(t,"class","shepherd-cancel-icon"),E(t,"type","button")},m(s,l){j(s,t,l),he(t,n),o||(r=He(t,"click",e[1]),o=!0)},p(s,[l]){l&1&&i!==(i=s[0].label?s[0].label:"Close Tour")&&E(t,"aria-label",i)},i:P,o:P,d(s){s&&k(t),o=!1,r()}}}function gi(e,t,n){let{cancelIcon:i,step:o}=t;const r=s=>{s.preventDefault(),o.cancel()};return e.$$set=s=>{"cancelIcon"in s&&n(0,i=s.cancelIcon),"step"in s&&n(2,o=s.step)},[i,r,o]}class bi extends U{constructor(t){super(),W(this,t,gi,mi,V,{cancelIcon:0,step:2})}}function yi(e){let t;return{c(){t=D("h3"),E(t,"id",e[1]),E(t,"class","shepherd-title")},m(n,i){j(n,t,i),e[3](t)},p(n,[i]){i&2&&E(t,"id",n[1])},i:P,o:P,d(n){n&&k(t),e[3](null)}}}function wi(e,t,n){let{labelId:i,element:o,title:r}=t;et(()=>{G(r)&&n(2,r=r()),n(0,o.innerHTML=r,o)});function s(l){me[l?"unshift":"push"](()=>{o=l,n(0,o)})}return e.$$set=l=>{"labelId"in l&&n(1,i=l.labelId),"element"in l&&n(0,o=l.element),"title"in l&&n(2,r=l.title)},[o,i,r,s]}class _i extends U{constructor(t){super(),W(this,t,wi,yi,V,{labelId:1,element:0,title:2})}}function Dt(e){let t,n;return t=new _i({props:{labelId:e[0],title:e[2]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&1&&(r.labelId=i[0]),o&4&&(r.title=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function Ht(e){let t,n;return t=new bi({props:{cancelIcon:e[3],step:e[1]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&8&&(r.cancelIcon=i[3]),o&2&&(r.step=i[1]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function xi(e){let t,n,i,o=e[2]&&Dt(e),r=e[3]&&e[3].enabled&&Ht(e);return{c(){t=D("header"),o&&o.c(),n=De(),r&&r.c(),E(t,"class","shepherd-header")},m(s,l){j(s,t,l),o&&o.m(t,null),he(t,n),r&&r.m(t,null),i=!0},p(s,[l]){s[2]?o?(o.p(s,l),l&4&&v(o,1)):(o=Dt(s),o.c(),v(o,1),o.m(t,n)):o&&(te(),S(o,1,1,()=>{o=null}),ne()),s[3]&&s[3].enabled?r?(r.p(s,l),l&8&&v(r,1)):(r=Ht(s),r.c(),v(r,1),r.m(t,null)):r&&(te(),S(r,1,1,()=>{r=null}),ne())},i(s){i||(v(o),v(r),i=!0)},o(s){S(o),S(r),i=!1},d(s){s&&k(t),o&&o.d(),r&&r.d()}}}function vi(e,t,n){let{labelId:i,step:o}=t,r,s;return e.$$set=l=>{"labelId"in l&&n(0,i=l.labelId),"step"in l&&n(1,o=l.step)},e.$$.update=()=>{e.$$.dirty&2&&(n(2,r=o.options.title),n(3,s=o.options.cancelIcon))},[i,o,r,s]}class Oi extends U{constructor(t){super(),W(this,t,vi,xi,V,{labelId:0,step:1})}}function Ei(e){let t;return{c(){t=D("div"),E(t,"class","shepherd-text"),E(t,"id",e[1])},m(n,i){j(n,t,i),e[3](t)},p(n,[i]){i&2&&E(t,"id",n[1])},i:P,o:P,d(n){n&&k(t),e[3](null)}}}function Si(e,t,n){let{descriptionId:i,element:o,step:r}=t;et(()=>{let{text:l}=r.options;G(l)&&(l=l.call(r)),We(l)?o.appendChild(l):n(0,o.innerHTML=l,o)});function s(l){me[l?"unshift":"push"](()=>{o=l,n(0,o)})}return e.$$set=l=>{"descriptionId"in l&&n(1,i=l.descriptionId),"element"in l&&n(0,o=l.element),"step"in l&&n(2,r=l.step)},[o,i,r,s]}class Ai extends U{constructor(t){super(),W(this,t,Si,Ei,V,{descriptionId:1,element:0,step:2})}}function Nt(e){let t,n;return t=new Oi({props:{labelId:e[1],step:e[2]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&2&&(r.labelId=i[1]),o&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function Bt(e){let t,n;return t=new Ai({props:{descriptionId:e[0],step:e[2]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&1&&(r.descriptionId=i[0]),o&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function Vt(e){let t,n;return t=new pi({props:{step:e[2]}}),{c(){ie(t.$$.fragment)},m(i,o){X(t,i,o),n=!0},p(i,o){const r={};o&4&&(r.step=i[2]),t.$set(r)},i(i){n||(v(t.$$.fragment,i),n=!0)},o(i){S(t.$$.fragment,i),n=!1},d(i){$(t,i)}}}function Ti(e){let t,n=!A(e[2].options.title)||e[2].options.cancelIcon&&e[2].options.cancelIcon.enabled,i,o=!A(e[2].options.text),r,s=Array.isArray(e[2].options.buttons)&&e[2].options.buttons.length,l,c=n&&Nt(e),a=o&&Bt(e),u=s&&Vt(e);return{c(){t=D("div"),c&&c.c(),i=De(),a&&a.c(),r=De(),u&&u.c(),E(t,"class","shepherd-content")},m(h,f){j(h,t,f),c&&c.m(t,null),he(t,i),a&&a.m(t,null),he(t,r),u&&u.m(t,null),l=!0},p(h,[f]){f&4&&(n=!A(h[2].options.title)||h[2].options.cancelIcon&&h[2].options.cancelIcon.enabled),n?c?(c.p(h,f),f&4&&v(c,1)):(c=Nt(h),c.c(),v(c,1),c.m(t,i)):c&&(te(),S(c,1,1,()=>{c=null}),ne()),f&4&&(o=!A(h[2].options.text)),o?a?(a.p(h,f),f&4&&v(a,1)):(a=Bt(h),a.c(),v(a,1),a.m(t,r)):a&&(te(),S(a,1,1,()=>{a=null}),ne()),f&4&&(s=Array.isArray(h[2].options.buttons)&&h[2].options.buttons.length),s?u?(u.p(h,f),f&4&&v(u,1)):(u=Vt(h),u.c(),v(u,1),u.m(t,null)):u&&(te(),S(u,1,1,()=>{u=null}),ne())},i(h){l||(v(c),v(a),v(u),l=!0)},o(h){S(c),S(a),S(u),l=!1},d(h){h&&k(t),c&&c.d(),a&&a.d(),u&&u.d()}}}function Ci(e,t,n){let{descriptionId:i,labelId:o,step:r}=t;return e.$$set=s=>{"descriptionId"in s&&n(0,i=s.descriptionId),"labelId"in s&&n(1,o=s.labelId),"step"in s&&n(2,r=s.step)},[i,o,r]}class Ii extends U{constructor(t){super(),W(this,t,Ci,Ti,V,{descriptionId:0,labelId:1,step:2})}}function Wt(e){let t;return{c(){t=D("div"),E(t,"class","shepherd-arrow"),E(t,"data-popper-arrow","")},m(n,i){j(n,t,i)},d(n){n&&k(t)}}}function Li(e){let t,n,i,o,r,s,l,c,a=e[4].options.arrow&&e[4].options.attachTo&&e[4].options.attachTo.element&&e[4].options.attachTo.on&&Wt();i=new Ii({props:{descriptionId:e[2],labelId:e[3],step:e[4]}});let u=[{"aria-describedby":o=A(e[4].options.text)?null:e[2]},{"aria-labelledby":r=e[4].options.title?e[3]:null},e[1],{role:"dialog"},{tabindex:"0"}],h={};for(let f=0;f<u.length;f+=1)h=Gn(h,u[f]);return{c(){t=D("div"),a&&a.c(),n=De(),ie(i.$$.fragment),Lt(t,h),de(t,"shepherd-has-cancel-icon",e[5]),de(t,"shepherd-has-title",e[6]),de(t,"shepherd-element",!0)},m(f,p){j(f,t,p),a&&a.m(t,null),he(t,n),X(i,t,null),e[13](t),s=!0,l||(c=He(t,"keydown",e[7]),l=!0)},p(f,[p]){f[4].options.arrow&&f[4].options.attachTo&&f[4].options.attachTo.element&&f[4].options.attachTo.on?a||(a=Wt(),a.c(),a.m(t,n)):a&&(a.d(1),a=null);const m={};p&4&&(m.descriptionId=f[2]),p&8&&(m.labelId=f[3]),p&16&&(m.step=f[4]),i.$set(m),Lt(t,h=li(u,[(!s||p&20&&o!==(o=A(f[4].options.text)?null:f[2]))&&{"aria-describedby":o},(!s||p&24&&r!==(r=f[4].options.title?f[3]:null))&&{"aria-labelledby":r},p&2&&f[1],{role:"dialog"},{tabindex:"0"}])),de(t,"shepherd-has-cancel-icon",f[5]),de(t,"shepherd-has-title",f[6]),de(t,"shepherd-element",!0)},i(f){s||(v(i.$$.fragment,f),s=!0)},o(f){S(i.$$.fragment,f),s=!1},d(f){f&&k(t),a&&a.d(),$(i),e[13](null),l=!1,c()}}}const Pi=9,Ri=27,ki=37,Mi=39;function Ut(e){return e.split(" ").filter(t=>!!t.length)}function ji(e,t,n){let{classPrefix:i,element:o,descriptionId:r,firstFocusableElement:s,focusableElements:l,labelId:c,lastFocusableElement:a,step:u,dataStepId:h}=t,f,p,m;const b=()=>o;ni(()=>{n(1,h={[`data-${i}shepherd-step-id`]:u.id}),n(9,l=o.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]')),n(8,s=l[0]),n(10,a=l[l.length-1])}),et(()=>{m!==u.options.classes&&w()});function w(){y(m),m=u.options.classes,x(m)}function y(d){if(ve(d)){const _=Ut(d);_.length&&o.classList.remove(..._)}}function x(d){if(ve(d)){const _=Ut(d);_.length&&o.classList.add(..._)}}const O=d=>{const{tour:_}=u;switch(d.keyCode){case Pi:if(l.length===0){d.preventDefault();break}d.shiftKey?(document.activeElement===s||document.activeElement.classList.contains("shepherd-element"))&&(d.preventDefault(),a.focus()):document.activeElement===a&&(d.preventDefault(),s.focus());break;case Ri:_.options.exitOnEsc&&(d.stopPropagation(),u.cancel());break;case ki:_.options.keyboardNavigation&&(d.stopPropagation(),_.back());break;case Mi:_.options.keyboardNavigation&&(d.stopPropagation(),_.next());break}};function g(d){me[d?"unshift":"push"](()=>{o=d,n(0,o)})}return e.$$set=d=>{"classPrefix"in d&&n(11,i=d.classPrefix),"element"in d&&n(0,o=d.element),"descriptionId"in d&&n(2,r=d.descriptionId),"firstFocusableElement"in d&&n(8,s=d.firstFocusableElement),"focusableElements"in d&&n(9,l=d.focusableElements),"labelId"in d&&n(3,c=d.labelId),"lastFocusableElement"in d&&n(10,a=d.lastFocusableElement),"step"in d&&n(4,u=d.step),"dataStepId"in d&&n(1,h=d.dataStepId)},e.$$.update=()=>{e.$$.dirty&16&&(n(5,f=u.options&&u.options.cancelIcon&&u.options.cancelIcon.enabled),n(6,p=u.options&&u.options.title))},[o,h,r,c,u,f,p,O,s,l,a,i,b,g]}class Fi extends U{constructor(t){super(),W(this,t,ji,Li,V,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class ot extends Ue{constructor(t,n={}){return super(t,n),this.tour=t,this.classPrefix=this.tour.options?ft(this.tour.options.classPrefix):"",this.styles=t.styles,this._resolvedAttachTo=null,at(this),this._setOptions(n),this}cancel(){this.tour.cancel(),this.trigger("cancel")}complete(){this.tour.complete(),this.trigger("complete")}destroy(){Yn(this),We(this.el)&&(this.el.remove(),this.el=null),this._updateStepTargetOnHide(),this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide(),this.trigger("before-hide"),this.el&&(this.el.hidden=!0),this._updateStepTargetOnHide(),this.trigger("hide")}_resolveAttachToOptions(){return this._resolvedAttachTo=cn(this),this._resolvedAttachTo}_getResolvedAttachToOptions(){return this._resolvedAttachTo===null?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!!(this.el&&!this.el.hidden)}show(){return G(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(t){Object.assign(this.options,t),this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){const t=`${this.id}-description`,n=`${this.id}-label`;return this.shepherdElementComponent=new Fi({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:t,labelId:n,step:this,styles:this.styles}}),this.shepherdElementComponent.getElement()}_scrollTo(t){const{element:n}=this._getResolvedAttachToOptions();G(this.options.scrollToHandler)?this.options.scrollToHandler(n):sn(n)&&typeof n.scrollIntoView=="function"&&n.scrollIntoView(t)}_getClassOptions(t){const n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions,i=t.classes?t.classes:"",o=n&&n.classes?n.classes:"",r=[...i.split(" "),...o.split(" ")],s=new Set(r);return Array.from(s).join(" ").trim()}_setOptions(t={}){let n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;n=Ve({},n||{}),this.options=Object.assign({arrow:!0},n,t,Un(n,t));const{when:i}=this.options;this.options.classes=this._getClassOptions(t),this.destroy(),this.id=this.options.id||`step-${Ye()}`,i&&Object.keys(i).forEach(o=>{this.on(o,i[o],this)})}_setupElements(){A(this.el)||this.destroy(),this.el=this._createTooltipContent(),this.options.advanceOn&&ln(this),Wn(this)}_show(){this.trigger("before-show"),this._resolveAttachToOptions(),this._setupElements(),this.tour.modal||this.tour._setupModal(),this.tour.modal.setupForStep(this),this._styleTargetElementForStep(this),this.el.hidden=!1,this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)}),this.el.hidden=!1;const t=this.shepherdElementComponent.getElement(),n=this.target||document.body;n.classList.add(`${this.classPrefix}shepherd-enabled`),n.classList.add(`${this.classPrefix}shepherd-target`),t.classList.add("shepherd-enabled"),this.trigger("show")}_styleTargetElementForStep(t){const n=t.target;n&&(t.options.highlightClass&&n.classList.add(t.options.highlightClass),n.classList.remove("shepherd-target-click-disabled"),t.options.canClickTarget===!1&&n.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){const t=this.target||document.body;this.options.highlightClass&&t.classList.remove(this.options.highlightClass),t.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function Di(e){if(e){const{steps:t}=e;t.forEach(n=>{n.options&&n.options.canClickTarget===!1&&n.options.attachTo&&n.target instanceof HTMLElement&&n.target.classList.remove("shepherd-target-click-disabled")})}}function Hi({width:e,height:t,x:n=0,y:i=0,r:o=0}){const{innerWidth:r,innerHeight:s}=window,{topLeft:l=0,topRight:c=0,bottomRight:a=0,bottomLeft:u=0}=typeof o=="number"?{topLeft:o,topRight:o,bottomRight:o,bottomLeft:o}:o;return`M${r},${s}H0V0H${r}V${s}ZM${n+l},${i}a${l},${l},0,0,0-${l},${l}V${t+i-u}a${u},${u},0,0,0,${u},${u}H${e+n-a}a${a},${a},0,0,0,${a}-${a}V${i+c}a${c},${c},0,0,0-${c}-${c}Z`}function Ni(e){let t,n,i,o,r;return{c(){t=Ct("svg"),n=Ct("path"),E(n,"d",e[2]),E(t,"class",i=`${e[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(s,l){j(s,t,l),he(t,n),e[11](t),o||(r=He(t,"touchmove",e[3]),o=!0)},p(s,[l]){l&4&&E(n,"d",s[2]),l&2&&i!==(i=`${s[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&E(t,"class",i)},i:P,o:P,d(s){s&&k(t),e[11](null),o=!1,r()}}}function Yt(e){if(!e)return null;const n=e instanceof HTMLElement&&window.getComputedStyle(e).overflowY;return n!=="hidden"&&n!=="visible"&&e.scrollHeight>=e.clientHeight?e:Yt(e.parentElement)}function Bi(e,t){const n=e.getBoundingClientRect();let i=n.y||n.top,o=n.bottom||i+n.height;if(t){const s=t.getBoundingClientRect(),l=s.y||s.top,c=s.bottom||l+s.height;i=Math.max(i,l),o=Math.min(o,c)}const r=Math.max(o-i,0);return{y:i,height:r}}function Vi(e,t,n){let{element:i,openingProperties:o}=t;Ye();let r=!1,s,l;a();const c=()=>i;function a(){n(4,o={width:0,height:0,x:0,y:0,r:0})}function u(){n(1,r=!1),y()}function h(g=0,d=0,_,I){if(I){const{y:M,height:H}=Bi(I,_),{x:se,width:Y,left:re}=I.getBoundingClientRect();n(4,o={width:Y+g*2,height:H+g*2,x:(se||re)-g,y:M-g,r:d})}else a()}function f(g){y(),g.tour.options.useModalOverlay?(x(g),p()):u()}function p(){n(1,r=!0)}const m=g=>{g.preventDefault()},b=g=>{g.stopPropagation()};function w(){window.addEventListener("touchmove",m,{passive:!1})}function y(){s&&(cancelAnimationFrame(s),s=void 0),window.removeEventListener("touchmove",m,{passive:!1})}function x(g){const{modalOverlayOpeningPadding:d,modalOverlayOpeningRadius:_}=g.options,I=Yt(g.target),M=()=>{s=void 0,h(d,_,I,g.target),s=requestAnimationFrame(M)};M(),w()}function O(g){me[g?"unshift":"push"](()=>{i=g,n(0,i)})}return e.$$set=g=>{"element"in g&&n(0,i=g.element),"openingProperties"in g&&n(4,o=g.openingProperties)},e.$$.update=()=>{e.$$.dirty&16&&n(2,l=Hi(o))},[i,r,l,b,o,c,a,u,h,f,p,O]}class Wi extends U{constructor(t){super(),W(this,t,Vi,Ni,V,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}const oe=new Ue;class Ui extends Ue{constructor(t={}){super(t),at(this);const n={exitOnEsc:!0,keyboardNavigation:!0};return this.options=Object.assign({},n,t),this.classPrefix=ft(this.options.classPrefix),this.steps=[],this.addSteps(this.options.steps),["active","cancel","complete","inactive","show","start"].map(o=>{(r=>{this.on(r,s=>{s=s||{},s.tour=this,oe.trigger(r,s)})})(o)}),this._setTourID(),this}addStep(t,n){let i=t;return i instanceof ot?i.tour=this:i=new ot(this,i),A(n)?this.steps.push(i):this.steps.splice(n,0,i),i}addSteps(t){return Array.isArray(t)&&t.forEach(n=>{this.addStep(n)}),this}back(){const t=this.steps.indexOf(this.currentStep);this.show(t-1,!1)}async cancel(){if(this.options.confirmCancel){const t=typeof this.options.confirmCancel=="function",n=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";(t?await this.options.confirmCancel():window.confirm(n))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(t){return this.steps.find(n=>n.id===t)}getCurrentStep(){return this.currentStep}hide(){const t=this.getCurrentStep();if(t)return t.hide()}isActive(){return oe.activeTour===this}next(){const t=this.steps.indexOf(this.currentStep);t===this.steps.length-1?this.complete():this.show(t+1,!0)}removeStep(t){const n=this.getCurrentStep();this.steps.some((i,o)=>{if(i.id===t)return i.isOpen()&&i.hide(),i.destroy(),this.steps.splice(o,1),!0}),n&&n.id===t&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(t=0,n=!0){const i=ve(t)?this.getById(t):this.steps[t];i&&(this._updateStateBeforeShow(),G(i.options.showOn)&&!i.options.showOn()?this._skipStep(i,n):(this.trigger("show",{step:i,previous:this.currentStep}),this.currentStep=i,i.show()))}start(){this.trigger("start"),this.focusedElBeforeOpen=document.activeElement,this.currentStep=null,this._setupModal(),this._setupActiveTour(),this.next()}_done(t){const n=this.steps.indexOf(this.currentStep);if(Array.isArray(this.steps)&&this.steps.forEach(i=>i.destroy()),Di(this),this.trigger(t,{index:n}),oe.activeTour=null,this.trigger("inactive",{tour:this}),this.modal&&this.modal.hide(),(t==="cancel"||t==="complete")&&this.modal){const i=document.querySelector(".shepherd-modal-overlay-container");i&&i.remove()}We(this.focusedElBeforeOpen)&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this}),oe.activeTour=this}_setupModal(){this.modal=new Wi({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(t,n){const i=this.steps.indexOf(t);if(i===this.steps.length-1)this.complete();else{const o=n?i+1:i-1;this.show(o,n)}}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide(),this.isActive()||this._setupActiveTour()}_setTourID(){const t=this.options.tourName||"tour";this.id=`${t}--${Ye()}`}}const Yi=typeof window>"u";class qt{constructor(){}}return Yi?Object.assign(oe,{Tour:qt,Step:qt}):Object.assign(oe,{Tour:Ui,Step:ot}),oe})})(zt);var Xi=zt.exports;const $i=Ki(Xi);try{window.Shepherd=$i}catch{}
