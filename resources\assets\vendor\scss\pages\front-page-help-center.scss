// * Help center
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

.help-center-header {
  .input-wrapper {
    max-width: 29rem;
    @include light.media-breakpoint-down(sm) {
      max-width: 80%;
    }
  }
}

@if $enable-light-style {
  .light-style {
    .help-center-header {
      .input-wrapper {
        .input-group-text,
        .form-control {
          background-color: light.$card-bg;
        }
      }
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .help-center-header {
      .input-wrapper {
        .input-group-text,
        .form-control {
          background-color: dark.$card-bg;
        }
      }
    }
  }
}
