(function(){function r(o,n){var e=document.createElement("link");e.rel="stylesheet",e.href=o,e.media=n||"all";var t=document.getElementsByTagName("script")[0];return t.parentNode.insertBefore(e,t),e}function d(o,n){var e=r(o,"only x");e.onload=function(){this.media="all",n&&n()},setTimeout(function(){e.media="all"},100)}function i(o){o.forEach(function(n){var e=document.createElement("link");e.rel="preload",e.as="style",e.href=n,e.onload=function(){this.rel="stylesheet"},document.head.appendChild(e)})}function a(){var o=[],n=[];window.innerWidth>768&&i(o);var e=!1,t=function(){e||(e=!0,i(n),document.removeEventListener("scroll",t),document.removeEventListener("mousemove",t),document.removeEventListener("touchstart",t),document.removeEventListener("click",t))};document.addEventListener("scroll",t,{passive:!0}),document.addEventListener("mousemove",t,{passive:!0}),document.addEventListener("touchstart",t,{passive:!0}),document.addEventListener("click",t,{passive:!0}),setTimeout(t,3e3)}function l(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",a):a()}l(),window.CriticalCSSLoader={loadCSS:r,loadCSSWithFallback:d,preloadCSS:i}})();
