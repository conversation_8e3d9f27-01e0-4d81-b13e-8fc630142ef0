<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SmsService extends Model
{
    use HasFactory;

    protected $table = 'sms_services';

    protected $fillable = [
        'name',
        'code',
        'country_id',
        'price',
        'stock',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the country that owns this service
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(SmsCountry::class, 'country_id');
    }

    /**
     * Get all sim rentals for this service
     */
    public function simRentals(): HasMany
    {
        return $this->hasMany(SmsSimRental::class, 'service_id');
    }

    /**
     * Scope for active services
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered services
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope for specific country
     */
    public function scopeForCountry($query, $countryId)
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope for services with stock
     */
    public function scopeInStock($query)
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * Decrease stock by 1
     */
    public function decreaseStock(): bool
    {
        if ($this->stock > 0) {
            $this->decrement('stock');
            return true;
        }
        return false;
    }

    /**
     * Increase stock by 1
     */
    public function increaseStock(): bool
    {
        $this->increment('stock');
        return true;
    }
}
