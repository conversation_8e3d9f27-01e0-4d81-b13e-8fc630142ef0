/**
 * Critical CSS Loader
 * Optimized loading of non-critical CSS files
 */

(function() {
    'use strict';
    
    /**
     * Load CSS file asynchronously
     * @param {string} href - CSS file URL
     * @param {string} media - Media query (optional)
     */
    function loadCSS(href, media) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = media || 'all';
        
        // Insert before the first script tag
        var ref = document.getElementsByTagName('script')[0];
        ref.parentNode.insertBefore(link, ref);
        
        return link;
    }
    
    /**
     * Load CSS with fallback for older browsers
     * @param {string} href - CSS file URL
     * @param {Function} callback - Callback function
     */
    function loadCSSWithFallback(href, callback) {
        var link = loadCSS(href, 'only x');
        
        // Set media to 'all' once loaded
        link.onload = function() {
            this.media = 'all';
            if (callback) callback();
        };
        
        // Fallback for browsers that don't support onload
        setTimeout(function() {
            link.media = 'all';
        }, 100);
    }
    
    /**
     * Preload CSS files for better performance
     * @param {Array} cssFiles - Array of CSS file URLs
     */
    function preloadCSS(cssFiles) {
        cssFiles.forEach(function(href) {
            var link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            link.onload = function() {
                this.rel = 'stylesheet';
            };
            document.head.appendChild(link);
        });
    }
    
    /**
     * Load CSS files based on viewport and user interaction
     */
    function loadDeferredCSS() {
        // Load icon fonts after page load
        var iconFonts = [
            // These will be populated by Vite asset URLs
        ];
        
        // Load vendor CSS after user interaction
        var vendorCSS = [
            // These will be populated by Vite asset URLs
        ];
        
        // Load immediately for critical viewport
        if (window.innerWidth > 768) {
            preloadCSS(iconFonts);
        }
        
        // Load on user interaction
        var userInteracted = false;
        var loadOnInteraction = function() {
            if (!userInteracted) {
                userInteracted = true;
                preloadCSS(vendorCSS);
                
                // Remove event listeners
                document.removeEventListener('scroll', loadOnInteraction);
                document.removeEventListener('mousemove', loadOnInteraction);
                document.removeEventListener('touchstart', loadOnInteraction);
                document.removeEventListener('click', loadOnInteraction);
            }
        };
        
        // Add event listeners for user interaction
        document.addEventListener('scroll', loadOnInteraction, { passive: true });
        document.addEventListener('mousemove', loadOnInteraction, { passive: true });
        document.addEventListener('touchstart', loadOnInteraction, { passive: true });
        document.addEventListener('click', loadOnInteraction, { passive: true });
        
        // Fallback: load after 3 seconds
        setTimeout(loadOnInteraction, 3000);
    }
    
    /**
     * Initialize critical CSS loading
     */
    function init() {
        // Load deferred CSS when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadDeferredCSS);
        } else {
            loadDeferredCSS();
        }
    }
    
    // Initialize when script loads
    init();
    
    // Expose functions globally for manual use
    window.CriticalCSSLoader = {
        loadCSS: loadCSS,
        loadCSSWithFallback: loadCSSWithFallback,
        preloadCSS: preloadCSS
    };
    
})();
