@extends('layouts/layoutMaster')

@section('title', 'Quản lý Software/Tool - Admin')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'])
@endsection

@section('page-script')
    @vite('resources/assets/js/software/software-list-admin.js')
@endsection

@section('content')
    <!-- Success Message -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Error Messages -->
    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Software Statistics Widget -->
    <div class="card mb-6">
        <div class="card-widget-separator-wrapper">
            <div class="card-body card-widget-separator">
                <div class="row gy-4 gy-sm-1">
                    <div class="col-sm-6 col-lg-3">
                        <div class="d-flex justify-content-between align-items-center card-widget-1 border-end pb-4 pb-sm-0">
                            <div>
                                <h4 class="mb-0">{{ $totalSoftware }}</h4>
                                <p class="mb-0">Tổng Software</p>
                            </div>
                            <div class="avatar me-sm-6">
                                <span class="avatar-initial rounded bg-label-primary text-heading">
                                    <i class="ti ti-apps ti-26px"></i>
                                </span>
                            </div>
                        </div>
                        <hr class="d-none d-sm-block d-lg-none me-6">
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="d-flex justify-content-between align-items-center card-widget-2 border-end pb-4 pb-sm-0">
                            <div>
                                <h4 class="mb-0">{{ $freeSoftware }}</h4>
                                <p class="mb-0">Software Miễn phí</p>
                            </div>
                            <div class="avatar me-lg-6">
                                <span class="avatar-initial rounded bg-label-success text-heading">
                                    <i class="ti ti-gift ti-26px"></i>
                                </span>
                            </div>
                        </div>
                        <hr class="d-none d-sm-block d-lg-none">
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="d-flex justify-content-between align-items-center border-end pb-4 pb-sm-0 card-widget-3">
                            <div>
                                <h4 class="mb-0">{{ $paidSoftware }}</h4>
                                <p class="mb-0">Software Trả phí</p>
                            </div>
                            <div class="avatar me-sm-6">
                                <span class="avatar-initial rounded bg-label-warning text-heading">
                                    <i class="ti ti-currency-dollar ti-26px"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0">{{ $activeSoftware }}</h4>
                                <p class="mb-0">Đang hoạt động</p>
                            </div>
                            <div class="avatar">
                                <span class="avatar-initial rounded bg-label-info text-heading">
                                    <i class="ti ti-check ti-26px"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Software List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title mb-0">Danh sách Software/Tool</h5>
            <div class="d-flex justify-content-between align-items-center row pt-4 gap-6 gap-md-0">
                <div class="col-md-4 software_status">
                    <select id="SoftwareStatus" class="form-select text-capitalize">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active">Hoạt động</option>
                        <option value="inactive">Không hoạt động</option>
                    </select>
                </div>
                <div class="col-md-4 software_type">
                    <select id="SoftwareType" class="form-select text-capitalize">
                        <option value="">Tất cả loại</option>
                        <option value="free">Miễn phí</option>
                        <option value="paid">Trả phí</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('software-create') }}" class="btn btn-primary">
                        <i class="ti ti-plus me-1"></i>Thêm Software
                    </a>
                </div>
            </div>
        </div>
        <div class="card-datatable table-responsive">
            <table class="software-list-table table border-top">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Hình ảnh</th>
                        <th>Tên Software</th>
                        <th>Loại</th>
                        <th>Giá</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th class="cell-fit text-center">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($softwares as $software)
                    <tr>
                        <td>{{ $software->id }}</td>
                        <td>
                            <div class="avatar avatar-sm">
                                <img src="{{ asset($software->image) }}" alt="{{ $software->name }}" class="rounded">
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <h6 class="mb-0">{{ $software->name }}</h6>
                                @if($software->en_name)
                                    <small class="text-muted">{{ $software->en_name }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            @if($software->is_free)
                                <span class="badge bg-label-success">Miễn phí</span>
                            @else
                                <span class="badge bg-label-warning">Trả phí</span>
                            @endif
                        </td>
                        <td>
                            @if($software->is_free)
                                <span class="text-success fw-medium">Miễn phí</span>
                            @else
                                <div class="d-flex flex-column">
                                    @if($software->price_1_m > 0)
                                        <small>1 tháng: {{ number_format($software->price_1_m) }}đ</small>
                                    @endif
                                    @if($software->price_1_y > 0)
                                        <small>1 năm: {{ number_format($software->price_1_y) }}đ</small>
                                    @endif
                                </div>
                            @endif
                        </td>
                        <td>
                            @if($software->status === 'active')
                                <span class="badge bg-label-success">Hoạt động</span>
                            @else
                                <span class="badge bg-label-secondary">Không hoạt động</span>
                            @endif
                        </td>
                        <td>{{ $software->created_at->format('d/m/Y') }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <a href="{{ route('software-show', $software->url) }}" class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect" data-bs-toggle="tooltip" title="Xem chi tiết">
                                    <i class="ti ti-eye"></i>
                                </a>
                                <a href="{{ route('software-edit', $software->id) }}" class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                    <i class="ti ti-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect delete-software" data-id="{{ $software->id }}" data-bs-toggle="tooltip" title="Xóa">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @if($softwares->hasPages())
        <div class="card-footer">
            {{ $softwares->links() }}
        </div>
        @endif
    </div>

@endsection
