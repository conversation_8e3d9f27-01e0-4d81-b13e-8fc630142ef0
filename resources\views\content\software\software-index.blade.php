@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Software Collection - Professional Development Tools')

@section('seo-meta')
    <x-seo-meta page="software" :data="['softwares' => $softwares ?? []]" />
@endsection

@section('vendor-style')
    @vite([
        'resources/assets/vendor/libs/select2/select2.scss',
        //   'resources/assets/vendor/libs/apex-charts/apex-charts.scss',
        //   'resources/assets/vendor/libs/swiper/swiper.scss'
    ])
@endsection

@section('page-style')
    {{-- Minimal critical CSS for immediate render --}}
    {{-- <style>
        {!! \App\Services\CSSOptimizer::getMinimalCSS('software') !!}
    </style> --}}

    {{-- Preload critical images for LCP optimization --}}
    @if (isset($softwares) && $softwares->count() > 0)
        @foreach ($softwares->take(3) as $software)
            <link rel="preload" as="image" href="{{ asset($software->image) }}"
                fetchpriority="{{ $loop->first ? 'high' : 'low' }}">
        @endforeach
    @endif

    {{-- Defer DataTables CSS (only load if needed) --}}
    <script>
        // Load DataTables CSS only if tables are present
        document.addEventListener('DOMContentLoaded', function() {
            var hasTables = document.querySelector('table, .datatable');
            if (hasTables) {
                function loadCSS(href) {
                    var link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = href;
                    document.head.appendChild(link);
                }

                // Load DataTables CSS
                loadCSS('/build/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css');
                loadCSS('/build/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css');
                loadCSS('/build/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css');
            }
        });
    </script>
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/select2/select2.js'])
@endsection

@section('page-script')
    @vite(['resources/assets/js/card/cards-advance.js'])
@endsection

@section('content')
    {{-- Structured Data for Software Collection --}}
    @if (isset($softwares) && $softwares->count() > 0)
        @foreach ($softwares->take(10) as $software)
            <script type="application/ld+json">
            {!! json_encode(\App\Services\SEOService::getStructuredData('software', ['software' => $software]), JSON_UNESCAPED_SLASHES) !!}
            </script>
        @endforeach
    @endif

    <style>
        .img-tool {
            width: 100%;
            height: 30vh;
            object-fit: cover;
        }
    </style>
    <div class="row">
        <form method="GET" action="{{ route('software-all') }}" class="mb-4">
            <div class="row justify-content-center align-items-center">
                <div class="col-12 col-md-4 mb-2 mb-md-0">
                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}"
                        placeholder="Search software...">
                </div>
                <div class="col-12 col-md-6 mb-2 mb-md-0">
                    <select name="tag[]" id="tag[]" class="select2 form-select" multiple>
                        @foreach ($allTags as $tag)
                            <option value="{{ $tag->name }}"
                                {{ in_array($tag->name, request('tag', [])) ? 'selected' : '' }}>
                                {{ $tag->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-12 col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> {{ __('messages.filter') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <!-- Software Card -->
        @foreach ($softwares as $software)
            <div class="col-md-6 col-xxl-4 mb-6">
                <div class="card h-100">
                    <div class="card-body p-2 pb-0">
                        <a href="{{ route('software-show', $software->url) }}">
                            <div class="bg-label-primary rounded text-center mb-2 position-relative">
                                @if ($software->is_free)
                                    <span class="badge badge-free position-absolute bg-success fs-6 p-3" style="top: 10px; left: 10px; z-index: 2;">Free</span>
                                @else
                                    <span class="badge badge-free position-absolute bg-warning fs-6 p-3" style="top: 10px; left: 10px; z-index: 2;">
                                        {{ number_format($software->price_1_m) }}đ
                                    </span>
                                @endif
                                <img src="{{ $software->image }}" alt="Software image" class="img-fluid img-tool"
                                    width="140" height="200" critical="{{ $loop->index < 3 }}"
                                    size="(max-width: 576px) 140px, (max-width: 768px) 140px, 140px" />
                                <i class="bi bi-heart-fill text-danger icon-overlay"></i>
                            </div>
                            <h5 class="mb-1">{{ $software->name }}</h5>
                        </a>
                    </div>
                    <div class="card-footer p-2 pt-0">
                        <div class="demo-inline-spacing pb-0 m-0">
                            @foreach ($software->tags as $tag)
                                <span class="badge rounded-pill m-0 mb-1"
                                    style="background-color: #666;">{{ $tag->name }}</span>
                            @endforeach
                        </div>
                        <a href="{{ route('software-show', $software->url) }}"
                            class="btn btn-primary w-100">{{ __('messages.view_detail') }}</a>
                    </div>
                </div>
            </div>
        @endforeach
        <!--/ Software Card -->
    </div>
    <!-- Pagination Sizes -->
    <div class="row">
        <div class="col-lg-12">
            <div class="demo-inline-spacing">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page Link -->
                        <li class="page-item {{ $softwares->onFirstPage() ? 'disabled' : '' }}">
                            <a class="page-link" href="{{ $softwares->previousPageUrl() ?? '#' }}"
                                aria-label="Go to previous page"
                                @if ($softwares->onFirstPage()) aria-disabled="true" onclick="return false;" @endif>
                                <i class="ti ti-chevrons-left ti-sm"></i>
                            </a>
                        </li>

                        <!-- Pagination Elements -->
                        @php
                            $start = max(1, $softwares->currentPage() - 2);
                            $end = min($softwares->lastPage(), $softwares->currentPage() + 2);
                        @endphp

                        @if ($start > 1)
                            <li class="page-item">
                                <a class="page-link" href="{{ $softwares->url(1) }}">1</a>
                            </li>
                            @if ($start > 2)
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            @endif
                        @endif

                        @for ($i = $start; $i <= $end; $i++)
                            <li class="page-item {{ $i == $softwares->currentPage() ? 'active' : '' }}">
                                <a class="page-link" href="{{ $softwares->url($i) }}">{{ $i }}</a>
                            </li>
                        @endfor

                        @if ($end < $softwares->lastPage())
                            @if ($end < $softwares->lastPage() - 1)
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            @endif
                            <li class="page-item">
                                <a class="page-link"
                                    href="{{ $softwares->url($softwares->lastPage()) }}">{{ $softwares->lastPage() }}</a>
                            </li>
                        @endif

                        <!-- Next Page Link -->
                        <li class="page-item {{ $softwares->hasMorePages() ? '' : 'disabled' }}">
                            <a class="page-link" href="{{ $softwares->nextPageUrl() ?? '#' }}" aria-label="Go to next page"
                                @if (!$softwares->hasMorePages()) aria-disabled="true" onclick="return false;" @endif>
                                <i class="ti ti-chevrons-right ti-sm"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    <!--/ Pagination Sizes -->
@endsection
