import{r as te}from"./jquery-Czc5UB_B.js";import"./_commonjsHelpers-BosuxZz1.js";var ie={exports:{}};(function(G){(function(l){G.exports?G.exports=l(te()):l(jQuery)})(function(l,y){/*!
 * jsTree 3.3.16
 * http://jstree.com/
 *
 * Copyright (c) 2014 <PERSON> (http://vakata.com)
 *
 * Licensed same as jquery - under the terms of the MIT License
 *   http://www.opensource.org/licenses/mit-license.php
 *//*!
 * if using jslint please allow for the jQuery global and use following options:
 * jslint: loopfunc: true, browser: true, ass: true, bitwise: true, continue: true, nomen: true, plusplus: true, regexp: true, unparam: true, todo: true, white: true
 */if(!l.jstree){var ee=0,D=!1,q=!1,J=!1,Z=[],R=l("script:last").attr("src"),T=window.document,W=window.setImmediate,b=window.Promise;!W&&b&&(W=function(e,s){b.resolve(s).then(e)}),l.jstree={version:"3.3.16",defaults:{plugins:[]},plugins:{},path:R&&R.indexOf("/")!==-1?R.replace(/\/[^\/]+$/,""):"",idregex:/[\\:&!^|()\[\]<>@*'+~#";.,=\- \/${}%?`]/g,root:"#"},l.jstree.create=function(e,s){var t=new l.jstree.core(++ee),i=s;return s=l.extend(!0,{},l.jstree.defaults,s),i&&i.plugins&&(s.plugins=i.plugins),l.each(s.plugins,function(r,a){r!=="core"&&(t=t.plugin(a,s[a]))}),l(e).data("jstree",t),t.init(e,s),t},l.jstree.destroy=function(){l(".jstree:jstree").jstree("destroy"),l(T).off(".jstree")},l.jstree.core=function(e){this._id=e,this._cnt=0,this._wrk=null,this._data={core:{themes:{name:!1,dots:!1,icons:!1,ellipsis:!1},selected:[],last_error:{},working:!1,worker_queue:[],focused:null}}},l.jstree.reference=function(e){var s=null,t=null;if(e&&e.id&&(!e.tagName||!e.nodeType)&&(e=e.id),!t||!t.length)try{t=l(e)}catch{}if(!t||!t.length)try{t=l("#"+e.replace(l.jstree.idregex,"\\$&"))}catch{}return t&&t.length&&(t=t.closest(".jstree")).length&&(t=t.data("jstree"))?s=t:l(".jstree").each(function(){var i=l(this).data("jstree");if(i&&i._model.data[e])return s=i,!1}),s},l.fn.jstree=function(e){var s=typeof e=="string",t=Array.prototype.slice.call(arguments,1),i=null;return e===!0&&!this.length?!1:(this.each(function(){var r=l.jstree.reference(this),a=s&&r?r[e]:null;if(i=s&&a?a.apply(r,t):null,!r&&!s&&(e===y||l.isPlainObject(e))&&l.jstree.create(this,e),(r&&!s||e===!0)&&(i=r||!1),i!==null&&i!==y)return!1}),i!==null&&i!==y?i:this)},l.expr.pseudos.jstree=l.expr.createPseudo(function(e){return function(s){return l(s).hasClass("jstree")&&l(s).data("jstree")!==y}}),l.jstree.defaults.core={data:!1,strings:!1,check_callback:!1,error:l.noop,animation:200,multiple:!0,themes:{name:!1,url:!1,dir:!1,dots:!0,icons:!0,ellipsis:!1,stripes:!1,variant:!1,responsive:!1},expand_selected_onload:!0,worker:!0,force_text:!1,dblclick_toggle:!0,loaded_state:!1,restore_focus:!0,compute_elements_positions:!1,keyboard:{"ctrl-space":function(e){e.type="click",l(e.currentTarget).trigger(e)},enter:function(e){e.type="click",l(e.currentTarget).trigger(e)},left:function(e){if(e.preventDefault(),this.is_open(e.currentTarget))this.close_node(e.currentTarget);else{var s=this.get_parent(e.currentTarget);s&&s.id!==l.jstree.root&&this.get_node(s,!0).children(".jstree-anchor").trigger("focus")}},up:function(e){e.preventDefault();var s=this.get_prev_dom(e.currentTarget);s&&s.length&&s.children(".jstree-anchor").trigger("focus")},right:function(e){if(e.preventDefault(),this.is_closed(e.currentTarget))this.open_node(e.currentTarget,function(t){this.get_node(t,!0).children(".jstree-anchor").trigger("focus")});else if(this.is_open(e.currentTarget)){var s=this.get_node(e.currentTarget,!0).children(".jstree-children")[0];s&&l(this._firstChild(s)).children(".jstree-anchor").trigger("focus")}},down:function(e){e.preventDefault();var s=this.get_next_dom(e.currentTarget);s&&s.length&&s.children(".jstree-anchor").trigger("focus")},"*":function(e){this.open_all()},home:function(e){e.preventDefault();var s=this._firstChild(this.get_container_ul()[0]);s&&l(s).children(".jstree-anchor").filter(":visible").trigger("focus")},end:function(e){e.preventDefault(),this.element.find(".jstree-anchor").filter(":visible").last().trigger("focus")},f2:function(e){e.preventDefault(),this.edit(e.currentTarget)}},allow_reselect:!1},l.jstree.core.prototype={plugin:function(e,s){var t=l.jstree.plugins[e];return t?(this._data[e]={},t.prototype=this,new t(s,this)):this},init:function(e,s){this._model={data:{},changed:[],force_full_redraw:!1,redraw_timeout:!1,default_state:{loaded:!0,opened:!1,selected:!1,disabled:!1}},this._model.data[l.jstree.root]={id:l.jstree.root,parent:null,parents:[],children:[],children_d:[],state:{loaded:!1}},this.element=l(e).addClass("jstree jstree-"+this._id),this.settings=s,this._data.core.ready=!1,this._data.core.loaded=!1,this._data.core.rtl=this.element.css("direction")==="rtl",this.element[this._data.core.rtl?"addClass":"removeClass"]("jstree-rtl"),this.element.attr("role","tree"),this.settings.core.multiple&&this.element.attr("aria-multiselectable",!0),this.element.attr("tabindex")||this.element.attr("tabindex","0"),this.bind(),this.trigger("init"),this._data.core.original_container_html=this.element.find(" > ul > li").clone(!0),this._data.core.original_container_html.find("li").addBack().contents().filter(function(){return this.nodeType===3&&(!this.nodeValue||/^\s+$/.test(this.nodeValue))}).remove(),this.element.html("<ul class='jstree-container-ul jstree-children' role='group'><li id='j"+this._id+"_loading' class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='none'><i class='jstree-icon jstree-ocl'></i><a class='jstree-anchor' role='treeitem' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>"+this.get_string("Loading ...")+"</a></li></ul>"),this.element.attr("aria-activedescendant","j"+this._id+"_loading"),this._data.core.li_height=this.get_container_ul().children("li").first().outerHeight()||24,this._data.core.node=this._create_prototype_node(),this.trigger("loading"),this.load_node(l.jstree.root)},destroy:function(e){if(this.trigger("destroy"),this._wrk)try{window.URL.revokeObjectURL(this._wrk),this._wrk=null}catch{}e||this.element.empty(),this.teardown()},_create_prototype_node:function(){var e=T.createElement("LI"),s,t;return e.setAttribute("role","none"),s=T.createElement("I"),s.className="jstree-icon jstree-ocl",s.setAttribute("role","presentation"),e.appendChild(s),s=T.createElement("A"),s.className="jstree-anchor",s.setAttribute("href","#"),s.setAttribute("tabindex","-1"),s.setAttribute("role","treeitem"),t=T.createElement("I"),t.className="jstree-icon jstree-themeicon",t.setAttribute("role","presentation"),s.appendChild(t),e.appendChild(s),s=t=null,e},_kbevent_to_func:function(e){var s={8:"Backspace",9:"Tab",13:"Enter",19:"Pause",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"Print",45:"Insert",46:"Delete",96:"Numpad0",97:"Numpad1",98:"Numpad2",99:"Numpad3",100:"Numpad4",101:"Numpad5",102:"Numpad6",103:"Numpad7",104:"Numpad8",105:"Numpad9","-13":"NumpadEnter",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"Numlock",145:"Scrolllock",16:"Shift",17:"Ctrl",18:"Alt",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",61:"=",65:"a",66:"b",67:"c",68:"d",69:"e",70:"f",71:"g",72:"h",73:"i",74:"j",75:"k",76:"l",77:"m",78:"n",79:"o",80:"p",81:"q",82:"r",83:"s",84:"t",85:"u",86:"v",87:"w",88:"x",89:"y",90:"z",107:"+",109:"-",110:".",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",111:"/",106:"*",173:"-"},t=[];if(e.ctrlKey&&t.push("ctrl"),e.altKey&&t.push("alt"),e.shiftKey&&t.push("shift"),t.push(s[e.which]?s[e.which].toLowerCase():e.which),t=t.sort().join("-").toLowerCase(),t==="shift-shift"||t==="ctrl-ctrl"||t==="alt-alt")return null;var i=this.settings.core.keyboard,r,a;for(r in i)if(i.hasOwnProperty(r)&&(a=r,a!=="-"&&a!=="+"&&(a=a.replace("--","-MINUS").replace("+-","-MINUS").replace("++","-PLUS").replace("-+","-PLUS"),a=a.split(/-|\+/).sort().join("-").replace("MINUS","-").replace("PLUS","+").toLowerCase()),a===t))return i[r];return null},teardown:function(){this.unbind(),this.element.removeClass("jstree").removeData("jstree").find("[class^='jstree']").addBack().attr("class",function(){return this.className.replace(/jstree[^ ]*|$/ig,"")}),this.element=null},bind:function(){var e="",s=null,t=0;this.element.on("dblclick.jstree",function(i){if(i.target.tagName&&i.target.tagName.toLowerCase()==="input")return!0;if(T.selection&&T.selection.empty)T.selection.empty();else if(window.getSelection){var r=window.getSelection();try{r.removeAllRanges(),r.collapse()}catch{}}}).on("mousedown.jstree",(function(i){i.target===this.element[0]&&(i.preventDefault(),t=+new Date)}).bind(this)).on("mousedown.jstree",".jstree-ocl",function(i){i.preventDefault()}).on("click.jstree",".jstree-ocl",(function(i){this.toggle_node(i.target)}).bind(this)).on("dblclick.jstree",".jstree-anchor",(function(i){if(i.target.tagName&&i.target.tagName.toLowerCase()==="input")return!0;this.settings.core.dblclick_toggle&&this.toggle_node(i.target)}).bind(this)).on("click.jstree",".jstree-anchor",(function(i){i.preventDefault(),i.currentTarget!==T.activeElement&&l(i.currentTarget).trigger("focus"),this.activate_node(i.currentTarget,i)}).bind(this)).on("keydown.jstree",".jstree-anchor",(function(i){if(i.target.tagName&&i.target.tagName.toLowerCase()==="input")return!0;this._data.core.rtl&&(i.which===37?i.which=39:i.which===39&&(i.which=37));var r=this._kbevent_to_func(i);if(r){var a=r.call(this,i);if(a===!1||a===!0)return a}}).bind(this)).on("load_node.jstree",(function(i,r){r.status&&(r.node.id===l.jstree.root&&!this._data.core.loaded&&(this._data.core.loaded=!0,this._firstChild(this.get_container_ul()[0])&&this.element.attr("aria-activedescendant",this._firstChild(this.get_container_ul()[0]).id),this.trigger("loaded")),this._data.core.ready||setTimeout((function(){if(this.element&&!this.get_container_ul().find(".jstree-loading").length){if(this._data.core.ready=!0,this._data.core.selected.length){if(this.settings.core.expand_selected_onload){var a=[],n,c;for(n=0,c=this._data.core.selected.length;n<c;n++)a=a.concat(this._model.data[this._data.core.selected[n]].parents);for(a=l.vakata.array_unique(a),n=0,c=a.length;n<c;n++)this.open_node(a[n],!1,0)}this.trigger("changed",{action:"ready",selected:this._data.core.selected})}this.trigger("ready")}}).bind(this),0))}).bind(this)).on("keypress.jstree",(function(i){if(i.target.tagName&&i.target.tagName.toLowerCase()==="input")return!0;s&&clearTimeout(s),s=setTimeout(function(){e=""},500);var r=String.fromCharCode(i.which).toLowerCase(),a=this.element.find(".jstree-anchor").filter(":visible"),n=a.index(T.activeElement)||0,c=!1;e+=r,!(e.length>1&&(a.slice(n).each((function(d,h){if(l(h).text().toLowerCase().indexOf(e)===0)return l(h).trigger("focus"),c=!0,!1}).bind(this)),c||(a.slice(0,n).each((function(d,h){if(l(h).text().toLowerCase().indexOf(e)===0)return l(h).trigger("focus"),c=!0,!1}).bind(this)),c)))&&new RegExp("^"+r.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")+"+$").test(e)&&(a.slice(n+1).each((function(d,h){if(l(h).text().toLowerCase().charAt(0)===r)return l(h).trigger("focus"),c=!0,!1}).bind(this)),c||a.slice(0,n+1).each((function(d,h){if(l(h).text().toLowerCase().charAt(0)===r)return l(h).trigger("focus"),c=!0,!1}).bind(this)))}).bind(this)).on("init.jstree",(function(){var i=this.settings.core.themes;this._data.core.themes.dots=i.dots,this._data.core.themes.stripes=i.stripes,this._data.core.themes.icons=i.icons,this._data.core.themes.ellipsis=i.ellipsis,this.set_theme(i.name||"default",i.url),this.set_theme_variant(i.variant)}).bind(this)).on("loading.jstree",(function(){this[this._data.core.themes.dots?"show_dots":"hide_dots"](),this[this._data.core.themes.icons?"show_icons":"hide_icons"](),this[this._data.core.themes.stripes?"show_stripes":"hide_stripes"](),this[this._data.core.themes.ellipsis?"show_ellipsis":"hide_ellipsis"]()}).bind(this)).on("blur.jstree",".jstree-anchor",(function(i){this._data.core.focused=null,l(i.currentTarget).filter(".jstree-hovered").trigger("mouseleave"),this.element.attr("tabindex","0"),l(i.currentTarget).attr("tabindex","-1")}).bind(this)).on("focus.jstree",".jstree-anchor",(function(i){var r=this.get_node(i.currentTarget);r&&(r.id||r.id===0)&&(this._data.core.focused=r.id),this.element.find(".jstree-hovered").not(i.currentTarget).trigger("mouseleave"),l(i.currentTarget).trigger("mouseenter"),this.element.attr("tabindex","-1"),l(i.currentTarget).attr("tabindex","0")}).bind(this)).on("focus.jstree",(function(){if(+new Date-t>500&&!this._data.core.focused&&this.settings.core.restore_focus){t=0;var i=this.get_node(this.element.attr("aria-activedescendant"),!0);i&&i.find("> .jstree-anchor").trigger("focus")}}).bind(this)).on("mouseenter.jstree",".jstree-anchor",(function(i){this.hover_node(i.currentTarget)}).bind(this)).on("mouseleave.jstree",".jstree-anchor",(function(i){this.dehover_node(i.currentTarget)}).bind(this))},unbind:function(){this.element.off(".jstree"),l(T).off(".jstree-"+this._id)},trigger:function(e,s){s||(s={}),s.instance=this,this.element.triggerHandler(e.replace(".jstree","")+".jstree",s)},get_container:function(){return this.element},get_container_ul:function(){return this.element.children(".jstree-children").first()},get_string:function(e){var s=this.settings.core.strings;return l.vakata.is_function(s)?s.call(this,e):s&&s[e]?s[e]:e},_firstChild:function(e){for(e=e?e.firstChild:null;e!==null&&e.nodeType!==1;)e=e.nextSibling;return e},_nextSibling:function(e){for(e=e?e.nextSibling:null;e!==null&&e.nodeType!==1;)e=e.nextSibling;return e},_previousSibling:function(e){for(e=e?e.previousSibling:null;e!==null&&e.nodeType!==1;)e=e.previousSibling;return e},get_node:function(e,s){e&&(e.id||e.id===0)&&(e=e.id),e instanceof l&&e.length&&e[0].id&&(e=e[0].id);var t;try{if(this._model.data[e])e=this._model.data[e];else if(typeof e=="string"&&this._model.data[e.replace(/^#/,"")])e=this._model.data[e.replace(/^#/,"")];else if(typeof e=="string"&&(t=l("#"+e.replace(l.jstree.idregex,"\\$&"),this.element)).length&&this._model.data[t.closest(".jstree-node").attr("id")])e=this._model.data[t.closest(".jstree-node").attr("id")];else if((t=this.element.find(e)).length&&this._model.data[t.closest(".jstree-node").attr("id")])e=this._model.data[t.closest(".jstree-node").attr("id")];else if((t=this.element.find(e)).length&&t.hasClass("jstree"))e=this._model.data[l.jstree.root];else return!1;return s&&(e=e.id===l.jstree.root?this.element:l("#"+e.id.replace(l.jstree.idregex,"\\$&"),this.element)),e}catch{return!1}},get_path:function(e,s,t){if(e=e.parents?e:this.get_node(e),!e||e.id===l.jstree.root||!e.parents)return!1;var i,r,a=[];for(a.push(t?e.id:e.text),i=0,r=e.parents.length;i<r;i++)a.push(t?e.parents[i]:this.get_text(e.parents[i]));return a=a.reverse().slice(1),s?a.join(s):a},get_next_dom:function(e,s){var t;if(e=this.get_node(e,!0),e[0]===this.element[0]){for(t=this._firstChild(this.get_container_ul()[0]);t&&t.offsetHeight===0;)t=this._nextSibling(t);return t?l(t):!1}if(!e||!e.length)return!1;if(s){t=e[0];do t=this._nextSibling(t);while(t&&t.offsetHeight===0);return t?l(t):!1}if(e.hasClass("jstree-open")){for(t=this._firstChild(e.children(".jstree-children")[0]);t&&t.offsetHeight===0;)t=this._nextSibling(t);if(t!==null)return l(t)}t=e[0];do t=this._nextSibling(t);while(t&&t.offsetHeight===0);return t!==null?l(t):e.parentsUntil(".jstree",".jstree-node").nextAll(".jstree-node:visible").first()},get_prev_dom:function(e,s){var t;if(e=this.get_node(e,!0),e[0]===this.element[0]){for(t=this.get_container_ul()[0].lastChild;t&&t.offsetHeight===0;)t=this._previousSibling(t);return t?l(t):!1}if(!e||!e.length)return!1;if(s){t=e[0];do t=this._previousSibling(t);while(t&&t.offsetHeight===0);return t?l(t):!1}t=e[0];do t=this._previousSibling(t);while(t&&t.offsetHeight===0);if(t!==null){for(e=l(t);e.hasClass("jstree-open");)e=e.children(".jstree-children").first().children(".jstree-node:visible:last");return e}return t=e[0].parentNode.parentNode,t&&t.className&&t.className.indexOf("jstree-node")!==-1?l(t):!1},get_parent:function(e){return e=this.get_node(e),!e||e.id===l.jstree.root?!1:e.parent},get_children_dom:function(e){return e=this.get_node(e,!0),e[0]===this.element[0]?this.get_container_ul().children(".jstree-node"):!e||!e.length?!1:e.children(".jstree-children").children(".jstree-node")},is_parent:function(e){return e=this.get_node(e),e&&(e.state.loaded===!1||e.children.length>0)},is_loaded:function(e){return e=this.get_node(e),e&&e.state.loaded},is_loading:function(e){return e=this.get_node(e),e&&e.state&&e.state.loading},is_open:function(e){return e=this.get_node(e),e&&e.state.opened},is_closed:function(e){return e=this.get_node(e),e&&this.is_parent(e)&&!e.state.opened},is_leaf:function(e){return!this.is_parent(e)},load_node:function(e,s){var t=this.get_node(e,!0),i,r,a,n,c;if(l.vakata.is_array(e))return this._load_nodes(e.slice(),s),!0;if(e=this.get_node(e),!e)return s&&s.call(this,e,!1),!1;if(e.state.loaded){for(e.state.loaded=!1,a=0,n=e.parents.length;a<n;a++)this._model.data[e.parents[a]].children_d=l.vakata.array_filter(this._model.data[e.parents[a]].children_d,function(d){return l.inArray(d,e.children_d)===-1});for(i=0,r=e.children_d.length;i<r;i++)this._model.data[e.children_d[i]].state.selected&&(c=!0),delete this._model.data[e.children_d[i]];c&&(this._data.core.selected=l.vakata.array_filter(this._data.core.selected,function(d){return l.inArray(d,e.children_d)===-1})),e.children=[],e.children_d=[],c&&this.trigger("changed",{action:"load_node",node:e,selected:this._data.core.selected})}return e.state.failed=!1,e.state.loading=!0,e.id!==l.jstree.root?t.children(".jstree-anchor").attr("aria-busy",!0):t.attr("aria-busy",!0),t.addClass("jstree-loading"),this._load_node(e,(function(d){e=this._model.data[e.id],e.state.loading=!1,e.state.loaded=d,e.state.failed=!e.state.loaded;var h=this.get_node(e,!0),o=0,f=0,_=this._model.data,u=!1;for(o=0,f=e.children.length;o<f;o++)if(_[e.children[o]]&&!_[e.children[o]].state.hidden){u=!0;break}e.state.loaded&&h&&h.length&&(h.removeClass("jstree-closed jstree-open jstree-leaf"),u?e.id!=="#"&&h.addClass(e.state.opened?"jstree-open":"jstree-closed"):h.addClass("jstree-leaf")),e.id!==l.jstree.root?h.children(".jstree-anchor").attr("aria-busy",!1):h.attr("aria-busy",!1),h.removeClass("jstree-loading"),this.trigger("load_node",{node:e,status:d}),s&&s.call(this,e,d)}).bind(this)),!0},_load_nodes:function(e,s,t,i){var r=!0,a=function(){this._load_nodes(e,s,!0)},n=this._model.data,c,d,h=[];for(c=0,d=e.length;c<d;c++)n[e[c]]&&(!n[e[c]].state.loaded&&!n[e[c]].state.failed||!t&&i)&&(this.is_loading(e[c])||this.load_node(e[c],a),r=!1);if(r){for(c=0,d=e.length;c<d;c++)n[e[c]]&&n[e[c]].state.loaded&&h.push(e[c]);s&&!s.done&&(s.call(this,h),s.done=!0)}},load_all:function(e,s){if(e||(e=l.jstree.root),e=this.get_node(e),!e)return!1;var t=[],i=this._model.data,r=i[e.id].children_d,a,n;for(e.state&&!e.state.loaded&&t.push(e.id),a=0,n=r.length;a<n;a++)i[r[a]]&&i[r[a]].state&&!i[r[a]].state.loaded&&t.push(r[a]);t.length?this._load_nodes(t,function(){this.load_all(e,s)}):(s&&s.call(this,e),this.trigger("load_all",{node:e}))},_load_node:function(e,s){var t=this.settings.core.data,i,r=function(){return this.nodeType!==3&&this.nodeType!==8};return t?l.vakata.is_function(t)?t.call(this,e,(function(a){a===!1?s.call(this,!1):this[typeof a=="string"?"_append_html_data":"_append_json_data"](e,typeof a=="string"?l(l.parseHTML(a)).filter(r):a,function(n){s.call(this,n)})}).bind(this)):typeof t=="object"?t.url?(t=l.extend(!0,{},t),l.vakata.is_function(t.url)&&(t.url=t.url.call(this,e)),l.vakata.is_function(t.data)&&(t.data=t.data.call(this,e)),l.ajax(t).done((function(a,n,c){var d=c.getResponseHeader("Content-Type");return d&&d.indexOf("json")!==-1||typeof a=="object"?this._append_json_data(e,a,function(h){s.call(this,h)}):d&&d.indexOf("html")!==-1||typeof a=="string"?this._append_html_data(e,l(l.parseHTML(a)).filter(r),function(h){s.call(this,h)}):(this._data.core.last_error={error:"ajax",plugin:"core",id:"core_04",reason:"Could not load node",data:JSON.stringify({id:e.id,xhr:c})},this.settings.core.error.call(this,this._data.core.last_error),s.call(this,!1))}).bind(this)).fail((function(a){this._data.core.last_error={error:"ajax",plugin:"core",id:"core_04",reason:"Could not load node",data:JSON.stringify({id:e.id,xhr:a})},s.call(this,!1),this.settings.core.error.call(this,this._data.core.last_error)}).bind(this))):(l.vakata.is_array(t)?i=l.extend(!0,[],t):l.isPlainObject(t)?i=l.extend(!0,{},t):i=t,e.id===l.jstree.root?this._append_json_data(e,i,function(a){s.call(this,a)}):(this._data.core.last_error={error:"nodata",plugin:"core",id:"core_05",reason:"Could not load node",data:JSON.stringify({id:e.id})},this.settings.core.error.call(this,this._data.core.last_error),s.call(this,!1))):typeof t=="string"?e.id===l.jstree.root?this._append_html_data(e,l(l.parseHTML(t)).filter(r),function(a){s.call(this,a)}):(this._data.core.last_error={error:"nodata",plugin:"core",id:"core_06",reason:"Could not load node",data:JSON.stringify({id:e.id})},this.settings.core.error.call(this,this._data.core.last_error),s.call(this,!1)):s.call(this,!1):e.id===l.jstree.root?this._append_html_data(e,this._data.core.original_container_html.clone(!0),function(a){s.call(this,a)}):s.call(this,!1)},_node_changed:function(e){e=this.get_node(e),e&&l.inArray(e.id,this._model.changed)===-1&&this._model.changed.push(e.id)},_append_html_data:function(e,s,t){e=this.get_node(e),e.children=[],e.children_d=[];var i=s.is("ul")?s.children():s,r=e.id,a=[],n=[],c=this._model.data,d=c[r],h=this._data.core.selected.length,o,f,_;for(i.each((function(u,g){o=this._parse_model_from_html(l(g),r,d.parents.concat()),o&&(a.push(o),n.push(o),c[o].children_d.length&&(n=n.concat(c[o].children_d)))}).bind(this)),d.children=a,d.children_d=n,f=0,_=d.parents.length;f<_;f++)c[d.parents[f]].children_d=c[d.parents[f]].children_d.concat(n);this.trigger("model",{nodes:n,parent:r}),r!==l.jstree.root?(this._node_changed(r),this.redraw()):(this.get_container_ul().children(".jstree-initial-node").remove(),this.redraw(!0)),this._data.core.selected.length!==h&&this.trigger("changed",{action:"model",selected:this._data.core.selected}),t.call(this,!0)},_append_json_data:function(e,s,t,i){if(this.element!==null){e=this.get_node(e),e.children=[],e.children_d=[],s.d&&(s=s.d,typeof s=="string"&&(s=JSON.parse(s))),l.vakata.is_array(s)||(s=[s]);var r=null,a={df:this._model.default_state,dat:s,par:e.id,m:this._model.data,t_id:this._id,t_cnt:this._cnt,sel:this._data.core.selected},n=this,c=function(h,o){h.data&&(h=h.data);var f=h.dat,_=h.par,u=[],g=[],p=[],m=h.df,N=h.t_id,O=h.t_cnt,x=h.m,A=x[_],K=h.sel,P,C,S,L,F=function(v,H,E){E?E=E.concat():E=[],H&&E.unshift(H);var j=v.id.toString(),w,B,Y,z,k={id:j,text:v.text||"",icon:v.icon!==o?v.icon:!0,parent:H,parents:E,children:v.children||[],children_d:v.children_d||[],data:v.data,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(w in m)m.hasOwnProperty(w)&&(k.state[w]=m[w]);if(v&&v.data&&v.data.jstree&&v.data.jstree.icon&&(k.icon=v.data.jstree.icon),(k.icon===o||k.icon===null||k.icon==="")&&(k.icon=!0),v&&v.data&&(k.data=v.data,v.data.jstree))for(w in v.data.jstree)v.data.jstree.hasOwnProperty(w)&&(k.state[w]=v.data.jstree[w]);if(v&&typeof v.state=="object")for(w in v.state)v.state.hasOwnProperty(w)&&(k.state[w]=v.state[w]);if(v&&typeof v.li_attr=="object")for(w in v.li_attr)v.li_attr.hasOwnProperty(w)&&(k.li_attr[w]=v.li_attr[w]);if(k.li_attr.id||(k.li_attr.id=j),v&&typeof v.a_attr=="object")for(w in v.a_attr)v.a_attr.hasOwnProperty(w)&&(k.a_attr[w]=v.a_attr[w]);for(v&&v.children&&v.children===!0&&(k.state.loaded=!1,k.children=[],k.children_d=[]),x[k.id]=k,w=0,B=k.children.length;w<B;w++)Y=F(x[k.children[w]],k.id,E),z=x[Y],k.children_d.push(Y),z.children_d.length&&(k.children_d=k.children_d.concat(z.children_d));return delete v.data,delete v.children,x[k.id].original=v,k.state.selected&&p.push(k.id),k.id},X=function(v,H,E){E?E=E.concat():E=[],H&&E.unshift(H);var j=!1,w,B,Y,z,k;do j="j"+N+"_"+ ++O;while(x[j]);k={id:!1,text:typeof v=="string"?v:"",icon:typeof v=="object"&&v.icon!==o?v.icon:!0,parent:H,parents:E,children:[],children_d:[],data:null,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(w in m)m.hasOwnProperty(w)&&(k.state[w]=m[w]);if(v&&(v.id||v.id===0)&&(k.id=v.id.toString()),v&&v.text&&(k.text=v.text),v&&v.data&&v.data.jstree&&v.data.jstree.icon&&(k.icon=v.data.jstree.icon),(k.icon===o||k.icon===null||k.icon==="")&&(k.icon=!0),v&&v.data&&(k.data=v.data,v.data.jstree))for(w in v.data.jstree)v.data.jstree.hasOwnProperty(w)&&(k.state[w]=v.data.jstree[w]);if(v&&typeof v.state=="object")for(w in v.state)v.state.hasOwnProperty(w)&&(k.state[w]=v.state[w]);if(v&&typeof v.li_attr=="object")for(w in v.li_attr)v.li_attr.hasOwnProperty(w)&&(k.li_attr[w]=v.li_attr[w]);if(k.li_attr.id&&!(k.id||k.id===0)&&(k.id=k.li_attr.id.toString()),k.id||k.id===0||(k.id=j),k.li_attr.id||(k.li_attr.id=k.id),v&&typeof v.a_attr=="object")for(w in v.a_attr)v.a_attr.hasOwnProperty(w)&&(k.a_attr[w]=v.a_attr[w]);if(v&&v.children&&v.children.length){for(w=0,B=v.children.length;w<B;w++)Y=X(v.children[w],k.id,E),z=x[Y],k.children.push(Y),z.children_d.length&&(k.children_d=k.children_d.concat(z.children_d));k.children_d=k.children_d.concat(k.children)}return v&&v.children&&v.children===!0&&(k.state.loaded=!1,k.children=[],k.children_d=[]),delete v.data,delete v.children,k.original=v,x[k.id]=k,k.state.selected&&p.push(k.id),k.id};if(f.length&&f[0].id!==o&&f[0].parent!==o){for(C=0,S=f.length;C<S;C++)f[C].children||(f[C].children=[]),f[C].state||(f[C].state={}),x[f[C].id.toString()]=f[C];for(C=0,S=f.length;C<S;C++){if(!x[f[C].parent.toString()]){typeof n<"u"&&(n._data.core.last_error={error:"parse",plugin:"core",id:"core_07",reason:"Node with invalid parent",data:JSON.stringify({id:f[C].id.toString(),parent:f[C].parent.toString()})},n.settings.core.error.call(n,n._data.core.last_error));continue}x[f[C].parent.toString()].children.push(f[C].id.toString()),A.children_d.push(f[C].id.toString())}for(C=0,S=A.children.length;C<S;C++)P=F(x[A.children[C]],_,A.parents.concat()),g.push(P),x[P].children_d.length&&(g=g.concat(x[P].children_d));for(C=0,S=A.parents.length;C<S;C++)x[A.parents[C]].children_d=x[A.parents[C]].children_d.concat(g);L={cnt:O,mod:x,sel:K,par:_,dpc:g,add:p}}else{for(C=0,S=f.length;C<S;C++)P=X(f[C],_,A.parents.concat()),P&&(u.push(P),g.push(P),x[P].children_d.length&&(g=g.concat(x[P].children_d)));for(A.children=u,A.children_d=g,C=0,S=A.parents.length;C<S;C++)x[A.parents[C]].children_d=x[A.parents[C]].children_d.concat(g);L={cnt:O,mod:x,sel:K,par:_,dpc:g,add:p}}if(typeof window>"u"||typeof window.document>"u")postMessage(L);else return L},d=function(h,o){if(this.element!==null){this._cnt=h.cnt;var f,_=this._model.data;for(f in _)_.hasOwnProperty(f)&&_[f].state&&_[f].state.loading&&h.mod[f]&&(h.mod[f].state.loading=!0);if(this._model.data=h.mod,o){var u,g=h.add,p=h.sel,m=this._data.core.selected.slice();if(_=this._model.data,p.length!==m.length||l.vakata.array_unique(p.concat(m)).length!==p.length){for(f=0,u=p.length;f<u;f++)l.inArray(p[f],g)===-1&&l.inArray(p[f],m)===-1&&(_[p[f]].state.selected=!1);for(f=0,u=m.length;f<u;f++)l.inArray(m[f],p)===-1&&(_[m[f]].state.selected=!0)}}h.add.length&&(this._data.core.selected=this._data.core.selected.concat(h.add)),this.trigger("model",{nodes:h.dpc,parent:h.par}),h.par!==l.jstree.root?(this._node_changed(h.par),this.redraw()):this.redraw(!0),h.add.length&&this.trigger("changed",{action:"model",selected:this._data.core.selected}),!o&&W?W(function(){t.call(n,!0)}):t.call(n,!0)}};if(this.settings.core.worker&&window.Blob&&window.URL&&window.Worker)try{this._wrk===null&&(this._wrk=window.URL.createObjectURL(new window.Blob(["self.onmessage = "+c.toString()],{type:"text/javascript"}))),!this._data.core.working||i?(this._data.core.working=!0,r=new window.Worker(this._wrk),r.onmessage=(function(h){d.call(this,h.data,!0);try{r.terminate(),r=null}catch{}this._data.core.worker_queue.length?this._append_json_data.apply(this,this._data.core.worker_queue.shift()):this._data.core.working=!1}).bind(this),r.onerror=(function(h){d.call(this,c(a),!1),this._data.core.worker_queue.length?this._append_json_data.apply(this,this._data.core.worker_queue.shift()):this._data.core.working=!1}).bind(this),a.par?r.postMessage(a):this._data.core.worker_queue.length?this._append_json_data.apply(this,this._data.core.worker_queue.shift()):this._data.core.working=!1):this._data.core.worker_queue.push([e,s,t,!0])}catch{d.call(this,c(a),!1),this._data.core.worker_queue.length?this._append_json_data.apply(this,this._data.core.worker_queue.shift()):this._data.core.working=!1}else d.call(this,c(a),!1)}},_parse_model_from_html:function(e,s,t){t?t=[].concat(t):t=[],s&&t.unshift(s);var i,r,a=this._model.data,n={id:!1,text:!1,icon:!0,parent:s,parents:t,children:[],children_d:[],data:null,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1},c,d,h;for(c in this._model.default_state)this._model.default_state.hasOwnProperty(c)&&(n.state[c]=this._model.default_state[c]);if(d=l.vakata.attributes(e,!0),l.each(d,function(o,f){if(f=l.vakata.trim(f),!f.length)return!0;n.li_attr[o]=f,o==="id"&&(n.id=f.toString())}),d=e.children("a").first(),d.length&&(d=l.vakata.attributes(d,!0),l.each(d,function(o,f){f=l.vakata.trim(f),f.length&&(n.a_attr[o]=f)})),d=e.children("a").first().length?e.children("a").first().clone():e.clone(),d.children("ins, i, ul").remove(),d=d.html(),d=l("<div></div>").html(d),n.text=this.settings.core.force_text?d.text():d.html(),d=e.data(),n.data=d?l.extend(!0,{},d):null,n.state.opened=e.hasClass("jstree-open"),n.state.selected=e.children("a").hasClass("jstree-clicked"),n.state.disabled=e.children("a").hasClass("jstree-disabled"),n.data&&n.data.jstree)for(c in n.data.jstree)n.data.jstree.hasOwnProperty(c)&&(n.state[c]=n.data.jstree[c]);d=e.children("a").children(".jstree-themeicon"),d.length&&(n.icon=d.hasClass("jstree-themeicon-hidden")?!1:d.attr("rel")),n.state.icon!==y&&(n.icon=n.state.icon),(n.icon===y||n.icon===null||n.icon==="")&&(n.icon=!0),d=e.children("ul").children("li");do h="j"+this._id+"_"+ ++this._cnt;while(a[h]);return n.id=n.li_attr.id?n.li_attr.id.toString():h,d.length?(d.each((function(o,f){i=this._parse_model_from_html(l(f),n.id,t),r=this._model.data[i],n.children.push(i),r.children_d.length&&(n.children_d=n.children_d.concat(r.children_d))}).bind(this)),n.children_d=n.children_d.concat(n.children)):e.hasClass("jstree-closed")&&(n.state.loaded=!1),n.li_attr.class&&(n.li_attr.class=n.li_attr.class.replace("jstree-closed","").replace("jstree-open","")),n.a_attr.class&&(n.a_attr.class=n.a_attr.class.replace("jstree-clicked","").replace("jstree-disabled","")),a[n.id]=n,n.state.selected&&this._data.core.selected.push(n.id),n.id},_parse_model_from_flat_json:function(e,s,t){t?t=t.concat():t=[],s&&t.unshift(s);var i=e.id.toString(),r=this._model.data,a=this._model.default_state,n,c,d,h,o={id:i,text:e.text||"",icon:e.icon!==y?e.icon:!0,parent:s,parents:t,children:e.children||[],children_d:e.children_d||[],data:e.data,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(n in a)a.hasOwnProperty(n)&&(o.state[n]=a[n]);if(e&&e.data&&e.data.jstree&&e.data.jstree.icon&&(o.icon=e.data.jstree.icon),(o.icon===y||o.icon===null||o.icon==="")&&(o.icon=!0),e&&e.data&&(o.data=e.data,e.data.jstree))for(n in e.data.jstree)e.data.jstree.hasOwnProperty(n)&&(o.state[n]=e.data.jstree[n]);if(e&&typeof e.state=="object")for(n in e.state)e.state.hasOwnProperty(n)&&(o.state[n]=e.state[n]);if(e&&typeof e.li_attr=="object")for(n in e.li_attr)e.li_attr.hasOwnProperty(n)&&(o.li_attr[n]=e.li_attr[n]);if(o.li_attr.id||(o.li_attr.id=i),e&&typeof e.a_attr=="object")for(n in e.a_attr)e.a_attr.hasOwnProperty(n)&&(o.a_attr[n]=e.a_attr[n]);for(e&&e.children&&e.children===!0&&(o.state.loaded=!1,o.children=[],o.children_d=[]),r[o.id]=o,n=0,c=o.children.length;n<c;n++)d=this._parse_model_from_flat_json(r[o.children[n]],o.id,t),h=r[d],o.children_d.push(d),h.children_d.length&&(o.children_d=o.children_d.concat(h.children_d));return delete e.data,delete e.children,r[o.id].original=e,o.state.selected&&this._data.core.selected.push(o.id),o.id},_parse_model_from_json:function(e,s,t){t?t=t.concat():t=[],s&&t.unshift(s);var i=!1,r,a,n,c,d=this._model.data,h=this._model.default_state,o;do i="j"+this._id+"_"+ ++this._cnt;while(d[i]);o={id:!1,text:typeof e=="string"?e:"",icon:typeof e=="object"&&e.icon!==y?e.icon:!0,parent:s,parents:t,children:[],children_d:[],data:null,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(r in h)h.hasOwnProperty(r)&&(o.state[r]=h[r]);if(e&&(e.id||e.id===0)&&(o.id=e.id.toString()),e&&e.text&&(o.text=e.text),e&&e.data&&e.data.jstree&&e.data.jstree.icon&&(o.icon=e.data.jstree.icon),(o.icon===y||o.icon===null||o.icon==="")&&(o.icon=!0),e&&e.data&&(o.data=e.data,e.data.jstree))for(r in e.data.jstree)e.data.jstree.hasOwnProperty(r)&&(o.state[r]=e.data.jstree[r]);if(e&&typeof e.state=="object")for(r in e.state)e.state.hasOwnProperty(r)&&(o.state[r]=e.state[r]);if(e&&typeof e.li_attr=="object")for(r in e.li_attr)e.li_attr.hasOwnProperty(r)&&(o.li_attr[r]=e.li_attr[r]);if(o.li_attr.id&&!(o.id||o.id===0)&&(o.id=o.li_attr.id.toString()),o.id||o.id===0||(o.id=i),o.li_attr.id||(o.li_attr.id=o.id),e&&typeof e.a_attr=="object")for(r in e.a_attr)e.a_attr.hasOwnProperty(r)&&(o.a_attr[r]=e.a_attr[r]);if(e&&e.children&&e.children.length){for(r=0,a=e.children.length;r<a;r++)n=this._parse_model_from_json(e.children[r],o.id,t),c=d[n],o.children.push(n),c.children_d.length&&(o.children_d=o.children_d.concat(c.children_d));o.children_d=o.children.concat(o.children_d)}return e&&e.children&&e.children===!0&&(o.state.loaded=!1,o.children=[],o.children_d=[]),delete e.data,delete e.children,o.original=e,d[o.id]=o,o.state.selected&&this._data.core.selected.push(o.id),o.id},_redraw:function(){var e=this._model.force_full_redraw?this._model.data[l.jstree.root].children.concat([]):this._model.changed.concat([]),s=T.createElement("UL"),t,i,r,a=this._data.core.focused;for(i=0,r=e.length;i<r;i++)t=this.redraw_node(e[i],!0,this._model.force_full_redraw),t&&this._model.force_full_redraw&&s.appendChild(t);this._model.force_full_redraw&&(s.className=this.get_container_ul()[0].className,s.setAttribute("role","presentation"),this.element.empty().append(s)),a!==null&&this.settings.core.restore_focus&&(t=this.get_node(a,!0),t&&t.length&&t.children(".jstree-anchor")[0]!==T.activeElement?t.children(".jstree-anchor").trigger("focus"):this._data.core.focused=null),this._model.force_full_redraw=!1,this._model.changed=[],this.trigger("redraw",{nodes:e})},redraw:function(e){e&&(this._model.force_full_redraw=!0),this._redraw()},draw_children:function(e){var s=this.get_node(e),t=!1,i=!1,r=!1,a=T;if(!s)return!1;if(s.id===l.jstree.root)return this.redraw(!0);if(e=this.get_node(e,!0),!e||!e.length)return!1;if(e.children(".jstree-children").remove(),e=e[0],s.children.length&&s.state.loaded){for(r=a.createElement("UL"),r.setAttribute("role","group"),r.className="jstree-children",t=0,i=s.children.length;t<i;t++)r.appendChild(this.redraw_node(s.children[t],!0,!0));e.appendChild(r)}},redraw_node:function(e,s,t,i){var r=this.get_node(e),a=!1,n=!1,c=!1,d=!1,h=!1,o=!1,f="",_=T,u=this._model.data,g=!1,p=null,m=0,N=0,O=!1,x=!1;if(!r)return!1;if(r.id===l.jstree.root)return this.redraw(!0);if(s=s||r.children.length===0,e=T.querySelector?this.element[0].querySelector("#"+("0123456789".indexOf(r.id[0])!==-1?"\\3"+r.id[0]+" "+r.id.substr(1).replace(l.jstree.idregex,"\\$&"):r.id.replace(l.jstree.idregex,"\\$&"))):T.getElementById(r.id),e)e=l(e),t||(a=e.parent().parent()[0],a===this.element[0]&&(a=null),n=e.index()),!s&&r.children.length&&!e.children(".jstree-children").length&&(s=!0),s||(c=e.children(".jstree-children")[0]),g=e.children(".jstree-anchor")[0]===T.activeElement,e.remove();else if(s=!0,!t){if(a=r.parent!==l.jstree.root?l("#"+r.parent.replace(l.jstree.idregex,"\\$&"),this.element)[0]:null,a!==null&&(!a||!u[r.parent].state.opened))return!1;n=l.inArray(r.id,a===null?u[l.jstree.root].children:u[r.parent].children)}e=this._data.core.node.cloneNode(!0),f="jstree-node ";for(d in r.li_attr)if(r.li_attr.hasOwnProperty(d)){if(d==="id")continue;d!=="class"?e.setAttribute(d,r.li_attr[d]):f+=r.li_attr[d]}for(r.a_attr.id||(r.a_attr.id=r.id+"_anchor"),e.childNodes[1].setAttribute("aria-selected",!!r.state.selected),e.childNodes[1].setAttribute("aria-level",r.parents.length),this.settings.core.compute_elements_positions&&(e.childNodes[1].setAttribute("aria-setsize",u[r.parent].children.length),e.childNodes[1].setAttribute("aria-posinset",u[r.parent].children.indexOf(r.id)+1)),r.state.disabled&&e.childNodes[1].setAttribute("aria-disabled",!0),d=0,h=r.children.length;d<h;d++)if(!u[r.children[d]].state.hidden){O=!0;break}if(r.parent!==null&&u[r.parent]&&!r.state.hidden&&(d=l.inArray(r.id,u[r.parent].children),x=r.id,d!==-1))for(d++,h=u[r.parent].children.length;d<h&&(u[u[r.parent].children[d]].state.hidden||(x=u[r.parent].children[d]),x===r.id);d++);r.state.hidden&&(f+=" jstree-hidden"),r.state.loading&&(f+=" jstree-loading"),r.state.loaded&&!O?f+=" jstree-leaf":(f+=r.state.opened&&r.state.loaded?" jstree-open":" jstree-closed",e.childNodes[1].setAttribute("aria-expanded",r.state.opened&&r.state.loaded)),x===r.id&&(f+=" jstree-last"),e.id=r.id,e.className=f,f=(r.state.selected?" jstree-clicked":"")+(r.state.disabled?" jstree-disabled":"");for(h in r.a_attr)if(r.a_attr.hasOwnProperty(h)){if(h==="href"&&r.a_attr[h]==="#")continue;h!=="class"?e.childNodes[1].setAttribute(h,r.a_attr[h]):f+=" "+r.a_attr[h]}if(f.length&&(e.childNodes[1].className="jstree-anchor "+f),(r.icon&&r.icon!==!0||r.icon===!1)&&(r.icon===!1?e.childNodes[1].childNodes[0].className+=" jstree-themeicon-hidden":r.icon.indexOf("/")===-1&&r.icon.indexOf(".")===-1?e.childNodes[1].childNodes[0].className+=" "+r.icon+" jstree-themeicon-custom":(e.childNodes[1].childNodes[0].style.backgroundImage='url("'+r.icon+'")',e.childNodes[1].childNodes[0].style.backgroundPosition="center center",e.childNodes[1].childNodes[0].style.backgroundSize="auto",e.childNodes[1].childNodes[0].className+=" jstree-themeicon-custom")),this.settings.core.force_text?e.childNodes[1].appendChild(_.createTextNode(r.text)):e.childNodes[1].innerHTML+=r.text,s&&r.children.length&&(r.state.opened||i)&&r.state.loaded){for(o=_.createElement("UL"),o.setAttribute("role","group"),o.className="jstree-children",d=0,h=r.children.length;d<h;d++)o.appendChild(this.redraw_node(r.children[d],s,!0));e.appendChild(o)}if(c&&e.appendChild(c),!t){for(a||(a=this.element[0]),d=0,h=a.childNodes.length;d<h;d++)if(a.childNodes[d]&&a.childNodes[d].className&&a.childNodes[d].className.indexOf("jstree-children")!==-1){p=a.childNodes[d];break}p||(p=_.createElement("UL"),p.setAttribute("role","group"),p.className="jstree-children",a.appendChild(p)),a=p,n<a.childNodes.length?a.insertBefore(e,a.childNodes[n]):a.appendChild(e),g&&(m=this.element[0].scrollTop,N=this.element[0].scrollLeft,e.childNodes[1].focus(),this.element[0].scrollTop=m,this.element[0].scrollLeft=N)}return r.state.opened&&!r.state.loaded&&(r.state.opened=!1,setTimeout((function(){this.open_node(r.id,!1,0)}).bind(this),0)),e},open_node:function(e,s,t){var i,r,a,n;if(l.vakata.is_array(e)){for(e=e.slice(),i=0,r=e.length;i<r;i++)this.open_node(e[i],s,t);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;if(t=t===y?this.settings.core.animation:t,!this.is_closed(e))return s&&s.call(this,e,!1),!1;if(this.is_loaded(e))return a=this.get_node(e,!0),n=this,a.length&&(t&&a.children(".jstree-children").length&&a.children(".jstree-children").stop(!0,!0),e.children.length&&!this._firstChild(a.children(".jstree-children")[0])&&this.draw_children(e),t?(this.trigger("before_open",{node:e}),a.children(".jstree-children").css("display","none").end().removeClass("jstree-closed").addClass("jstree-open").children(".jstree-anchor").attr("aria-expanded",!0).end().children(".jstree-children").stop(!0,!0).slideDown(t,function(){this.style.display="",n.element&&n.trigger("after_open",{node:e})})):(this.trigger("before_open",{node:e}),a[0].className=a[0].className.replace("jstree-closed","jstree-open"),a[0].childNodes[1].setAttribute("aria-expanded",!0))),e.state.opened=!0,s&&s.call(this,e,!0),a.length||this.trigger("before_open",{node:e}),this.trigger("open_node",{node:e}),(!t||!a.length)&&this.trigger("after_open",{node:e}),!0;if(this.is_loading(e))return setTimeout((function(){this.open_node(e,s,t)}).bind(this),500);this.load_node(e,function(c,d){return d?this.open_node(c,s,t):s?s.call(this,c,!1):!1})},_open_to:function(e){if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;var s,t,i=e.parents;for(s=0,t=i.length;s<t;s+=1)s!==l.jstree.root&&this.open_node(i[s],!1,0);return l("#"+e.id.replace(l.jstree.idregex,"\\$&"),this.element)},close_node:function(e,s){var t,i,r,a;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.close_node(e[t],s);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root||this.is_closed(e))return!1;s=s===y?this.settings.core.animation:s,r=this,a=this.get_node(e,!0),e.state.opened=!1,this.trigger("close_node",{node:e}),a.length?s?a.children(".jstree-children").attr("style","display:block !important").end().removeClass("jstree-open").addClass("jstree-closed").children(".jstree-anchor").attr("aria-expanded",!1).end().children(".jstree-children").stop(!0,!0).slideUp(s,function(){this.style.display="",a.children(".jstree-children").remove(),r.element&&r.trigger("after_close",{node:e})}):(a[0].className=a[0].className.replace("jstree-open","jstree-closed"),a.children(".jstree-anchor").attr("aria-expanded",!1),a.children(".jstree-children").remove(),this.trigger("after_close",{node:e})):this.trigger("after_close",{node:e})},toggle_node:function(e){var s,t;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.toggle_node(e[s]);return!0}if(this.is_closed(e))return this.open_node(e);if(this.is_open(e))return this.close_node(e)},open_all:function(e,s,t){if(e||(e=l.jstree.root),e=this.get_node(e),!e)return!1;var i=e.id===l.jstree.root?this.get_container_ul():this.get_node(e,!0),r,a,n;if(!i.length){for(r=0,a=e.children_d.length;r<a;r++)this.is_closed(this._model.data[e.children_d[r]])&&(this._model.data[e.children_d[r]].state.opened=!0);return this.trigger("open_all",{node:e})}t=t||i,n=this,i=this.is_closed(e)?i.find(".jstree-closed").addBack():i.find(".jstree-closed"),i.each(function(){n.open_node(this,function(c,d){d&&this.is_parent(c)&&this.open_all(c,s,t)},s||0)}),t.find(".jstree-closed").length===0&&this.trigger("open_all",{node:this.get_node(t)})},close_all:function(e,s){if(e||(e=l.jstree.root),e=this.get_node(e),!e)return!1;var t=e.id===l.jstree.root?this.get_container_ul():this.get_node(e,!0),i=this,r,a;for(t.length&&(t=this.is_open(e)?t.find(".jstree-open").addBack():t.find(".jstree-open"),l(t.get().reverse()).each(function(){i.close_node(this,s||0)})),r=0,a=e.children_d.length;r<a;r++)this._model.data[e.children_d[r]].state.opened=!1;this.trigger("close_all",{node:e})},is_disabled:function(e){return e=this.get_node(e),e&&e.state&&e.state.disabled},enable_node:function(e){var s,t;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.enable_node(e[s]);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;e.state.disabled=!1,this.get_node(e,!0).children(".jstree-anchor").removeClass("jstree-disabled").attr("aria-disabled",!1),this.trigger("enable_node",{node:e})},disable_node:function(e){var s,t;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.disable_node(e[s]);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;e.state.disabled=!0,this.get_node(e,!0).children(".jstree-anchor").addClass("jstree-disabled").attr("aria-disabled",!0),this.trigger("disable_node",{node:e})},is_hidden:function(e){return e=this.get_node(e),e.state.hidden===!0},hide_node:function(e,s){var t,i;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.hide_node(e[t],!0);return s||this.redraw(),!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;e.state.hidden||(e.state.hidden=!0,this._node_changed(e.parent),s||this.redraw(),this.trigger("hide_node",{node:e}))},show_node:function(e,s){var t,i;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.show_node(e[t],!0);return s||this.redraw(),!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;e.state.hidden&&(e.state.hidden=!1,this._node_changed(e.parent),s||this.redraw(),this.trigger("show_node",{node:e}))},hide_all:function(e){var s,t=this._model.data,i=[];for(s in t)t.hasOwnProperty(s)&&s!==l.jstree.root&&!t[s].state.hidden&&(t[s].state.hidden=!0,i.push(s));return this._model.force_full_redraw=!0,e||this.redraw(),this.trigger("hide_all",{nodes:i}),i},show_all:function(e){var s,t=this._model.data,i=[];for(s in t)t.hasOwnProperty(s)&&s!==l.jstree.root&&t[s].state.hidden&&(t[s].state.hidden=!1,i.push(s));return this._model.force_full_redraw=!0,e||this.redraw(),this.trigger("show_all",{nodes:i}),i},activate_node:function(e,s){if(this.is_disabled(e))return!1;if((!s||typeof s!="object")&&(s={}),this._data.core.last_clicked=this._data.core.last_clicked&&this._data.core.last_clicked.id!==y?this.get_node(this._data.core.last_clicked.id):null,this._data.core.last_clicked&&!this._data.core.last_clicked.state.selected&&(this._data.core.last_clicked=null),!this._data.core.last_clicked&&this._data.core.selected.length&&(this._data.core.last_clicked=this.get_node(this._data.core.selected[this._data.core.selected.length-1])),!this.settings.core.multiple||!s.metaKey&&!s.ctrlKey&&!s.shiftKey||s.shiftKey&&(!this._data.core.last_clicked||!this.get_parent(e)||this.get_parent(e)!==this._data.core.last_clicked.parent))!this.settings.core.multiple&&(s.metaKey||s.ctrlKey||s.shiftKey)&&this.is_selected(e)?this.deselect_node(e,!1,s):((this.settings.core.allow_reselect||!this.is_selected(e)||this._data.core.selected.length!==1)&&(this.deselect_all(!0),this.select_node(e,!1,!1,s)),this._data.core.last_clicked=this.get_node(e));else if(s.shiftKey){var t=this.get_node(e).id,i=this._data.core.last_clicked.id,r=this.get_node(this._data.core.last_clicked.parent).children,a=!1,n,c;for(n=0,c=r.length;n<c;n+=1)r[n]===t&&(a=!a),r[n]===i&&(a=!a),!this.is_disabled(r[n])&&(a||r[n]===t||r[n]===i)?this.is_hidden(r[n])||this.select_node(r[n],!0,!1,s):s.ctrlKey||this.deselect_node(r[n],!0,s);this.trigger("changed",{action:"select_node",node:this.get_node(e),selected:this._data.core.selected,event:s})}else this.is_selected(e)?this.deselect_node(e,!1,s):(s.ctrlKey&&(this._data.core.last_clicked=this.get_node(e)),this.select_node(e,!1,!1,s));this.trigger("activate_node",{node:this.get_node(e),event:s})},hover_node:function(e){if(e=this.get_node(e,!0),!e||!e.length||e.children(".jstree-hovered").length)return!1;var s=this.element.find(".jstree-hovered"),t=this.element;s&&s.length&&this.dehover_node(s),e.children(".jstree-anchor").addClass("jstree-hovered"),this.trigger("hover_node",{node:this.get_node(e)}),setTimeout(function(){t.attr("aria-activedescendant",e[0].id)},0)},dehover_node:function(e){if(e=this.get_node(e,!0),!e||!e.length||!e.children(".jstree-hovered").length)return!1;e.children(".jstree-anchor").removeClass("jstree-hovered"),this.trigger("dehover_node",{node:this.get_node(e)})},select_node:function(e,s,t,i){var r,a,n;if(l.vakata.is_array(e)){for(e=e.slice(),a=0,n=e.length;a<n;a++)this.select_node(e[a],s,t,i);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;r=this.get_node(e,!0),e.state.selected||(e.state.selected=!0,this._data.core.selected.push(e.id),t||(r=this._open_to(e)),r&&r.length&&r.children(".jstree-anchor").addClass("jstree-clicked").attr("aria-selected",!0),this.trigger("select_node",{node:e,selected:this._data.core.selected,event:i}),s||this.trigger("changed",{action:"select_node",node:e,selected:this._data.core.selected,event:i}))},deselect_node:function(e,s,t){var i,r,a;if(l.vakata.is_array(e)){for(e=e.slice(),i=0,r=e.length;i<r;i++)this.deselect_node(e[i],s,t);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;a=this.get_node(e,!0),e.state.selected&&(e.state.selected=!1,this._data.core.selected=l.vakata.array_remove_item(this._data.core.selected,e.id),a.length&&a.children(".jstree-anchor").removeClass("jstree-clicked").attr("aria-selected",!1),this.trigger("deselect_node",{node:e,selected:this._data.core.selected,event:t}),s||this.trigger("changed",{action:"deselect_node",node:e,selected:this._data.core.selected,event:t}))},select_all:function(e){var s=this._data.core.selected.concat([]),t,i;for(this._data.core.selected=this._model.data[l.jstree.root].children_d.concat(),t=0,i=this._data.core.selected.length;t<i;t++)this._model.data[this._data.core.selected[t]]&&(this._model.data[this._data.core.selected[t]].state.selected=!0);this.redraw(!0),this.trigger("select_all",{selected:this._data.core.selected}),e||this.trigger("changed",{action:"select_all",selected:this._data.core.selected,old_selection:s})},deselect_all:function(e){var s=this._data.core.selected.concat([]),t,i;for(t=0,i=this._data.core.selected.length;t<i;t++)this._model.data[this._data.core.selected[t]]&&(this._model.data[this._data.core.selected[t]].state.selected=!1);this._data.core.selected=[],this.element.find(".jstree-clicked").removeClass("jstree-clicked").attr("aria-selected",!1),this.trigger("deselect_all",{selected:this._data.core.selected,node:s}),e||this.trigger("changed",{action:"deselect_all",selected:this._data.core.selected,old_selection:s})},is_selected:function(e){return e=this.get_node(e),!e||e.id===l.jstree.root?!1:e.state.selected},get_selected:function(e){return e?l.map(this._data.core.selected,(function(s){return this.get_node(s)}).bind(this)):this._data.core.selected.slice()},get_top_selected:function(e){var s=this.get_selected(!0),t={},i,r,a,n;for(i=0,r=s.length;i<r;i++)t[s[i].id]=s[i];for(i=0,r=s.length;i<r;i++)for(a=0,n=s[i].children_d.length;a<n;a++)t[s[i].children_d[a]]&&delete t[s[i].children_d[a]];s=[];for(i in t)t.hasOwnProperty(i)&&s.push(i);return e?l.map(s,(function(c){return this.get_node(c)}).bind(this)):s},get_bottom_selected:function(e){var s=this.get_selected(!0),t=[],i,r;for(i=0,r=s.length;i<r;i++)s[i].children.length||t.push(s[i].id);return e?l.map(t,(function(a){return this.get_node(a)}).bind(this)):t},get_state:function(){var e={core:{open:[],loaded:[],scroll:{left:this.element.scrollLeft(),top:this.element.scrollTop()},selected:[]}},s;for(s in this._model.data)this._model.data.hasOwnProperty(s)&&s!==l.jstree.root&&(this._model.data[s].state.loaded&&this.settings.core.loaded_state&&e.core.loaded.push(s),this._model.data[s].state.opened&&e.core.open.push(s),this._model.data[s].state.selected&&e.core.selected.push(s));return e},set_state:function(e,s){if(e){if(e.core&&e.core.selected&&e.core.initial_selection===y&&(e.core.initial_selection=this._data.core.selected.concat([]).sort().join(",")),e.core){var t,i;if(e.core.loaded)return!this.settings.core.loaded_state||!l.vakata.is_array(e.core.loaded)||!e.core.loaded.length?(delete e.core.loaded,this.set_state(e,s)):this._load_nodes(e.core.loaded,function(r){delete e.core.loaded,this.set_state(e,s)}),!1;if(e.core.open)return!l.vakata.is_array(e.core.open)||!e.core.open.length?(delete e.core.open,this.set_state(e,s)):this._load_nodes(e.core.open,function(r){this.open_node(r,!1,0),delete e.core.open,this.set_state(e,s)}),!1;if(e.core.scroll)return e.core.scroll&&e.core.scroll.left!==y&&this.element.scrollLeft(e.core.scroll.left),e.core.scroll&&e.core.scroll.top!==y&&this.element.scrollTop(e.core.scroll.top),delete e.core.scroll,this.set_state(e,s),!1;if(e.core.selected)return t=this,(e.core.initial_selection===y||e.core.initial_selection===this._data.core.selected.concat([]).sort().join(","))&&(this.deselect_all(),l.each(e.core.selected,function(r,a){t.select_node(a,!1,!0)})),delete e.core.initial_selection,delete e.core.selected,this.set_state(e,s),!1;for(i in e)e.hasOwnProperty(i)&&i!=="core"&&l.inArray(i,this.settings.plugins)===-1&&delete e[i];if(l.isEmptyObject(e.core))return delete e.core,this.set_state(e,s),!1}return l.isEmptyObject(e)?(e=null,s&&s.call(this),this.trigger("set_state"),!1):!0}return!1},refresh:function(e,s){this._data.core.state=s===!0?{}:this.get_state(),s&&l.vakata.is_function(s)&&(this._data.core.state=s.call(this,this._data.core.state)),this._cnt=0,this._model.data={},this._model.data[l.jstree.root]={id:l.jstree.root,parent:null,parents:[],children:[],children_d:[],state:{loaded:!1}},this._data.core.selected=[],this._data.core.last_clicked=null,this._data.core.focused=null;var t=this.get_container_ul()[0].className;e||(this.element.html("<ul class='"+t+"' role='group'><li class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='none' id='j"+this._id+"_loading'><i class='jstree-icon jstree-ocl'></i><a class='jstree-anchor' role='treeitem' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>"+this.get_string("Loading ...")+"</a></li></ul>"),this.element.attr("aria-activedescendant","j"+this._id+"_loading")),this.load_node(l.jstree.root,function(i,r){r&&(this.get_container_ul()[0].className=t,this._firstChild(this.get_container_ul()[0])&&this.element.attr("aria-activedescendant",this._firstChild(this.get_container_ul()[0]).id),this.set_state(l.extend(!0,{},this._data.core.state),function(){this.trigger("refresh")})),this._data.core.state=null})},refresh_node:function(e){if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;var s=[],t=[],i=this._data.core.selected.concat([]);t.push(e.id),e.state.opened===!0&&s.push(e.id),this.get_node(e,!0).find(".jstree-open").each(function(){t.push(this.id),s.push(this.id)}),this._load_nodes(t,(function(r){this.open_node(s,!1,0),this.select_node(i),this.trigger("refresh_node",{node:e,nodes:r})}).bind(this),!1,!0)},set_id:function(e,s){if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;var t,i,r=this._model.data,a=e.id;for(s=s.toString(),r[e.parent].children[l.inArray(e.id,r[e.parent].children)]=s,t=0,i=e.parents.length;t<i;t++)r[e.parents[t]].children_d[l.inArray(e.id,r[e.parents[t]].children_d)]=s;for(t=0,i=e.children.length;t<i;t++)r[e.children[t]].parent=s;for(t=0,i=e.children_d.length;t<i;t++)r[e.children_d[t]].parents[l.inArray(e.id,r[e.children_d[t]].parents)]=s;return t=l.inArray(e.id,this._data.core.selected),t!==-1&&(this._data.core.selected[t]=s),t=this.get_node(e.id,!0),t&&(t.attr("id",s),this.element.attr("aria-activedescendant")===e.id&&this.element.attr("aria-activedescendant",s)),delete r[e.id],e.id=s,e.li_attr.id=s,r[s]=e,this.trigger("set_id",{node:e,new:e.id,old:a}),!0},get_text:function(e){return e=this.get_node(e),!e||e.id===l.jstree.root?!1:e.text},set_text:function(e,s){var t,i;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.set_text(e[t],s);return!0}return e=this.get_node(e),!e||e.id===l.jstree.root?!1:(e.text=s,this.get_node(e,!0).length&&this.redraw_node(e.id),this.trigger("set_text",{obj:e,text:s}),!0)},get_json:function(e,s,t){if(e=this.get_node(e||l.jstree.root),!e)return!1;s&&s.flat&&!t&&(t=[]);var i={id:e.id,text:e.text,icon:this.get_icon(e),li_attr:l.extend(!0,{},e.li_attr),a_attr:l.extend(!0,{},e.a_attr),state:{},data:s&&s.no_data?!1:l.extend(!0,l.vakata.is_array(e.data)?[]:{},e.data)},r,a;if(s&&s.flat?i.parent=e.parent:i.children=[],!s||!s.no_state)for(r in e.state)e.state.hasOwnProperty(r)&&(i.state[r]=e.state[r]);else delete i.state;if(s&&s.no_li_attr&&delete i.li_attr,s&&s.no_a_attr&&delete i.a_attr,s&&s.no_id&&(delete i.id,i.li_attr&&i.li_attr.id&&delete i.li_attr.id,i.a_attr&&i.a_attr.id&&delete i.a_attr.id),s&&s.flat&&e.id!==l.jstree.root&&t.push(i),!s||!s.no_children)for(r=0,a=e.children.length;r<a;r++)s&&s.flat?this.get_json(e.children[r],s,t):i.children.push(this.get_json(e.children[r],s));return s&&s.flat?t:e.id===l.jstree.root?i.children:i},create_node:function(e,s,t,i,r){if(e===null&&(e=l.jstree.root),e=this.get_node(e),!e)return!1;if(t=t===y?"last":t,!t.toString().match(/^(before|after)$/)&&!r&&!this.is_loaded(e))return this.load_node(e,function(){this.create_node(e,s,t,i,!0)});s||(s={text:this.get_string("New node")}),typeof s=="string"?s={text:s}:s=l.extend(!0,{},s),s.text===y&&(s.text=this.get_string("New node"));var a,n,c,d;switch(e.id===l.jstree.root&&(t==="before"&&(t="first"),t==="after"&&(t="last")),t){case"before":a=this.get_node(e.parent),t=l.inArray(e.id,a.children),e=a;break;case"after":a=this.get_node(e.parent),t=l.inArray(e.id,a.children)+1,e=a;break;case"inside":case"first":t=0;break;case"last":t=e.children.length;break;default:t||(t=0);break}if(t>e.children.length&&(t=e.children.length),s.id===y&&(s.id=!0),!this.check("create_node",s,e,t))return this.settings.core.error.call(this,this._data.core.last_error),!1;if(s.id===!0&&delete s.id,s=this._parse_model_from_json(s,e.id,e.parents.concat()),!s)return!1;for(a=this.get_node(s),n=[],n.push(s),n=n.concat(a.children_d),this.trigger("model",{nodes:n,parent:e.id}),e.children_d=e.children_d.concat(n),c=0,d=e.parents.length;c<d;c++)this._model.data[e.parents[c]].children_d=this._model.data[e.parents[c]].children_d.concat(n);for(s=a,a=[],c=0,d=e.children.length;c<d;c++)a[c>=t?c+1:c]=e.children[c];return a[t]=s.id,e.children=a,this.redraw_node(e,!0),this.trigger("create_node",{node:this.get_node(s),parent:e.id,position:t}),i&&i.call(this,this.get_node(s)),s.id},rename_node:function(e,s){var t,i,r;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.rename_node(e[t],s);return!0}return e=this.get_node(e),!e||e.id===l.jstree.root?!1:(r=e.text,this.check("rename_node",e,this.get_parent(e),s)?(this.set_text(e,s),this.trigger("rename_node",{node:e,text:s,old:r}),!0):(this.settings.core.error.call(this,this._data.core.last_error),!1))},delete_node:function(e){var s,t,i,r,a,n,c,d,h,o,f,_;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.delete_node(e[s]);return!0}if(e=this.get_node(e),!e||e.id===l.jstree.root)return!1;if(i=this.get_node(e.parent),r=l.inArray(e.id,i.children),o=!1,!this.check("delete_node",e,i,r))return this.settings.core.error.call(this,this._data.core.last_error),!1;for(r!==-1&&(i.children=l.vakata.array_remove(i.children,r)),a=e.children_d.concat([]),a.push(e.id),n=0,c=e.parents.length;n<c;n++)this._model.data[e.parents[n]].children_d=l.vakata.array_filter(this._model.data[e.parents[n]].children_d,function(u){return l.inArray(u,a)===-1});for(d=0,h=a.length;d<h;d++)if(this._model.data[a[d]].state.selected){o=!0;break}for(o&&(this._data.core.selected=l.vakata.array_filter(this._data.core.selected,function(u){return l.inArray(u,a)===-1})),this.trigger("delete_node",{node:e,parent:i.id}),o&&this.trigger("changed",{action:"delete_node",node:e,selected:this._data.core.selected,parent:i.id}),d=0,h=a.length;d<h;d++)delete this._model.data[a[d]];return l.inArray(this._data.core.focused,a)!==-1&&(this._data.core.focused=null,f=this.element[0].scrollTop,_=this.element[0].scrollLeft,i.id===l.jstree.root?this._model.data[l.jstree.root].children[0]&&this.get_node(this._model.data[l.jstree.root].children[0],!0).children(".jstree-anchor").trigger("focus"):this.get_node(i,!0).children(".jstree-anchor").trigger("focus"),this.element[0].scrollTop=f,this.element[0].scrollLeft=_),this.redraw_node(i,!0),!0},check:function(e,s,t,i,r){s=s&&s.id?s:this.get_node(s),t=t&&t.id?t:this.get_node(t);var a=e.match(/^(move_node|copy_node|create_node)$/i)?t:s,n=this.settings.core.check_callback;if(e==="move_node"||e==="copy_node"){if((!r||!r.is_multi)&&e==="move_node"&&l.inArray(s.id,t.children)===i)return this._data.core.last_error={error:"check",plugin:"core",id:"core_08",reason:"Moving node to its current position",data:JSON.stringify({chk:e,pos:i,obj:s&&s.id?s.id:!1,par:t&&t.id?t.id:!1})},!1;if((!r||!r.is_multi)&&(s.id===t.id||e==="move_node"&&l.inArray(s.id,t.children)===i||l.inArray(t.id,s.children_d)!==-1))return this._data.core.last_error={error:"check",plugin:"core",id:"core_01",reason:"Moving parent inside child",data:JSON.stringify({chk:e,pos:i,obj:s&&s.id?s.id:!1,par:t&&t.id?t.id:!1})},!1}return a&&a.data&&(a=a.data),a&&a.functions&&(a.functions[e]===!1||a.functions[e]===!0)?(a.functions[e]===!1&&(this._data.core.last_error={error:"check",plugin:"core",id:"core_02",reason:"Node data prevents function: "+e,data:JSON.stringify({chk:e,pos:i,obj:s&&s.id?s.id:!1,par:t&&t.id?t.id:!1})}),a.functions[e]):n===!1||l.vakata.is_function(n)&&n.call(this,e,s,t,i,r)===!1||n&&n[e]===!1?(this._data.core.last_error={error:"check",plugin:"core",id:"core_03",reason:"User config for core.check_callback prevents function: "+e,data:JSON.stringify({chk:e,pos:i,obj:s&&s.id?s.id:!1,par:t&&t.id?t.id:!1})},!1):!0},last_error:function(){return this._data.core.last_error},move_node:function(e,s,t,i,r,a,n){var c,d,h,o,f,_,u,g,p,m,N,O,x,A;if(s=this.get_node(s),t=t===y?0:t,!s)return!1;if(!t.toString().match(/^(before|after)$/)&&!r&&!this.is_loaded(s))return this.load_node(s,function(){this.move_node(e,s,t,i,!0,!1,n)});if(l.vakata.is_array(e))if(e.length===1)e=e[0];else{for(c=0,d=e.length;c<d;c++)(p=this.move_node(e[c],s,t,i,r,!1,n))&&(s=p,t="after");return this.redraw(),!0}if(e=e&&e.id!==y?e:this.get_node(e),!e||e.id===l.jstree.root)return!1;if(h=(e.parent||l.jstree.root).toString(),f=!t.toString().match(/^(before|after)$/)||s.id===l.jstree.root?s:this.get_node(s.parent),_=n||(this._model.data[e.id]?this:l.jstree.reference(e.id)),u=!_||!_._id||this._id!==_._id,o=_&&_._id&&h&&_._model.data[h]&&_._model.data[h].children?l.inArray(e.id,_._model.data[h].children):-1,_&&_._id&&(e=_._model.data[e.id]),u)return(p=this.copy_node(e,s,t,i,r,!1,n))?(_&&_.delete_node(e),p):!1;switch(s.id===l.jstree.root&&(t==="before"&&(t="first"),t==="after"&&(t="last")),t){case"before":t=l.inArray(s.id,f.children);break;case"after":t=l.inArray(s.id,f.children)+1;break;case"inside":case"first":t=0;break;case"last":t=f.children.length;break;default:t||(t=0);break}if(t>f.children.length&&(t=f.children.length),!this.check("move_node",e,f,t,{core:!0,origin:n,is_multi:_&&_._id&&_._id!==this._id,is_foreign:!_||!_._id}))return this.settings.core.error.call(this,this._data.core.last_error),!1;if(e.parent===f.id){for(g=f.children.concat(),p=l.inArray(e.id,g),p!==-1&&(g=l.vakata.array_remove(g,p),t>p&&t--),p=[],m=0,N=g.length;m<N;m++)p[m>=t?m+1:m]=g[m];p[t]=e.id,f.children=p,this._node_changed(f.id),this.redraw(f.id===l.jstree.root)}else{for(p=e.children_d.concat(),p.push(e.id),m=0,N=e.parents.length;m<N;m++){for(g=[],A=_._model.data[e.parents[m]].children_d,O=0,x=A.length;O<x;O++)l.inArray(A[O],p)===-1&&g.push(A[O]);_._model.data[e.parents[m]].children_d=g}for(_._model.data[h].children=l.vakata.array_remove_item(_._model.data[h].children,e.id),m=0,N=f.parents.length;m<N;m++)this._model.data[f.parents[m]].children_d=this._model.data[f.parents[m]].children_d.concat(p);for(g=[],m=0,N=f.children.length;m<N;m++)g[m>=t?m+1:m]=f.children[m];for(g[t]=e.id,f.children=g,f.children_d.push(e.id),f.children_d=f.children_d.concat(e.children_d),e.parent=f.id,p=f.parents.concat(),p.unshift(f.id),A=e.parents.length,e.parents=p,p=p.concat(),m=0,N=e.children_d.length;m<N;m++)this._model.data[e.children_d[m]].parents=this._model.data[e.children_d[m]].parents.slice(0,A*-1),Array.prototype.push.apply(this._model.data[e.children_d[m]].parents,p);(h===l.jstree.root||f.id===l.jstree.root)&&(this._model.force_full_redraw=!0),this._model.force_full_redraw||(this._node_changed(h),this._node_changed(f.id)),a||this.redraw()}return i&&i.call(this,e,f,t),this.trigger("move_node",{node:e,parent:f.id,position:t,old_parent:h,old_position:o,is_multi:_&&_._id&&_._id!==this._id,is_foreign:!_||!_._id,old_instance:_,new_instance:this}),e.id},copy_node:function(e,s,t,i,r,a,n){var c,d,h,o,f,_,u,g,p,m;if(s=this.get_node(s),t=t===y?0:t,!s)return!1;if(!t.toString().match(/^(before|after)$/)&&!r&&!this.is_loaded(s))return this.load_node(s,function(){this.copy_node(e,s,t,i,!0,!1,n)});if(l.vakata.is_array(e))if(e.length===1)e=e[0];else{for(c=0,d=e.length;c<d;c++)(o=this.copy_node(e[c],s,t,i,r,!0,n))&&(s=o,t="after");return this.redraw(),!0}if(e=e&&e.id!==y?e:this.get_node(e),!e||e.id===l.jstree.root)return!1;switch(g=(e.parent||l.jstree.root).toString(),p=!t.toString().match(/^(before|after)$/)||s.id===l.jstree.root?s:this.get_node(s.parent),m=n||(this._model.data[e.id]?this:l.jstree.reference(e.id)),!m||!m._id||(this._id,m._id),m&&m._id&&(e=m._model.data[e.id]),s.id===l.jstree.root&&(t==="before"&&(t="first"),t==="after"&&(t="last")),t){case"before":t=l.inArray(s.id,p.children);break;case"after":t=l.inArray(s.id,p.children)+1;break;case"inside":case"first":t=0;break;case"last":t=p.children.length;break;default:t||(t=0);break}if(t>p.children.length&&(t=p.children.length),!this.check("copy_node",e,p,t,{core:!0,origin:n,is_multi:m&&m._id&&m._id!==this._id,is_foreign:!m||!m._id}))return this.settings.core.error.call(this,this._data.core.last_error),!1;if(u=m?m.get_json(e,{no_id:!0,no_data:!0,no_state:!0}):e,!u||(u.id===!0&&delete u.id,u=this._parse_model_from_json(u,p.id,p.parents.concat()),!u))return!1;for(o=this.get_node(u),e&&e.state&&e.state.loaded===!1&&(o.state.loaded=!1),h=[],h.push(u),h=h.concat(o.children_d),this.trigger("model",{nodes:h,parent:p.id}),f=0,_=p.parents.length;f<_;f++)this._model.data[p.parents[f]].children_d=this._model.data[p.parents[f]].children_d.concat(h);for(h=[],f=0,_=p.children.length;f<_;f++)h[f>=t?f+1:f]=p.children[f];return h[t]=o.id,p.children=h,p.children_d.push(o.id),p.children_d=p.children_d.concat(o.children_d),p.id===l.jstree.root&&(this._model.force_full_redraw=!0),this._model.force_full_redraw||this._node_changed(p.id),a||this.redraw(p.id===l.jstree.root),i&&i.call(this,o,p,t),this.trigger("copy_node",{node:o,original:e,parent:p.id,position:t,old_parent:g,old_position:m&&m._id&&g&&m._model.data[g]&&m._model.data[g].children?l.inArray(e.id,m._model.data[g].children):-1,is_multi:m&&m._id&&m._id!==this._id,is_foreign:!m||!m._id,old_instance:m,new_instance:this}),o.id},cut:function(e){if(e||(e=this._data.core.selected.concat()),l.vakata.is_array(e)||(e=[e]),!e.length)return!1;var s=[],t,i,r;for(i=0,r=e.length;i<r;i++)t=this.get_node(e[i]),t&&(t.id||t.id===0)&&t.id!==l.jstree.root&&s.push(t);if(!s.length)return!1;D=s,J=this,q="move_node",this.trigger("cut",{node:e})},copy:function(e){if(e||(e=this._data.core.selected.concat()),l.vakata.is_array(e)||(e=[e]),!e.length)return!1;var s=[],t,i,r;for(i=0,r=e.length;i<r;i++)t=this.get_node(e[i]),t&&t.id!==y&&t.id!==l.jstree.root&&s.push(t);if(!s.length)return!1;D=s,J=this,q="copy_node",this.trigger("copy",{node:e})},get_buffer:function(){return{mode:q,node:D,inst:J}},can_paste:function(){return q!==!1&&D!==!1},paste:function(e,s){if(e=this.get_node(e),!e||!q||!q.match(/^(copy_node|move_node)$/)||!D)return!1;this[q](D,e,s,!1,!1,!1,J)&&this.trigger("paste",{parent:e.id,node:D,mode:q}),D=!1,q=!1,J=!1},clear_buffer:function(){D=!1,q=!1,J=!1,this.trigger("clear_buffer")},edit:function(e,s,t){var i,r,a,n,c,d,h,o,f,_=!1;if(e=this.get_node(e),!e)return!1;if(!this.check("edit",e,this.get_parent(e)))return this.settings.core.error.call(this,this._data.core.last_error),!1;f=e,s=typeof s=="string"?s:e.text,this.set_text(e,""),e=this._open_to(e),f.text=s,i=this._data.core.rtl,r=this.element.width(),this._data.core.focused=f.id,a=e.children(".jstree-anchor").trigger("focus"),n=l("<span></span>");/*!
oi = obj.children("i:visible"),
ai = a.children("i:visible"),
w1 = oi.width() * oi.length,
w2 = ai.width() * ai.length,
*/c=s,d=l("<div></div>",{css:{position:"absolute",top:"-200px",left:i?"0px":"-1000px",visibility:"hidden"}}).appendTo(T.body),h=l("<input />",{value:c,class:"jstree-rename-input",css:{padding:"0",border:"1px solid silver","box-sizing":"border-box",display:"inline-block",height:this._data.core.li_height+"px",lineHeight:this._data.core.li_height+"px",width:"150px"},blur:(function(u){u.stopImmediatePropagation(),u.preventDefault();var g=n.children(".jstree-rename-input"),p=g.val(),m=this.settings.core.force_text,N;p===""&&(p=c),d.remove(),n.replaceWith(a),n.remove(),c=m?c:l("<div></div>").append(l.parseHTML(c)).html(),e=this.get_node(e),this.set_text(e,c),N=!!this.rename_node(e,m?l("<div></div>").text(p).text():l("<div></div>").append(l.parseHTML(p)).html()),N||this.set_text(e,c),this._data.core.focused=f.id,setTimeout((function(){var O=this.get_node(f.id,!0);O.length&&(this._data.core.focused=f.id,O.children(".jstree-anchor").trigger("focus"))}).bind(this),0),t&&t.call(this,f,N,_,p),h=null}).bind(this),keydown:function(u){var g=u.which;g===27&&(_=!0,this.value=c),(g===27||g===13||g===37||g===38||g===39||g===40||g===32)&&u.stopImmediatePropagation(),(g===27||g===13)&&(u.preventDefault(),this.blur())},click:function(u){u.stopImmediatePropagation()},mousedown:function(u){u.stopImmediatePropagation()},keyup:function(u){h.width(Math.min(d.text("pW"+this.value).width(),r))},keypress:function(u){if(u.which===13)return!1}}),o={fontFamily:a.css("fontFamily")||"",fontSize:a.css("fontSize")||"",fontWeight:a.css("fontWeight")||"",fontStyle:a.css("fontStyle")||"",fontStretch:a.css("fontStretch")||"",fontVariant:a.css("fontVariant")||"",letterSpacing:a.css("letterSpacing")||"",wordSpacing:a.css("wordSpacing")||""},n.attr("class",a.attr("class")).append(a.contents().clone()).append(h),a.replaceWith(n),d.css(o),h.css(o).width(Math.min(d.text("pW"+h[0].value).width(),r))[0].select(),l(T).one("mousedown.jstree touchstart.jstree dnd_start.vakata",function(u){h&&u.target!==h&&l(h).trigger("blur")})},set_theme:function(e,s){if(!e)return!1;if(s===!0){var t=this.settings.core.themes.dir;t||(t=l.jstree.path+"/themes"),s=t+"/"+e+"/style.css"}s&&l.inArray(s,Z)===-1&&(l("head").append('<link rel="stylesheet" href="'+s+'" type="text/css" />'),Z.push(s)),this._data.core.themes.name&&this.element.removeClass("jstree-"+this._data.core.themes.name),this._data.core.themes.name=e,this.element.addClass("jstree-"+e),this.element[this.settings.core.themes.responsive?"addClass":"removeClass"]("jstree-"+e+"-responsive"),this.trigger("set_theme",{theme:e})},get_theme:function(){return this._data.core.themes.name},set_theme_variant:function(e){this._data.core.themes.variant&&this.element.removeClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant),this._data.core.themes.variant=e,e&&this.element.addClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant)},get_theme_variant:function(){return this._data.core.themes.variant},show_stripes:function(){this._data.core.themes.stripes=!0,this.get_container_ul().addClass("jstree-striped"),this.trigger("show_stripes")},hide_stripes:function(){this._data.core.themes.stripes=!1,this.get_container_ul().removeClass("jstree-striped"),this.trigger("hide_stripes")},toggle_stripes:function(){this._data.core.themes.stripes?this.hide_stripes():this.show_stripes()},show_dots:function(){this._data.core.themes.dots=!0,this.get_container_ul().removeClass("jstree-no-dots"),this.trigger("show_dots")},hide_dots:function(){this._data.core.themes.dots=!1,this.get_container_ul().addClass("jstree-no-dots"),this.trigger("hide_dots")},toggle_dots:function(){this._data.core.themes.dots?this.hide_dots():this.show_dots()},show_icons:function(){this._data.core.themes.icons=!0,this.get_container_ul().removeClass("jstree-no-icons"),this.trigger("show_icons")},hide_icons:function(){this._data.core.themes.icons=!1,this.get_container_ul().addClass("jstree-no-icons"),this.trigger("hide_icons")},toggle_icons:function(){this._data.core.themes.icons?this.hide_icons():this.show_icons()},show_ellipsis:function(){this._data.core.themes.ellipsis=!0,this.get_container_ul().addClass("jstree-ellipsis"),this.trigger("show_ellipsis")},hide_ellipsis:function(){this._data.core.themes.ellipsis=!1,this.get_container_ul().removeClass("jstree-ellipsis"),this.trigger("hide_ellipsis")},toggle_ellipsis:function(){this._data.core.themes.ellipsis?this.hide_ellipsis():this.show_ellipsis()},set_icon:function(e,s){var t,i,r,a;if(l.vakata.is_array(e)){for(e=e.slice(),t=0,i=e.length;t<i;t++)this.set_icon(e[t],s);return!0}return e=this.get_node(e),!e||e.id===l.jstree.root?!1:(a=e.icon,e.icon=s===!0||s===null||s===y||s===""?!0:s,r=this.get_node(e,!0).children(".jstree-anchor").children(".jstree-themeicon"),s===!1?(r.removeClass("jstree-themeicon-custom "+a).css("background","").removeAttr("rel"),this.hide_icon(e)):s===!0||s===null||s===y||s===""?(r.removeClass("jstree-themeicon-custom "+a).css("background","").removeAttr("rel"),a===!1&&this.show_icon(e)):s.indexOf("/")===-1&&s.indexOf(".")===-1?(r.removeClass(a).css("background",""),r.addClass(s+" jstree-themeicon-custom").attr("rel",s),a===!1&&this.show_icon(e)):(r.removeClass(a).css("background",""),r.addClass("jstree-themeicon-custom").css("background","url('"+s+"') center center no-repeat").attr("rel",s),a===!1&&this.show_icon(e)),!0)},get_icon:function(e){return e=this.get_node(e),!e||e.id===l.jstree.root?!1:e.icon},hide_icon:function(e){var s,t;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.hide_icon(e[s]);return!0}return e=this.get_node(e),!e||e===l.jstree.root?!1:(e.icon=!1,this.get_node(e,!0).children(".jstree-anchor").children(".jstree-themeicon").addClass("jstree-themeicon-hidden"),!0)},show_icon:function(e){var s,t,i;if(l.vakata.is_array(e)){for(e=e.slice(),s=0,t=e.length;s<t;s++)this.show_icon(e[s]);return!0}return e=this.get_node(e),!e||e===l.jstree.root?!1:(i=this.get_node(e,!0),e.icon=i.length?i.children(".jstree-anchor").children(".jstree-themeicon").attr("rel"):!0,e.icon||(e.icon=!0),i.children(".jstree-anchor").children(".jstree-themeicon").removeClass("jstree-themeicon-hidden"),!0)}},l.vakata={},l.vakata.attributes=function(e,s){e=l(e)[0];var t=s?{}:[];return e&&e.attributes&&l.each(e.attributes,function(i,r){l.inArray(r.name.toLowerCase(),["style","contenteditable","hasfocus","tabindex"])===-1&&r.value!==null&&l.vakata.trim(r.value)!==""&&(s?t[r.name]=r.value:t.push(r.name))}),t},l.vakata.array_unique=function(e){var s=[],t,i,r={};for(t=0,i=e.length;t<i;t++)r[e[t]]===y&&(s.push(e[t]),r[e[t]]=!0);return s},l.vakata.array_remove=function(e,s){return e.splice(s,1),e},l.vakata.array_remove_item=function(e,s){var t=l.inArray(s,e);return t!==-1?l.vakata.array_remove(e,t):e},l.vakata.array_filter=function(e,s,t,i,r){if(e.filter)return e.filter(s,t);i=[];for(r in e)~~r+""==r+""&&r>=0&&s.call(t,e[r],+r,e)&&i.push(e[r]);return i},l.vakata.trim=function(e){return String.prototype.trim?String.prototype.trim.call(e.toString()):e.toString().replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},l.vakata.is_function=function(e){return typeof e=="function"&&typeof e.nodeType!="number"},l.vakata.is_array=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"},Function.prototype.bind||(Function.prototype.bind=function(){var e=this,s=arguments[0],t=Array.prototype.slice.call(arguments,1);if(typeof e!="function")throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");return function(){var i=t.concat(Array.prototype.slice.call(arguments));return e.apply(s,i)}}),l.jstree.plugins.changed=function(e,s){var t=[];this.trigger=function(i,r){var a,n;if(r||(r={}),i.replace(".jstree","")==="changed"){r.changed={selected:[],deselected:[]};var c={};for(a=0,n=t.length;a<n;a++)c[t[a]]=1;for(a=0,n=r.selected.length;a<n;a++)c[r.selected[a]]?c[r.selected[a]]=2:r.changed.selected.push(r.selected[a]);for(a=0,n=t.length;a<n;a++)c[t[a]]===1&&r.changed.deselected.push(t[a]);t=r.selected.slice()}s.trigger.call(this,i,r)},this.refresh=function(i,r){return t=[],s.refresh.apply(this,arguments)}};var V=T.createElement("I");V.className="jstree-icon jstree-checkbox",V.setAttribute("role","presentation"),l.jstree.defaults.checkbox={visible:!0,three_state:!0,whole_node:!0,keep_selected_style:!0,cascade:"",tie_selection:!0,cascade_to_disabled:!0,cascade_to_hidden:!0},l.jstree.plugins.checkbox=function(e,s){this.bind=function(){s.bind.call(this),this._data.checkbox.uto=!1,this._data.checkbox.selected=[],this.settings.checkbox.three_state&&(this.settings.checkbox.cascade="up+down+undetermined"),this.element.on("init.jstree",(function(){this._data.checkbox.visible=this.settings.checkbox.visible,this.settings.checkbox.keep_selected_style||this.element.addClass("jstree-checkbox-no-clicked"),this.settings.checkbox.tie_selection&&this.element.addClass("jstree-checkbox-selection")}).bind(this)).on("loading.jstree",(function(){this[this._data.checkbox.visible?"show_checkboxes":"hide_checkboxes"]()}).bind(this)),this.settings.checkbox.cascade.indexOf("undetermined")!==-1&&this.element.on("changed.jstree uncheck_node.jstree check_node.jstree uncheck_all.jstree check_all.jstree move_node.jstree copy_node.jstree redraw.jstree open_node.jstree",(function(){this._data.checkbox.uto&&clearTimeout(this._data.checkbox.uto),this._data.checkbox.uto=setTimeout(this._undetermined.bind(this),50)}).bind(this)),this.settings.checkbox.tie_selection||this.element.on("model.jstree",(function(t,i){var r=this._model.data;r[i.parent];var a=i.nodes,n,c;for(n=0,c=a.length;n<c;n++)r[a[n]].state.checked=r[a[n]].state.checked||r[a[n]].original&&r[a[n]].original.state&&r[a[n]].original.state.checked,r[a[n]].state.checked&&this._data.checkbox.selected.push(a[n])}).bind(this)),(this.settings.checkbox.cascade.indexOf("up")!==-1||this.settings.checkbox.cascade.indexOf("down")!==-1)&&this.element.on("model.jstree",(function(t,i){var r=this._model.data,a=r[i.parent],n=i.nodes,c=[],d,h,o,f,_,u,g=this.settings.checkbox.cascade,p=this.settings.checkbox.tie_selection;if(g.indexOf("down")!==-1){if(a.state[p?"selected":"checked"]){for(h=0,o=n.length;h<o;h++)r[n[h]].state[p?"selected":"checked"]=!0;this._data[p?"core":"checkbox"].selected=this._data[p?"core":"checkbox"].selected.concat(n)}else for(h=0,o=n.length;h<o;h++)if(r[n[h]].state[p?"selected":"checked"]){for(f=0,_=r[n[h]].children_d.length;f<_;f++)r[r[n[h]].children_d[f]].state[p?"selected":"checked"]=!0;this._data[p?"core":"checkbox"].selected=this._data[p?"core":"checkbox"].selected.concat(r[n[h]].children_d)}}if(g.indexOf("up")!==-1){for(h=0,o=a.children_d.length;h<o;h++)r[a.children_d[h]].children.length||c.push(r[a.children_d[h]].parent);for(c=l.vakata.array_unique(c),f=0,_=c.length;f<_;f++)for(a=r[c[f]];a&&a.id!==l.jstree.root;){for(d=0,h=0,o=a.children.length;h<o;h++)d+=r[a.children[h]].state[p?"selected":"checked"];if(d===o)a.state[p?"selected":"checked"]=!0,this._data[p?"core":"checkbox"].selected.push(a.id),u=this.get_node(a,!0),u&&u.length&&u.children(".jstree-anchor").attr("aria-selected",!0).addClass(p?"jstree-clicked":"jstree-checked");else break;a=this.get_node(a.parent)}}this._data[p?"core":"checkbox"].selected=l.vakata.array_unique(this._data[p?"core":"checkbox"].selected)}).bind(this)).on(this.settings.checkbox.tie_selection?"select_node.jstree":"check_node.jstree",(function(t,i){var r=i.node,a=this._model.data,n=this.get_node(r.parent),c,d,h,o,f=this.settings.checkbox.cascade,_=this.settings.checkbox.tie_selection,u={},g=this._data[_?"core":"checkbox"].selected;for(c=0,d=g.length;c<d;c++)u[g[c]]=!0;if(f.indexOf("down")!==-1){var p=this._cascade_new_checked_state(r.id,!0),m=r.children_d.concat(r.id);for(c=0,d=m.length;c<d;c++)p.indexOf(m[c])>-1?u[m[c]]=!0:delete u[m[c]]}if(f.indexOf("up")!==-1)for(;n&&n.id!==l.jstree.root;){for(h=0,c=0,d=n.children.length;c<d;c++)h+=a[n.children[c]].state[_?"selected":"checked"];if(h===d)n.state[_?"selected":"checked"]=!0,u[n.id]=!0,o=this.get_node(n,!0),o&&o.length&&o.children(".jstree-anchor").attr("aria-selected",!0).addClass(_?"jstree-clicked":"jstree-checked");else break;n=this.get_node(n.parent)}g=[];for(c in u)u.hasOwnProperty(c)&&g.push(c);this._data[_?"core":"checkbox"].selected=g}).bind(this)).on(this.settings.checkbox.tie_selection?"deselect_all.jstree":"uncheck_all.jstree",(function(t,i){var r=this.get_node(l.jstree.root),a=this._model.data,n,c,d;for(n=0,c=r.children_d.length;n<c;n++)d=a[r.children_d[n]],d&&d.original&&d.original.state&&d.original.state.undetermined&&(d.original.state.undetermined=!1)}).bind(this)).on(this.settings.checkbox.tie_selection?"deselect_node.jstree":"uncheck_node.jstree",(function(t,i){var r=i.node;this.get_node(r,!0);var a,n,c,d=this.settings.checkbox.cascade,h=this.settings.checkbox.tie_selection,o=this._data[h?"core":"checkbox"].selected,f=r.children_d.concat(r.id);if(d.indexOf("down")!==-1){var _=this._cascade_new_checked_state(r.id,!1);o=l.vakata.array_filter(o,function(u){return f.indexOf(u)===-1||_.indexOf(u)>-1})}if(d.indexOf("up")!==-1&&o.indexOf(r.id)===-1){for(a=0,n=r.parents.length;a<n;a++)c=this._model.data[r.parents[a]],c.state[h?"selected":"checked"]=!1,c&&c.original&&c.original.state&&c.original.state.undetermined&&(c.original.state.undetermined=!1),c=this.get_node(r.parents[a],!0),c&&c.length&&c.children(".jstree-anchor").attr("aria-selected",!1).removeClass(h?"jstree-clicked":"jstree-checked");o=l.vakata.array_filter(o,function(u){return r.parents.indexOf(u)===-1})}this._data[h?"core":"checkbox"].selected=o}).bind(this)),this.settings.checkbox.cascade.indexOf("up")!==-1&&this.element.on("delete_node.jstree",(function(t,i){for(var r=this.get_node(i.parent),a=this._model.data,n,c,d,h,o=this.settings.checkbox.tie_selection;r&&r.id!==l.jstree.root&&!r.state[o?"selected":"checked"];){for(d=0,n=0,c=r.children.length;n<c;n++)d+=a[r.children[n]].state[o?"selected":"checked"];if(c>0&&d===c)r.state[o?"selected":"checked"]=!0,this._data[o?"core":"checkbox"].selected.push(r.id),h=this.get_node(r,!0),h&&h.length&&h.children(".jstree-anchor").attr("aria-selected",!0).addClass(o?"jstree-clicked":"jstree-checked");else break;r=this.get_node(r.parent)}}).bind(this)).on("move_node.jstree",(function(t,i){var r=i.is_multi,a=i.old_parent,n=this.get_node(i.parent),c=this._model.data,d,h,o,f,_,u=this.settings.checkbox.tie_selection;if(!r)for(d=this.get_node(a);d&&d.id!==l.jstree.root&&!d.state[u?"selected":"checked"];){for(h=0,o=0,f=d.children.length;o<f;o++)h+=c[d.children[o]].state[u?"selected":"checked"];if(f>0&&h===f)d.state[u?"selected":"checked"]=!0,this._data[u?"core":"checkbox"].selected.push(d.id),_=this.get_node(d,!0),_&&_.length&&_.children(".jstree-anchor").attr("aria-selected",!0).addClass(u?"jstree-clicked":"jstree-checked");else break;d=this.get_node(d.parent)}for(d=n;d&&d.id!==l.jstree.root;){for(h=0,o=0,f=d.children.length;o<f;o++)h+=c[d.children[o]].state[u?"selected":"checked"];if(h===f)d.state[u?"selected":"checked"]||(d.state[u?"selected":"checked"]=!0,this._data[u?"core":"checkbox"].selected.push(d.id),_=this.get_node(d,!0),_&&_.length&&_.children(".jstree-anchor").attr("aria-selected",!0).addClass(u?"jstree-clicked":"jstree-checked"));else if(d.state[u?"selected":"checked"])d.state[u?"selected":"checked"]=!1,this._data[u?"core":"checkbox"].selected=l.vakata.array_remove_item(this._data[u?"core":"checkbox"].selected,d.id),_=this.get_node(d,!0),_&&_.length&&_.children(".jstree-anchor").attr("aria-selected",!1).removeClass(u?"jstree-clicked":"jstree-checked");else break;d=this.get_node(d.parent)}}).bind(this))},this.get_undetermined=function(t){if(this.settings.checkbox.cascade.indexOf("undetermined")===-1)return[];var i,r,a,n,c={},d=this._model.data,h=this.settings.checkbox.tie_selection,o=this._data[h?"core":"checkbox"].selected,f=[],_=this,u=[];for(i=0,r=o.length;i<r;i++)if(d[o[i]]&&d[o[i]].parents)for(a=0,n=d[o[i]].parents.length;a<n&&c[d[o[i]].parents[a]]===y;a++)d[o[i]].parents[a]!==l.jstree.root&&(c[d[o[i]].parents[a]]=!0,f.push(d[o[i]].parents[a]));for(this.element.find(".jstree-closed").not(":has(.jstree-children)").each(function(){var g=_.get_node(this),p;if(g){if(g.state.loaded){for(i=0,r=g.children_d.length;i<r;i++)if(p=d[g.children_d[i]],!p.state.loaded&&p.original&&p.original.state&&p.original.state.undetermined&&p.original.state.undetermined===!0)for(c[p.id]===y&&p.id!==l.jstree.root&&(c[p.id]=!0,f.push(p.id)),a=0,n=p.parents.length;a<n;a++)c[p.parents[a]]===y&&p.parents[a]!==l.jstree.root&&(c[p.parents[a]]=!0,f.push(p.parents[a]))}else if(g.original&&g.original.state&&g.original.state.undetermined&&g.original.state.undetermined===!0)for(c[g.id]===y&&g.id!==l.jstree.root&&(c[g.id]=!0,f.push(g.id)),a=0,n=g.parents.length;a<n;a++)c[g.parents[a]]===y&&g.parents[a]!==l.jstree.root&&(c[g.parents[a]]=!0,f.push(g.parents[a]))}}),i=0,r=f.length;i<r;i++)d[f[i]].state[h?"selected":"checked"]||u.push(t?d[f[i]]:f[i]);return u},this._undetermined=function(){if(this.element!==null){var t=this.get_undetermined(!1),i,r,a;for(this.element.find(".jstree-undetermined").removeClass("jstree-undetermined"),i=0,r=t.length;i<r;i++)a=this.get_node(t[i],!0),a&&a.length&&a.children(".jstree-anchor").children(".jstree-checkbox").addClass("jstree-undetermined")}},this.redraw_node=function(t,i,r,a){if(t=s.redraw_node.apply(this,arguments),t){var n,c,d=null,h=null;for(n=0,c=t.childNodes.length;n<c;n++)if(t.childNodes[n]&&t.childNodes[n].className&&t.childNodes[n].className.indexOf("jstree-anchor")!==-1){d=t.childNodes[n];break}d&&(!this.settings.checkbox.tie_selection&&this._model.data[t.id].state.checked&&(d.className+=" jstree-checked"),h=V.cloneNode(!1),this._model.data[t.id].state.checkbox_disabled&&(h.className+=" jstree-checkbox-disabled"),d.insertBefore(h,d.childNodes[0]))}return!r&&this.settings.checkbox.cascade.indexOf("undetermined")!==-1&&(this._data.checkbox.uto&&clearTimeout(this._data.checkbox.uto),this._data.checkbox.uto=setTimeout(this._undetermined.bind(this),50)),t},this.show_checkboxes=function(){this._data.core.themes.checkboxes=!0,this.get_container_ul().removeClass("jstree-no-checkboxes")},this.hide_checkboxes=function(){this._data.core.themes.checkboxes=!1,this.get_container_ul().addClass("jstree-no-checkboxes")},this.toggle_checkboxes=function(){this._data.core.themes.checkboxes?this.hide_checkboxes():this.show_checkboxes()},this.is_undetermined=function(t){t=this.get_node(t);var i=this.settings.checkbox.cascade,r,a,n=this.settings.checkbox.tie_selection,c=this._data[n?"core":"checkbox"].selected,d=this._model.data;if(!t||t.state[n?"selected":"checked"]===!0||i.indexOf("undetermined")===-1||i.indexOf("down")===-1&&i.indexOf("up")===-1)return!1;if(!t.state.loaded&&t.original.state.undetermined===!0)return!0;for(r=0,a=t.children_d.length;r<a;r++)if(l.inArray(t.children_d[r],c)!==-1||!d[t.children_d[r]].state.loaded&&d[t.children_d[r]].original.state.undetermined)return!0;return!1},this.disable_checkbox=function(t){var i,r,a;if(l.vakata.is_array(t)){for(t=t.slice(),i=0,r=t.length;i<r;i++)this.disable_checkbox(t[i]);return!0}if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;a=this.get_node(t,!0),t.state.checkbox_disabled||(t.state.checkbox_disabled=!0,a&&a.length&&a.children(".jstree-anchor").children(".jstree-checkbox").addClass("jstree-checkbox-disabled"),this.trigger("disable_checkbox",{node:t}))},this.enable_checkbox=function(t){var i,r,a;if(l.vakata.is_array(t)){for(t=t.slice(),i=0,r=t.length;i<r;i++)this.enable_checkbox(t[i]);return!0}if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;a=this.get_node(t,!0),t.state.checkbox_disabled&&(t.state.checkbox_disabled=!1,a&&a.length&&a.children(".jstree-anchor").children(".jstree-checkbox").removeClass("jstree-checkbox-disabled"),this.trigger("enable_checkbox",{node:t}))},this.activate_node=function(t,i){if(l(i.target).hasClass("jstree-checkbox-disabled"))return!1;if(this.settings.checkbox.tie_selection&&(this.settings.checkbox.whole_node||l(i.target).hasClass("jstree-checkbox"))&&(i.ctrlKey=!0),this.settings.checkbox.tie_selection||!this.settings.checkbox.whole_node&&!l(i.target).hasClass("jstree-checkbox"))return s.activate_node.call(this,t,i);if(this.is_disabled(t))return!1;this.is_checked(t)?this.uncheck_node(t,i):this.check_node(t,i),this.trigger("activate_node",{node:this.get_node(t)})},this.delete_node=function(t){if(this.settings.checkbox.tie_selection||l.vakata.is_array(t))return s.delete_node.call(this,t);var i,r,a,n=!1;if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;for(i=t.children_d.concat([]),i.push(t.id),r=0,a=i.length;r<a;r++)if(this._model.data[i[r]].state.checked){n=!0;break}return n&&(this._data.checkbox.selected=l.vakata.array_filter(this._data.checkbox.selected,function(c){return l.inArray(c,i)===-1})),s.delete_node.call(this,t)},this._cascade_new_checked_state=function(t,i){var r=this,a=this.settings.checkbox.tie_selection,n=this._model.data[t],c=[],d=[],h,o,f;if((this.settings.checkbox.cascade_to_disabled||!n.state.disabled)&&(this.settings.checkbox.cascade_to_hidden||!n.state.hidden)){if(n.children)for(h=0,o=n.children.length;h<o;h++){var _=n.children[h];f=r._cascade_new_checked_state(_,i),c=c.concat(f),f.indexOf(_)>-1&&d.push(_)}var u=r.get_node(n,!0),g=d.length>0&&d.length<n.children.length;n.original&&n.original.state&&n.original.state.undetermined&&(n.original.state.undetermined=g),g?(n.state[a?"selected":"checked"]=!1,u.children(".jstree-anchor").attr("aria-selected",!1).removeClass(a?"jstree-clicked":"jstree-checked")):i&&d.length===n.children.length?(n.state[a?"selected":"checked"]=i,c.push(n.id),u.children(".jstree-anchor").attr("aria-selected",!0).addClass(a?"jstree-clicked":"jstree-checked")):(n.state[a?"selected":"checked"]=!1,u.children(".jstree-anchor").attr("aria-selected",!1).removeClass(a?"jstree-clicked":"jstree-checked"))}else f=this.get_checked_descendants(t),n.state[a?"selected":"checked"]&&f.push(n.id),c=c.concat(f);return c},this.get_checked_descendants=function(t){var i=this,r=i.settings.checkbox.tie_selection,a=i._model.data[t];return l.vakata.array_filter(a.children_d,function(n){return i._model.data[n].state[r?"selected":"checked"]})},this.check_node=function(t,i){if(this.settings.checkbox.tie_selection)return this.select_node(t,!1,!0,i);var r,a,n;if(l.vakata.is_array(t)){for(t=t.slice(),a=0,n=t.length;a<n;a++)this.check_node(t[a],i);return!0}if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;r=this.get_node(t,!0),t.state.checked||(t.state.checked=!0,this._data.checkbox.selected.push(t.id),r&&r.length&&r.children(".jstree-anchor").addClass("jstree-checked"),this.trigger("check_node",{node:t,selected:this._data.checkbox.selected,event:i}))},this.uncheck_node=function(t,i){if(this.settings.checkbox.tie_selection)return this.deselect_node(t,!1,i);var r,a,n;if(l.vakata.is_array(t)){for(t=t.slice(),r=0,a=t.length;r<a;r++)this.uncheck_node(t[r],i);return!0}if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;n=this.get_node(t,!0),t.state.checked&&(t.state.checked=!1,this._data.checkbox.selected=l.vakata.array_remove_item(this._data.checkbox.selected,t.id),n.length&&n.children(".jstree-anchor").removeClass("jstree-checked"),this.trigger("uncheck_node",{node:t,selected:this._data.checkbox.selected,event:i}))},this.check_all=function(){if(this.settings.checkbox.tie_selection)return this.select_all();this._data.checkbox.selected.concat([]);var t,i;for(this._data.checkbox.selected=this._model.data[l.jstree.root].children_d.concat(),t=0,i=this._data.checkbox.selected.length;t<i;t++)this._model.data[this._data.checkbox.selected[t]]&&(this._model.data[this._data.checkbox.selected[t]].state.checked=!0);this.redraw(!0),this.trigger("check_all",{selected:this._data.checkbox.selected})},this.uncheck_all=function(){if(this.settings.checkbox.tie_selection)return this.deselect_all();var t=this._data.checkbox.selected.concat([]),i,r;for(i=0,r=this._data.checkbox.selected.length;i<r;i++)this._model.data[this._data.checkbox.selected[i]]&&(this._model.data[this._data.checkbox.selected[i]].state.checked=!1);this._data.checkbox.selected=[],this.element.find(".jstree-checked").removeClass("jstree-checked"),this.trigger("uncheck_all",{selected:this._data.checkbox.selected,node:t})},this.is_checked=function(t){return this.settings.checkbox.tie_selection?this.is_selected(t):(t=this.get_node(t),!t||t.id===l.jstree.root?!1:t.state.checked)},this.get_checked=function(t){return this.settings.checkbox.tie_selection?this.get_selected(t):t?l.map(this._data.checkbox.selected,(function(i){return this.get_node(i)}).bind(this)):this._data.checkbox.selected.slice()},this.get_top_checked=function(t){if(this.settings.checkbox.tie_selection)return this.get_top_selected(t);var i=this.get_checked(!0),r={},a,n,c,d;for(a=0,n=i.length;a<n;a++)r[i[a].id]=i[a];for(a=0,n=i.length;a<n;a++)for(c=0,d=i[a].children_d.length;c<d;c++)r[i[a].children_d[c]]&&delete r[i[a].children_d[c]];i=[];for(a in r)r.hasOwnProperty(a)&&i.push(a);return t?l.map(i,(function(h){return this.get_node(h)}).bind(this)):i},this.get_bottom_checked=function(t){if(this.settings.checkbox.tie_selection)return this.get_bottom_selected(t);var i=this.get_checked(!0),r=[],a,n;for(a=0,n=i.length;a<n;a++)i[a].children.length||r.push(i[a].id);return t?l.map(r,(function(c){return this.get_node(c)}).bind(this)):r},this.load_node=function(t,i){var r,a,n;if(!l.vakata.is_array(t)&&!this.settings.checkbox.tie_selection&&(n=this.get_node(t),n&&n.state.loaded))for(r=0,a=n.children_d.length;r<a;r++)this._model.data[n.children_d[r]].state.checked&&(this._data.checkbox.selected=l.vakata.array_remove_item(this._data.checkbox.selected,n.children_d[r]));return s.load_node.apply(this,arguments)},this.get_state=function(){var t=s.get_state.apply(this,arguments);return this.settings.checkbox.tie_selection||(t.checkbox=this._data.checkbox.selected.slice()),t},this.set_state=function(t,i){var r=s.set_state.apply(this,arguments);if(r&&t.checkbox){if(!this.settings.checkbox.tie_selection){this.uncheck_all();var a=this;l.each(t.checkbox,function(n,c){a.check_node(c)})}return delete t.checkbox,this.set_state(t,i),!1}return r},this.refresh=function(t,i){return this.settings.checkbox.tie_selection&&(this._data.checkbox.selected=[]),s.refresh.apply(this,arguments)}},l.jstree.defaults.conditionalselect=function(){return!0},l.jstree.plugins.conditionalselect=function(e,s){this.activate_node=function(t,i){if(this.settings.conditionalselect.call(this,this.get_node(t),i))return s.activate_node.call(this,t,i)}},l.jstree.defaults.contextmenu={select_node:!0,show_at_node:!0,items:function(e,s){return{create:{separator_before:!1,separator_after:!0,_disabled:!1,label:"Create",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.create_node(r,{},"last",function(a){try{i.edit(a)}catch{setTimeout(function(){i.edit(a)},0)}})}},rename:{separator_before:!1,separator_after:!1,_disabled:!1,label:"Rename",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.edit(r)}},remove:{separator_before:!1,icon:!1,separator_after:!1,_disabled:!1,label:"Delete",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.is_selected(r)?i.delete_node(i.get_selected()):i.delete_node(r)}},ccp:{separator_before:!0,icon:!1,separator_after:!1,label:"Edit",action:!1,submenu:{cut:{separator_before:!1,separator_after:!1,label:"Cut",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.is_selected(r)?i.cut(i.get_top_selected()):i.cut(r)}},copy:{separator_before:!1,icon:!1,separator_after:!1,label:"Copy",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.is_selected(r)?i.copy(i.get_top_selected()):i.copy(r)}},paste:{separator_before:!1,icon:!1,_disabled:function(t){return!l.jstree.reference(t.reference).can_paste()},separator_after:!1,label:"Paste",action:function(t){var i=l.jstree.reference(t.reference),r=i.get_node(t.reference);i.paste(r)}}}}}}},l.jstree.plugins.contextmenu=function(e,s){this.bind=function(){s.bind.call(this);var t=0,i=null,r,a;this.element.on("init.jstree loading.jstree ready.jstree",(function(){this.get_container_ul().addClass("jstree-contextmenu")}).bind(this)).on("contextmenu.jstree",".jstree-anchor",(function(n,c){n.target.tagName.toLowerCase()!=="input"&&(n.preventDefault(),t=n.ctrlKey?+new Date:0,(c||i)&&(t=+new Date+1e4),i&&clearTimeout(i),this.is_loading(n.currentTarget)||this.show_contextmenu(n.currentTarget,n.pageX,n.pageY,n))}).bind(this)).on("click.jstree",".jstree-anchor",(function(n){this._data.contextmenu.visible&&(!t||+new Date-t>250)&&l.vakata.context.hide(),t=0}).bind(this)).on("touchstart.jstree",".jstree-anchor",function(n){!n.originalEvent||!n.originalEvent.changedTouches||!n.originalEvent.changedTouches[0]||(r=n.originalEvent.changedTouches[0].clientX,a=n.originalEvent.changedTouches[0].clientY,i=setTimeout(function(){l(n.currentTarget).trigger("contextmenu",!0)},750))}).on("touchmove.vakata.jstree",function(n){i&&n.originalEvent&&n.originalEvent.changedTouches&&n.originalEvent.changedTouches[0]&&(Math.abs(r-n.originalEvent.changedTouches[0].clientX)>10||Math.abs(a-n.originalEvent.changedTouches[0].clientY)>10)&&(clearTimeout(i),l.vakata.context.hide())}).on("touchend.vakata.jstree",function(n){i&&clearTimeout(i)});/*!
if(!('oncontextmenu' in document.body) && ('ontouchstart' in document.body)) {
	var el = null, tm = null;
	this.element
		.on("touchstart", ".jstree-anchor", function (e) {
			el = e.currentTarget;
			tm = +new Date();
			$(document).one("touchend", function (e) {
				e.target = document.elementFromPoint(e.originalEvent.targetTouches[0].pageX - window.pageXOffset, e.originalEvent.targetTouches[0].pageY - window.pageYOffset);
				e.currentTarget = e.target;
				tm = ((+(new Date())) - tm);
				if(e.target === el && tm > 600 && tm < 1000) {
					e.preventDefault();
					$(el).trigger('contextmenu', e);
				}
				el = null;
				tm = null;
			});
		});
}
*/l(T).on("context_hide.vakata.jstree",(function(n,c){this._data.contextmenu.visible=!1,l(c.reference).removeClass("jstree-context")}).bind(this))},this.teardown=function(){this._data.contextmenu.visible&&l.vakata.context.hide(),l(T).off("context_hide.vakata.jstree"),s.teardown.call(this)},this.show_contextmenu=function(t,i,r,a){if(t=this.get_node(t),!t||t.id===l.jstree.root)return!1;var n=this.settings.contextmenu,c=this.get_node(t,!0),d=c.children(".jstree-anchor"),h=!1,o=!1;(n.show_at_node||i===y||r===y)&&(h=d.offset(),i=h.left,r=h.top+this._data.core.li_height),this.settings.contextmenu.select_node&&!this.is_selected(t)&&this.activate_node(t,a),o=n.items,l.vakata.is_function(o)&&(o=o.call(this,t,(function(f){this._show_contextmenu(t,i,r,f)}).bind(this))),l.isPlainObject(o)&&this._show_contextmenu(t,i,r,o)},this._show_contextmenu=function(t,i,r,a){var n=this.get_node(t,!0),c=n.children(".jstree-anchor");l(T).one("context_show.vakata.jstree",(function(d,h){var o="jstree-contextmenu jstree-"+this.get_theme()+"-contextmenu";l(h.element).addClass(o),c.addClass("jstree-context")}).bind(this)),this._data.contextmenu.visible=!0,l.vakata.context.show(c,{x:i,y:r},a),this.trigger("show_contextmenu",{node:t,x:i,y:r})}},function(e){var s=!1,t={element:!1,reference:!1,position_x:0,position_y:0,items:[],html:"",is_visible:!1};e.vakata.context={settings:{hide_onmouseleave:0,icons:!0},_trigger:function(i){e(T).triggerHandler("context_"+i+".vakata",{reference:t.reference,element:t.element,position:{x:t.position_x,y:t.position_y}})},_execute:function(i){return i=t.items[i],i&&(!i._disabled||e.vakata.is_function(i._disabled)&&!i._disabled({item:i,reference:t.reference,element:t.element}))&&i.action?i.action.call(null,{item:i,reference:t.reference,element:t.element,position:{x:t.position_x,y:t.position_y}}):!1},_parse:function(i,r){if(!i)return!1;r||(t.html="",t.items=[]);var a="",n=!1,c;return r&&(a+="<ul>"),e.each(i,function(d,h){if(!h)return!0;t.items.push(h),!n&&h.separator_before&&(a+="<li class='vakata-context-separator'><a href='#' "+(e.vakata.context.settings.icons?"":'class="vakata-context-no-icons"')+">&#160;</a></li>"),n=!1,a+="<li class='"+(h._class||"")+(h._disabled===!0||e.vakata.is_function(h._disabled)&&h._disabled({item:h,reference:t.reference,element:t.element})?" vakata-contextmenu-disabled ":"")+"' "+(h.shortcut?" data-shortcut='"+h.shortcut+"' ":"")+">",a+="<a href='#' rel='"+(t.items.length-1)+"' "+(h.title?"title='"+h.title+"'":"")+">",e.vakata.context.settings.icons&&(a+="<i ",h.icon&&(h.icon.indexOf("/")!==-1||h.icon.indexOf(".")!==-1?a+=` style='background:url("`+h.icon+`") center center no-repeat' `:a+=" class='"+h.icon+"' "),a+="></i><span class='vakata-contextmenu-sep'>&#160;</span>"),a+=(e.vakata.is_function(h.label)?h.label({item:d,reference:t.reference,element:t.element}):h.label)+(h.shortcut?' <span class="vakata-contextmenu-shortcut vakata-contextmenu-shortcut-'+h.shortcut+'">'+(h.shortcut_label||"")+"</span>":"")+"</a>",h.submenu&&(c=e.vakata.context._parse(h.submenu,!0),c&&(a+=c)),a+="</li>",h.separator_after&&(a+="<li class='vakata-context-separator'><a href='#' "+(e.vakata.context.settings.icons?"":'class="vakata-context-no-icons"')+">&#160;</a></li>",n=!0)}),a=a.replace(/<li class\='vakata-context-separator'\><\/li\>$/,""),r&&(a+="</ul>"),r||(t.html=a,e.vakata.context._trigger("parse")),a.length>10?a:!1},_show_submenu:function(i){if(i=e(i),!(!i.length||!i.children("ul").length)){var r=i.children("ul"),a=i.offset().left,n=a+i.outerWidth(),c=i.offset().top,d=r.width(),h=r.height(),o=e(window).width()+e(window).scrollLeft(),f=e(window).height()+e(window).scrollTop();s?i[n-(d+10+i.outerWidth())<0?"addClass":"removeClass"]("vakata-context-left"):i[n+d>o&&a>o-n?"addClass":"removeClass"]("vakata-context-right"),c+h+10>f&&r.css("bottom","-1px"),i.hasClass("vakata-context-right")?a<d&&r.css("margin-right",a-d):o-n<d&&r.css("margin-left",o-n-d),r.show()}},show:function(i,r,a){var n,c,d,h,o,f,_,u,g=!0;switch(t.element&&t.element.length&&t.element.width(""),g){case(!r&&!i):return!1;case(!!r&&!!i):t.reference=i,t.position_x=r.x,t.position_y=r.y;break;case(!r&&!!i):t.reference=i,n=i.offset(),t.position_x=n.left+i.outerHeight(),t.position_y=n.top;break;case(!!r&&!i):t.position_x=r.x,t.position_y=r.y;break}i&&!a&&e(i).data("vakata_contextmenu")&&(a=e(i).data("vakata_contextmenu")),e.vakata.context._parse(a)&&t.element.html(t.html),t.items.length&&(t.element.appendTo(T.body),c=t.element,d=t.position_x,h=t.position_y,o=c.width(),f=c.height(),_=e(window).width()+e(window).scrollLeft(),u=e(window).height()+e(window).scrollTop(),s&&(d-=c.outerWidth()-e(i).outerWidth(),d<e(window).scrollLeft()+20&&(d=e(window).scrollLeft()+20)),d+o+20>_&&(d=_-(o+20)),h+f+20>u&&(h=u-(f+20)),t.element.css({left:d,top:h}).show().find("a").first().trigger("focus").parent().addClass("vakata-context-hover"),t.is_visible=!0,e.vakata.context._trigger("show"))},hide:function(){t.is_visible&&(t.element.hide().find("ul").hide().end().find(":focus").trigger("blur").end().detach(),t.is_visible=!1,e.vakata.context._trigger("hide"))}},e(function(){s=e(T.body).css("direction")==="rtl";var i=!1;t.element=e("<ul class='vakata-context'></ul>"),t.element.on("mouseenter","li",function(r){r.stopImmediatePropagation(),!e.contains(this,r.relatedTarget)&&(i&&clearTimeout(i),t.element.find(".vakata-context-hover").removeClass("vakata-context-hover").end(),e(this).siblings().find("ul").hide().end().end().parentsUntil(".vakata-context","li").addBack().addClass("vakata-context-hover"),e.vakata.context._show_submenu(this))}).on("mouseleave","li",function(r){e.contains(this,r.relatedTarget)||e(this).find(".vakata-context-hover").addBack().removeClass("vakata-context-hover")}).on("mouseleave",function(r){e(this).find(".vakata-context-hover").removeClass("vakata-context-hover"),e.vakata.context.settings.hide_onmouseleave&&(i=setTimeout(function(a){return function(){e.vakata.context.hide()}}(),e.vakata.context.settings.hide_onmouseleave))}).on("click","a",function(r){r.preventDefault(),!e(this).trigger("blur").parent().hasClass("vakata-context-disabled")&&e.vakata.context._execute(e(this).attr("rel"))!==!1&&e.vakata.context.hide()}).on("keydown","a",function(r){var a=null;switch(r.which){case 13:case 32:r.type="click",r.preventDefault(),e(r.currentTarget).trigger(r);break;case 37:t.is_visible&&(t.element.find(".vakata-context-hover").last().closest("li").first().find("ul").hide().find(".vakata-context-hover").removeClass("vakata-context-hover").end().end().children("a").trigger("focus"),r.stopImmediatePropagation(),r.preventDefault());break;case 38:t.is_visible&&(a=t.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").prevAll("li:not(.vakata-context-separator)").first(),a.length||(a=t.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").last()),a.addClass("vakata-context-hover").children("a").trigger("focus"),r.stopImmediatePropagation(),r.preventDefault());break;case 39:t.is_visible&&(t.element.find(".vakata-context-hover").last().children("ul").show().children("li:not(.vakata-context-separator)").removeClass("vakata-context-hover").first().addClass("vakata-context-hover").children("a").trigger("focus"),r.stopImmediatePropagation(),r.preventDefault());break;case 40:t.is_visible&&(a=t.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").nextAll("li:not(.vakata-context-separator)").first(),a.length||(a=t.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").first()),a.addClass("vakata-context-hover").children("a").trigger("focus"),r.stopImmediatePropagation(),r.preventDefault());break;case 27:e.vakata.context.hide(),r.preventDefault();break}}).on("keydown",function(r){r.preventDefault();var a=t.element.find(".vakata-contextmenu-shortcut-"+r.which).parent();a.parent().not(".vakata-context-disabled")&&a.trigger("click")}),e(T).on("mousedown.vakata.jstree",function(r){t.is_visible&&t.element[0]!==r.target&&!e.contains(t.element[0],r.target)&&e.vakata.context.hide()}).on("context_show.vakata.jstree",function(r,a){t.element.find("li:has(ul)").children("a").addClass("vakata-context-parent"),s&&t.element.addClass("vakata-context-rtl").css("direction","rtl"),t.element.find("ul").hide().end()})})}(l),l.jstree.defaults.dnd={copy:!0,open_timeout:500,is_draggable:!0,check_while_dragging:!0,always_copy:!1,inside_pos:0,drag_selection:!0,touch:!0,large_drop_target:!1,large_drag_target:!1,use_html5:!1,blank_space_drop:!1};var I,M;l.jstree.plugins.dnd=function(e,s){this.init=function(t,i){s.init.call(this,t,i),this.settings.dnd.use_html5=this.settings.dnd.use_html5&&"draggable"in T.createElement("span")},this.bind=function(){s.bind.call(this),this.element.on(this.settings.dnd.use_html5?"dragstart.jstree":"mousedown.jstree touchstart.jstree",this.settings.dnd.large_drag_target?".jstree-node":".jstree-anchor",(function(t){if(this.settings.dnd.large_drag_target&&l(t.target).closest(".jstree-node")[0]!==t.currentTarget||t.type==="touchstart"&&(!this.settings.dnd.touch||this.settings.dnd.touch==="selected"&&!l(t.currentTarget).closest(".jstree-node").children(".jstree-anchor").hasClass("jstree-clicked")))return!0;var i=this.get_node(t.target),r=this.is_selected(i)&&this.settings.dnd.drag_selection?this.get_top_selected().length:1,a=r>1?r+" "+this.get_string("nodes"):this.get_text(t.currentTarget);if(this.settings.core.force_text&&(a=l.vakata.html.escape(a)),i&&(i.id||i.id===0)&&i.id!==l.jstree.root&&(t.which===1||t.type==="touchstart"||t.type==="dragstart")&&(this.settings.dnd.is_draggable===!0||l.vakata.is_function(this.settings.dnd.is_draggable)&&this.settings.dnd.is_draggable.call(this,r>1?this.get_top_selected(!0):[i],t)))if(I={jstree:!0,origin:this,obj:this.get_node(i,!0),nodes:r>1?this.get_top_selected():[i.id]},M=t.currentTarget,this.settings.dnd.use_html5)l.vakata.dnd._trigger("start",t,{helper:l(),element:M,data:I});else return this.element.trigger("mousedown.jstree"),l.vakata.dnd.start(t,I,'<div id="jstree-dnd" class="jstree-'+this.get_theme()+" jstree-"+this.get_theme()+"-"+this.get_theme_variant()+" "+(this.settings.core.themes.responsive?" jstree-dnd-responsive":"")+'"><i class="jstree-icon jstree-er"></i>'+a+'<ins class="jstree-copy">+</ins></div>')}).bind(this)),this.settings.dnd.use_html5&&this.element.on("dragover.jstree",function(t){return t.preventDefault(),l.vakata.dnd._trigger("move",t,{helper:l(),element:M,data:I}),!1}).on("drop.jstree",(function(t){return t.preventDefault(),l.vakata.dnd._trigger("stop",t,{helper:l(),element:M,data:I}),!1}).bind(this))},this.redraw_node=function(t,i,r,a){if(t=s.redraw_node.apply(this,arguments),t&&this.settings.dnd.use_html5)if(this.settings.dnd.large_drag_target)t.setAttribute("draggable",!0);else{var n,c,d=null;for(n=0,c=t.childNodes.length;n<c;n++)if(t.childNodes[n]&&t.childNodes[n].className&&t.childNodes[n].className.indexOf("jstree-anchor")!==-1){d=t.childNodes[n];break}d&&d.setAttribute("draggable",!0)}return t}},l(function(){var e=!1,s=!1,t=!1,i=!1,r=l('<div id="jstree-marker">&#160;</div>').hide();l(T).on("dragover.vakata.jstree",function(a){M&&l.vakata.dnd._trigger("move",a,{helper:l(),element:M,data:I})}).on("drop.vakata.jstree",function(a){M&&(l.vakata.dnd._trigger("stop",a,{helper:l(),element:M,data:I}),M=null,I=null)}).on("dnd_start.vakata.jstree",function(a,n){e=!1,t=!1,!(!n||!n.data||!n.data.jstree)&&r.appendTo(T.body)}).on("dnd_move.vakata.jstree",function(a,n){var c=n.event.target!==t.target;if(i&&(!n.event||n.event.type!=="dragover"||c)&&clearTimeout(i),!(!n||!n.data||!n.data.jstree)&&!(n.event.target.id&&n.event.target.id==="jstree-marker")){t=n.event;var d=l.jstree.reference(n.event.target),h=!1,o=!1,f=!1,_,u,g,p,m,N,O,x,A,K,P,C,S,L,F,X,v;if(d&&d._data&&d._data.dnd){if(r.attr("class","jstree-"+d.get_theme()+(d.settings.core.themes.responsive?" jstree-dnd-responsive":"")),F=n.data.origin&&(n.data.origin.settings.dnd.always_copy||n.data.origin.settings.dnd.copy&&(n.event.metaKey||n.event.ctrlKey)),n.helper.children().attr("class","jstree-"+d.get_theme()+" jstree-"+d.get_theme()+"-"+d.get_theme_variant()+" "+(d.settings.core.themes.responsive?" jstree-dnd-responsive":"")).find(".jstree-copy").first()[F?"show":"hide"](),(n.event.target===d.element[0]||n.event.target===d.get_container_ul()[0])&&(d.get_container_ul().children().length===0||d.settings.dnd.blank_space_drop)){for(O=!0,x=0,A=n.data.nodes.length;x<A&&(O=O&&d.check(n.data.origin&&(n.data.origin.settings.dnd.always_copy||n.data.origin.settings.dnd.copy&&(n.event.metaKey||n.event.ctrlKey))?"copy_node":"move_node",n.data.origin&&n.data.origin!==d?n.data.origin.get_node(n.data.nodes[x]):n.data.nodes[x],l.jstree.root,"last",{dnd:!0,ref:d.get_node(l.jstree.root),pos:"i",origin:n.data.origin,is_multi:n.data.origin&&n.data.origin!==d,is_foreign:!n.data.origin}),!!O);x++);if(O){e={ins:d,par:l.jstree.root,pos:"last"},r.hide(),n.helper.find(".jstree-icon").first().removeClass("jstree-er").addClass("jstree-ok"),n.event.originalEvent&&n.event.originalEvent.dataTransfer&&(n.event.originalEvent.dataTransfer.dropEffect=F?"copy":"move");return}}else if(h=d.settings.dnd.large_drop_target?l(n.event.target).closest(".jstree-node").children(".jstree-anchor"):l(n.event.target).closest(".jstree-anchor"),h&&h.length&&h.parent().is(".jstree-closed, .jstree-open, .jstree-leaf")&&(o=h.offset(),f=(n.event.pageY!==y?n.event.pageY:n.event.originalEvent.pageY)-o.top,g=h.outerHeight(),f<g/3?N=["b","i","a"]:f>g-g/3?N=["a","i","b"]:N=f>g/2?["i","a","b"]:["i","b","a"],l.each(N,function(H,E){switch(E){case"b":_=o.left-6,u=o.top,p=d.get_parent(h),m=h.parent().index(),v="jstree-below";break;case"i":S=d.settings.dnd.inside_pos,L=d.get_node(h.parent()),_=o.left-2,u=o.top+g/2+1,p=L.id,m=S==="first"?0:S==="last"?L.children.length:Math.min(S,L.children.length),v="jstree-inside";break;case"a":_=o.left-6,u=o.top+g,p=d.get_parent(h),m=h.parent().index()+1,v="jstree-above";break}for(O=!0,x=0,A=n.data.nodes.length;x<A;x++)if(K=n.data.origin&&(n.data.origin.settings.dnd.always_copy||n.data.origin.settings.dnd.copy&&(n.event.metaKey||n.event.ctrlKey))?"copy_node":"move_node",P=m,K==="move_node"&&E==="a"&&n.data.origin&&n.data.origin===d&&p===d.get_parent(n.data.nodes[x])&&(C=d.get_node(p),P>l.inArray(n.data.nodes[x],C.children)&&(P-=1)),O=O&&(d&&d.settings&&d.settings.dnd&&d.settings.dnd.check_while_dragging===!1||d.check(K,n.data.origin&&n.data.origin!==d?n.data.origin.get_node(n.data.nodes[x]):n.data.nodes[x],p,P,{dnd:!0,ref:d.get_node(h.parent()),pos:E,origin:n.data.origin,is_multi:n.data.origin&&n.data.origin!==d,is_foreign:!n.data.origin})),!O){d&&d.last_error&&(s=d.last_error());break}if(E==="i"&&h.parent().is(".jstree-closed")&&d.settings.dnd.open_timeout&&(!n.event||n.event.type!=="dragover"||c)&&(i&&clearTimeout(i),i=setTimeout(function(j,w){return function(){j.open_node(w)}}(d,h),d.settings.dnd.open_timeout)),O)return X=d.get_node(p,!0),X.hasClass(".jstree-dnd-parent")||(l(".jstree-dnd-parent").removeClass("jstree-dnd-parent"),X.addClass("jstree-dnd-parent")),e={ins:d,par:p,pos:E==="i"&&S==="last"&&m===0&&!d.is_loaded(L)?"last":m},r.css({left:_+"px",top:u+"px"}).show(),r.removeClass("jstree-above jstree-inside jstree-below").addClass(v),n.helper.find(".jstree-icon").first().removeClass("jstree-er").addClass("jstree-ok"),n.event.originalEvent&&n.event.originalEvent.dataTransfer&&(n.event.originalEvent.dataTransfer.dropEffect=F?"copy":"move"),s={},N=!0,!1}),N===!0))return}l(".jstree-dnd-parent").removeClass("jstree-dnd-parent"),e=!1,n.helper.find(".jstree-icon").removeClass("jstree-ok").addClass("jstree-er"),n.event.originalEvent&&n.event.originalEvent.dataTransfer,r.hide()}}).on("dnd_scroll.vakata.jstree",function(a,n){!n||!n.data||!n.data.jstree||(r.hide(),e=!1,t=!1,n.helper.find(".jstree-icon").first().removeClass("jstree-ok").addClass("jstree-er"))}).on("dnd_stop.vakata.jstree",function(a,n){if(l(".jstree-dnd-parent").removeClass("jstree-dnd-parent"),i&&clearTimeout(i),!(!n||!n.data||!n.data.jstree)){r.hide().detach();var c,d,h=[];if(e){for(c=0,d=n.data.nodes.length;c<d;c++)h[c]=n.data.origin?n.data.origin.get_node(n.data.nodes[c]):n.data.nodes[c];e.ins[n.data.origin&&(n.data.origin.settings.dnd.always_copy||n.data.origin.settings.dnd.copy&&(n.event.metaKey||n.event.ctrlKey))?"copy_node":"move_node"](h,e.par,e.pos,!1,!1,!1,n.data.origin)}else c=l(n.event.target).closest(".jstree"),c.length&&s&&s.error&&s.error==="check"&&(c=c.jstree(!0),c&&c.settings.core.error.call(this,s));t=!1,e=!1}}).on("keyup.jstree keydown.jstree",function(a,n){n=l.vakata.dnd._get(),n&&n.data&&n.data.jstree&&(a.type==="keyup"&&a.which===27?(i&&clearTimeout(i),e=!1,s=!1,t=!1,i=!1,r.hide().detach(),l.vakata.dnd._clean()):(n.helper.find(".jstree-copy").first()[n.data.origin&&(n.data.origin.settings.dnd.always_copy||n.data.origin.settings.dnd.copy&&(a.metaKey||a.ctrlKey))?"show":"hide"](),t&&(t.metaKey=a.metaKey,t.ctrlKey=a.ctrlKey,l.vakata.dnd._trigger("move",t))))})}),function(e){e.vakata.html={div:e("<div></div>"),escape:function(t){return e.vakata.html.div.text(t).html()},strip:function(t){return e.vakata.html.div.empty().append(e.parseHTML(t)).text()}};var s={element:!1,target:!1,is_down:!1,is_drag:!1,helper:!1,helper_w:0,data:!1,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:!1,scroll_i:!1,is_touch:!1};e.vakata.dnd={settings:{scroll_speed:10,scroll_proximity:20,helper_left:5,helper_top:10,threshold:5,threshold_touch:10},_trigger:function(t,i,r){r===y&&(r=e.vakata.dnd._get()),r.event=i,e(T).triggerHandler("dnd_"+t+".vakata",r)},_get:function(){return{data:s.data,element:s.element,helper:s.helper}},_clean:function(){s.helper&&s.helper.remove(),s.scroll_i&&(clearInterval(s.scroll_i),s.scroll_i=!1),s={element:!1,target:!1,is_down:!1,is_drag:!1,helper:!1,helper_w:0,data:!1,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:!1,scroll_i:!1,is_touch:!1},M=null,e(T).off("mousemove.vakata.jstree touchmove.vakata.jstree",e.vakata.dnd.drag),e(T).off("mouseup.vakata.jstree touchend.vakata.jstree",e.vakata.dnd.stop)},_scroll:function(t){if(!s.scroll_e||!s.scroll_l&&!s.scroll_t)return s.scroll_i&&(clearInterval(s.scroll_i),s.scroll_i=!1),!1;if(!s.scroll_i)return s.scroll_i=setInterval(e.vakata.dnd._scroll,100),!1;if(t===!0)return!1;var i=s.scroll_e.scrollTop(),r=s.scroll_e.scrollLeft();s.scroll_e.scrollTop(i+s.scroll_t*e.vakata.dnd.settings.scroll_speed),s.scroll_e.scrollLeft(r+s.scroll_l*e.vakata.dnd.settings.scroll_speed),(i!==s.scroll_e.scrollTop()||r!==s.scroll_e.scrollLeft())&&e.vakata.dnd._trigger("scroll",s.scroll_e)},start:function(t,i,r){t.type==="touchstart"&&t.originalEvent&&t.originalEvent.changedTouches&&t.originalEvent.changedTouches[0]&&(t.pageX=t.originalEvent.changedTouches[0].pageX,t.pageY=t.originalEvent.changedTouches[0].pageY,t.target=T.elementFromPoint(t.originalEvent.changedTouches[0].pageX-window.pageXOffset,t.originalEvent.changedTouches[0].pageY-window.pageYOffset)),s.is_drag&&e.vakata.dnd.stop({});try{t.currentTarget.unselectable="on",t.currentTarget.onselectstart=function(){return!1},t.currentTarget.style&&(t.currentTarget.style.touchAction="none",t.currentTarget.style.msTouchAction="none",t.currentTarget.style.MozUserSelect="none")}catch{}return s.init_x=t.pageX,s.init_y=t.pageY,s.data=i,s.is_down=!0,s.element=t.currentTarget,s.target=t.target,s.is_touch=t.type==="touchstart",r!==!1&&(s.helper=e("<div id='vakata-dnd'></div>").html(r).css({display:"block",margin:"0",padding:"0",position:"absolute",top:"-2000px",lineHeight:"16px",zIndex:"10000"})),e(T).on("mousemove.vakata.jstree touchmove.vakata.jstree",e.vakata.dnd.drag),e(T).on("mouseup.vakata.jstree touchend.vakata.jstree",e.vakata.dnd.stop),!1},drag:function(t){if(t.type==="touchmove"&&t.originalEvent&&t.originalEvent.changedTouches&&t.originalEvent.changedTouches[0]&&(t.pageX=t.originalEvent.changedTouches[0].pageX,t.pageY=t.originalEvent.changedTouches[0].pageY,t.target=T.elementFromPoint(t.originalEvent.changedTouches[0].pageX-window.pageXOffset,t.originalEvent.changedTouches[0].pageY-window.pageYOffset)),!!s.is_down){if(!s.is_drag)if(Math.abs(t.pageX-s.init_x)>(s.is_touch?e.vakata.dnd.settings.threshold_touch:e.vakata.dnd.settings.threshold)||Math.abs(t.pageY-s.init_y)>(s.is_touch?e.vakata.dnd.settings.threshold_touch:e.vakata.dnd.settings.threshold))s.helper&&(s.helper.appendTo(T.body),s.helper_w=s.helper.outerWidth()),s.is_drag=!0,e(s.target).one("click.vakata",!1),e.vakata.dnd._trigger("start",t);else return;var i=!1,r=!1,a=!1,n=!1,c=!1,d=!1,h=!1,o=!1,f=!1,_=!1;return s.scroll_t=0,s.scroll_l=0,s.scroll_e=!1,e(e(t.target).parentsUntil("body").addBack().get().reverse()).filter(function(){return this.ownerDocument&&/^auto|scroll$/.test(e(this).css("overflow"))&&(this.scrollHeight>this.offsetHeight||this.scrollWidth>this.offsetWidth)}).each(function(){var u=e(this),g=u.offset();if(this.scrollHeight>this.offsetHeight&&(g.top+u.height()-t.pageY<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_t=1),t.pageY-g.top<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_t=-1)),this.scrollWidth>this.offsetWidth&&(g.left+u.width()-t.pageX<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_l=1),t.pageX-g.left<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_l=-1)),s.scroll_t||s.scroll_l)return s.scroll_e=e(this),!1}),s.scroll_e||(i=e(T),r=e(window),a=i.height(),n=r.height(),c=i.width(),d=r.width(),h=i.scrollTop(),o=i.scrollLeft(),a>n&&t.pageY-h<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_t=-1),a>n&&n-(t.pageY-h)<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_t=1),c>d&&t.pageX-o<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_l=-1),c>d&&d-(t.pageX-o)<e.vakata.dnd.settings.scroll_proximity&&(s.scroll_l=1),(s.scroll_t||s.scroll_l)&&(s.scroll_e=i)),s.scroll_e&&e.vakata.dnd._scroll(!0),s.helper&&(f=parseInt(t.pageY+e.vakata.dnd.settings.helper_top,10),_=parseInt(t.pageX+e.vakata.dnd.settings.helper_left,10),a&&f+25>a&&(f=a-50),c&&_+s.helper_w>c&&(_=c-(s.helper_w+2)),s.helper.css({left:_+"px",top:f+"px"})),e.vakata.dnd._trigger("move",t),!1}},stop:function(t){if(t.type==="touchend"&&t.originalEvent&&t.originalEvent.changedTouches&&t.originalEvent.changedTouches[0]&&(t.pageX=t.originalEvent.changedTouches[0].pageX,t.pageY=t.originalEvent.changedTouches[0].pageY,t.target=T.elementFromPoint(t.originalEvent.changedTouches[0].pageX-window.pageXOffset,t.originalEvent.changedTouches[0].pageY-window.pageYOffset)),s.is_drag)t.target!==s.target&&e(s.target).off("click.vakata"),e.vakata.dnd._trigger("stop",t);else if(t.type==="touchend"&&t.target===s.target){var i=setTimeout(function(){e(t.target).trigger("click")},100);e(t.target).one("click",function(){i&&clearTimeout(i)})}return e.vakata.dnd._clean(),!1}}}(l),l.jstree.defaults.massload=null,l.jstree.plugins.massload=function(e,s){this.init=function(t,i){this._data.massload={},s.init.call(this,t,i)},this._load_nodes=function(t,i,r,a){var n=this.settings.massload,c=[],d=this._model.data,h,o,f;if(!r){for(h=0,o=t.length;h<o;h++)(!d[t[h]]||!d[t[h]].state.loaded&&!d[t[h]].state.failed||a)&&(c.push(t[h]),f=this.get_node(t[h],!0),f&&f.length&&f.addClass("jstree-loading").attr("aria-busy",!0));if(this._data.massload={},c.length){if(l.vakata.is_function(n))return n.call(this,c,(function(_){var u,g;if(_)for(u in _)_.hasOwnProperty(u)&&(this._data.massload[u]=_[u]);for(u=0,g=t.length;u<g;u++)f=this.get_node(t[u],!0),f&&f.length&&f.removeClass("jstree-loading").attr("aria-busy",!1);s._load_nodes.call(this,t,i,r,a)}).bind(this));if(typeof n=="object"&&n&&n.url)return n=l.extend(!0,{},n),l.vakata.is_function(n.url)&&(n.url=n.url.call(this,c)),l.vakata.is_function(n.data)&&(n.data=n.data.call(this,c)),l.ajax(n).done((function(_,u,g){var p,m;if(_)for(p in _)_.hasOwnProperty(p)&&(this._data.massload[p]=_[p]);for(p=0,m=t.length;p<m;p++)f=this.get_node(t[p],!0),f&&f.length&&f.removeClass("jstree-loading").attr("aria-busy",!1);s._load_nodes.call(this,t,i,r,a)}).bind(this)).fail((function(_){s._load_nodes.call(this,t,i,r,a)}).bind(this))}}return s._load_nodes.call(this,t,i,r,a)},this._load_node=function(t,i){var r=this._data.massload[t.id],a=null,n;return r?(a=this[typeof r=="string"?"_append_html_data":"_append_json_data"](t,typeof r=="string"?l(l.parseHTML(r)).filter(function(){return this.nodeType!==3}):r,function(c){i.call(this,c)}),n=this.get_node(t.id,!0),n&&n.length&&n.removeClass("jstree-loading").attr("aria-busy",!1),delete this._data.massload[t.id],a):s._load_node.call(this,t,i)}},l.jstree.defaults.search={ajax:!1,fuzzy:!1,case_sensitive:!1,show_only_matches:!1,show_only_matches_children:!1,close_opened_onclear:!0,search_leaves_only:!1,search_callback:!1},l.jstree.plugins.search=function(e,s){this.bind=function(){s.bind.call(this),this._data.search.str="",this._data.search.dom=l(),this._data.search.res=[],this._data.search.opn=[],this._data.search.som=!1,this._data.search.smc=!1,this._data.search.hdn=[],this.element.on("search.jstree",(function(t,i){if(this._data.search.som&&i.res.length){var r=this._model.data,a,n,c=[],d,h;for(a=0,n=i.res.length;a<n;a++)if(r[i.res[a]]&&!r[i.res[a]].state.hidden&&(c.push(i.res[a]),c=c.concat(r[i.res[a]].parents),this._data.search.smc))for(d=0,h=r[i.res[a]].children_d.length;d<h;d++)r[r[i.res[a]].children_d[d]]&&!r[r[i.res[a]].children_d[d]].state.hidden&&c.push(r[i.res[a]].children_d[d]);c=l.vakata.array_remove_item(l.vakata.array_unique(c),l.jstree.root),this._data.search.hdn=this.hide_all(!0),this.show_node(c,!0),this.redraw(!0)}}).bind(this)).on("clear_search.jstree",(function(t,i){this._data.search.som&&i.res.length&&(this.show_node(this._data.search.hdn,!0),this.redraw(!0))}).bind(this))},this.search=function(t,i,r,a,n,c){if(t===!1||l.vakata.trim(t.toString())==="")return this.clear_search();a=this.get_node(a),a=a&&(a.id||a.id===0)?a.id:null,t=t.toString();var d=this.settings.search,h=d.ajax?d.ajax:!1,o=this._model.data,f=null,_=[],u=[],g,p;if(this._data.search.res.length&&!n&&this.clear_search(),r===y&&(r=d.show_only_matches),c===y&&(c=d.show_only_matches_children),!i&&h!==!1)return l.vakata.is_function(h)?h.call(this,t,(function(m){m&&m.d&&(m=m.d),this._load_nodes(l.vakata.is_array(m)?l.vakata.array_unique(m):[],function(){this.search(t,!0,r,a,n,c)})}).bind(this),a):(h=l.extend({},h),h.data||(h.data={}),h.data.str=t,a&&(h.data.inside=a),this._data.search.lastRequest&&this._data.search.lastRequest.abort(),this._data.search.lastRequest=l.ajax(h).fail((function(){this._data.core.last_error={error:"ajax",plugin:"search",id:"search_01",reason:"Could not load search parents",data:JSON.stringify(h)},this.settings.core.error.call(this,this._data.core.last_error)}).bind(this)).done((function(m){m&&m.d&&(m=m.d),this._load_nodes(l.vakata.is_array(m)?l.vakata.array_unique(m):[],function(){this.search(t,!0,r,a,n,c)})}).bind(this)),this._data.search.lastRequest);if(n||(this._data.search.str=t,this._data.search.dom=l(),this._data.search.res=[],this._data.search.opn=[],this._data.search.som=r,this._data.search.smc=c),f=new l.vakata.search(t,!0,{caseSensitive:d.case_sensitive,fuzzy:d.fuzzy}),l.each(o[a||l.jstree.root].children_d,function(m,N){var O=o[N];O.text&&!O.state.hidden&&(!d.search_leaves_only||O.state.loaded&&O.children.length===0)&&(d.search_callback&&d.search_callback.call(this,t,O)||!d.search_callback&&f.search(O.text).isMatch)&&(_.push(N),u=u.concat(O.parents))}),_.length){for(u=l.vakata.array_unique(u),g=0,p=u.length;g<p;g++)u[g]!==l.jstree.root&&o[u[g]]&&this.open_node(u[g],null,0)===!0&&this._data.search.opn.push(u[g]);n?(this._data.search.dom=this._data.search.dom.add(l(this.element[0].querySelectorAll("#"+l.map(_,function(m){return"0123456789".indexOf(m[0])!==-1?"\\3"+m[0]+" "+m.substr(1).replace(l.jstree.idregex,"\\$&"):m.replace(l.jstree.idregex,"\\$&")}).join(", #")))),this._data.search.res=l.vakata.array_unique(this._data.search.res.concat(_))):(this._data.search.dom=l(this.element[0].querySelectorAll("#"+l.map(_,function(m){return"0123456789".indexOf(m[0])!==-1?"\\3"+m[0]+" "+m.substr(1).replace(l.jstree.idregex,"\\$&"):m.replace(l.jstree.idregex,"\\$&")}).join(", #"))),this._data.search.res=_),this._data.search.dom.children(".jstree-anchor").addClass("jstree-search")}this.trigger("search",{nodes:this._data.search.dom,str:t,res:this._data.search.res,show_only_matches:r})},this.clear_search=function(){this.settings.search.close_opened_onclear&&this.close_node(this._data.search.opn,0),this.trigger("clear_search",{nodes:this._data.search.dom,str:this._data.search.str,res:this._data.search.res}),this._data.search.res.length&&(this._data.search.dom=l(this.element[0].querySelectorAll("#"+l.map(this._data.search.res,function(t){return"0123456789".indexOf(t[0])!==-1?"\\3"+t[0]+" "+t.substr(1).replace(l.jstree.idregex,"\\$&"):t.replace(l.jstree.idregex,"\\$&")}).join(", #"))),this._data.search.dom.children(".jstree-anchor").removeClass("jstree-search")),this._data.search.str="",this._data.search.res=[],this._data.search.opn=[],this._data.search.dom=l()},this.redraw_node=function(t,i,r,a){if(t=s.redraw_node.apply(this,arguments),t&&l.inArray(t.id,this._data.search.res)!==-1){var n,c,d=null;for(n=0,c=t.childNodes.length;n<c;n++)if(t.childNodes[n]&&t.childNodes[n].className&&t.childNodes[n].className.indexOf("jstree-anchor")!==-1){d=t.childNodes[n];break}d&&(d.className+=" jstree-search")}return t}},function(e){e.vakata.search=function(s,t,i){i=i||{},i=e.extend({},e.vakata.search.defaults,i),i.fuzzy!==!1&&(i.fuzzy=!0),s=i.caseSensitive?s:s.toLowerCase();var r=i.location,a=i.distance,n=i.threshold,c=s.length,d,h,o,f;return c>32&&(i.fuzzy=!1),i.fuzzy&&(d=1<<c-1,h=function(){var _={},u=0;for(u=0;u<c;u++)_[s.charAt(u)]=0;for(u=0;u<c;u++)_[s.charAt(u)]|=1<<c-u-1;return _}(),o=function(_,u){var g=_/c,p=Math.abs(r-u);return a?g+p/a:p?1:g}),f=function(_){if(_=i.caseSensitive?_.toString():_.toString().toLowerCase(),s===_||_.indexOf(s)!==-1)return{isMatch:!0,score:0};if(!i.fuzzy)return{isMatch:!1,score:1};var u,g,p=_.length,m=n,N=_.indexOf(s,r),O,x,A=c+p,K,P,C,S,L,F=1;for(N!==-1&&(m=Math.min(o(0,N),m),N=_.lastIndexOf(s,r+c),N!==-1&&(m=Math.min(o(0,N),m))),N=-1,u=0;u<c;u++){for(O=0,x=A;O<x;)o(u,r+x)<=m?O=x:A=x,x=Math.floor((A-O)/2+O);for(A=x,P=Math.max(1,r-x+1),C=Math.min(r+x,p)+c,S=new Array(C+2),S[C+1]=(1<<u)-1,g=C;g>=P;g--)if(L=h[_.charAt(g-1)],u===0?S[g]=(S[g+1]<<1|1)&L:S[g]=(S[g+1]<<1|1)&L|((K[g+1]|K[g])<<1|1)|K[g+1],S[g]&d&&(F=o(u,g-1),F<=m))if(m=F,N=g-1,N>r)P=Math.max(1,2*r-N);else break;if(o(u+1,r)>m)break;K=S}return{isMatch:N>=0,score:F}},t===!0?{search:f}:f(t)},e.vakata.search.defaults={location:0,distance:100,threshold:.6,fuzzy:!1,caseSensitive:!1}}(l),l.jstree.defaults.sort=function(e,s){return this.get_text(e)>this.get_text(s)?1:-1},l.jstree.plugins.sort=function(e,s){this.bind=function(){s.bind.call(this),this.element.on("model.jstree",(function(t,i){this.sort(i.parent,!0)}).bind(this)).on("rename_node.jstree create_node.jstree",(function(t,i){this.sort(i.parent||i.node.parent,!1),this.redraw_node(i.parent||i.node.parent,!0)}).bind(this)).on("move_node.jstree copy_node.jstree",(function(t,i){this.sort(i.parent,!1),this.redraw_node(i.parent,!0)}).bind(this))},this.sort=function(t,i){var r,a;if(t=this.get_node(t),t&&t.children&&t.children.length&&(t.children.sort(this.settings.sort.bind(this)),i))for(r=0,a=t.children_d.length;r<a;r++)this.sort(t.children_d[r],!1)}};var Q=!1;l.jstree.defaults.state={key:"jstree",events:"changed.jstree open_node.jstree close_node.jstree check_node.jstree uncheck_node.jstree",ttl:!1,filter:!1,preserve_loaded:!1},l.jstree.plugins.state=function(e,s){this.bind=function(){s.bind.call(this);var t=(function(){this.element.on(this.settings.state.events,(function(){Q&&clearTimeout(Q),Q=setTimeout((function(){this.save_state()}).bind(this),100)}).bind(this)),this.trigger("state_ready")}).bind(this);this.element.on("ready.jstree",(function(i,r){this.element.one("restore_state.jstree",t),this.restore_state()||t()}).bind(this))},this.save_state=function(){var t=this.get_state();this.settings.state.preserve_loaded||delete t.core.loaded;var i={state:t,ttl:this.settings.state.ttl,sec:+new Date};l.vakata.storage.set(this.settings.state.key,JSON.stringify(i))},this.restore_state=function(){var t=l.vakata.storage.get(this.settings.state.key);if(t)try{t=JSON.parse(t)}catch{return!1}return t&&t.ttl&&t.sec&&+new Date-t.sec>t.ttl?!1:(t&&t.state&&(t=t.state),t&&l.vakata.is_function(this.settings.state.filter)&&(t=this.settings.state.filter.call(this,t)),t?(this.settings.state.preserve_loaded||delete t.core.loaded,this.element.one("set_state.jstree",function(i,r){r.instance.trigger("restore_state",{state:l.extend(!0,{},t)})}),this.set_state(t),!0):!1)},this.clear_state=function(){return l.vakata.storage.del(this.settings.state.key)}},function(e,s){e.vakata.storage={set:function(t,i){return window.localStorage.setItem(t,i)},get:function(t){return window.localStorage.getItem(t)},del:function(t){return window.localStorage.removeItem(t)}}}(l),l.jstree.defaults.types={default:{}},l.jstree.defaults.types[l.jstree.root]={},l.jstree.plugins.types=function(e,s){this.init=function(t,i){var r,a;if(i&&i.types&&i.types.default){for(r in i.types)if(r!=="default"&&r!==l.jstree.root&&i.types.hasOwnProperty(r))for(a in i.types.default)i.types.default.hasOwnProperty(a)&&i.types[r][a]===y&&(i.types[r][a]=i.types.default[a])}s.init.call(this,t,i),this._model.data[l.jstree.root].type=l.jstree.root},this.refresh=function(t,i){s.refresh.call(this,t,i),this._model.data[l.jstree.root].type=l.jstree.root},this.bind=function(){this.element.on("model.jstree",(function(t,i){var r=this._model.data,a=i.nodes,n=this.settings.types,c,d,h="default",o;for(c=0,d=a.length;c<d;c++){if(h="default",r[a[c]].original&&r[a[c]].original.type&&n[r[a[c]].original.type]&&(h=r[a[c]].original.type),r[a[c]].data&&r[a[c]].data.jstree&&r[a[c]].data.jstree.type&&n[r[a[c]].data.jstree.type]&&(h=r[a[c]].data.jstree.type),r[a[c]].type=h,r[a[c]].icon===!0&&n[h].icon!==y&&(r[a[c]].icon=n[h].icon),n[h].li_attr!==y&&typeof n[h].li_attr=="object"){for(o in n[h].li_attr)if(n[h].li_attr.hasOwnProperty(o)){if(o==="id")continue;r[a[c]].li_attr[o]===y?r[a[c]].li_attr[o]=n[h].li_attr[o]:o==="class"&&(r[a[c]].li_attr.class=n[h].li_attr.class+" "+r[a[c]].li_attr.class)}}if(n[h].a_attr!==y&&typeof n[h].a_attr=="object"){for(o in n[h].a_attr)if(n[h].a_attr.hasOwnProperty(o)){if(o==="id")continue;r[a[c]].a_attr[o]===y?r[a[c]].a_attr[o]=n[h].a_attr[o]:o==="href"&&r[a[c]].a_attr[o]==="#"?r[a[c]].a_attr.href=n[h].a_attr.href:o==="class"&&(r[a[c]].a_attr.class=n[h].a_attr.class+" "+r[a[c]].a_attr.class)}}}r[l.jstree.root].type=l.jstree.root}).bind(this)),s.bind.call(this)},this.get_json=function(t,i,r){var a,n,c=this._model.data,d=i?l.extend(!0,{},i,{no_id:!1}):{},h=s.get_json.call(this,t,d,r);if(h===!1)return!1;if(l.vakata.is_array(h))for(a=0,n=h.length;a<n;a++)h[a].type=(h[a].id||h[a].id===0)&&c[h[a].id]&&c[h[a].id].type?c[h[a].id].type:"default",i&&i.no_id&&(delete h[a].id,h[a].li_attr&&h[a].li_attr.id&&delete h[a].li_attr.id,h[a].a_attr&&h[a].a_attr.id&&delete h[a].a_attr.id);else h.type=(h.id||h.id===0)&&c[h.id]&&c[h.id].type?c[h.id].type:"default",i&&i.no_id&&(h=this._delete_ids(h));return h},this._delete_ids=function(t){if(l.vakata.is_array(t)){for(var i=0,r=t.length;i<r;i++)t[i]=this._delete_ids(t[i]);return t}return delete t.id,t.li_attr&&t.li_attr.id&&delete t.li_attr.id,t.a_attr&&t.a_attr.id&&delete t.a_attr.id,t.children&&l.vakata.is_array(t.children)&&(t.children=this._delete_ids(t.children)),t},this.check=function(t,i,r,a,n){if(s.check.call(this,t,i,r,a,n)===!1)return!1;i=i&&(i.id||i.id===0)?i:this.get_node(i),r=r&&(r.id||r.id===0)?r:this.get_node(r);var c=i&&(i.id||i.id===0)?n&&n.origin?n.origin:l.jstree.reference(i.id):null,d,h,o,f;switch(c=c&&c._model&&c._model.data?c._model.data:null,t){case"create_node":case"move_node":case"copy_node":if(t!=="move_node"||l.inArray(i.id,r.children)===-1){if(d=this.get_rules(r),d.max_children!==y&&d.max_children!==-1&&d.max_children===r.children.length)return this._data.core.last_error={error:"check",plugin:"types",id:"types_01",reason:"max_children prevents function: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})},!1;if(d.valid_children!==y&&d.valid_children!==-1&&l.inArray(i.type||"default",d.valid_children)===-1)return this._data.core.last_error={error:"check",plugin:"types",id:"types_02",reason:"valid_children prevents function: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})},!1;if(c&&i.children_d&&i.parents){for(h=0,o=0,f=i.children_d.length;o<f;o++)h=Math.max(h,c[i.children_d[o]].parents.length);h=h-i.parents.length+1}(h<=0||h===y)&&(h=1);do{if(d.max_depth!==y&&d.max_depth!==-1&&d.max_depth<h)return this._data.core.last_error={error:"check",plugin:"types",id:"types_03",reason:"max_depth prevents function: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})},!1;r=this.get_node(r.parent),d=this.get_rules(r),h++}while(r)}break}return!0},this.get_rules=function(t){if(t=this.get_node(t),!t)return!1;var i=this.get_type(t,!0);return i.max_depth===y&&(i.max_depth=-1),i.max_children===y&&(i.max_children=-1),i.valid_children===y&&(i.valid_children=-1),i},this.get_type=function(t,i){return t=this.get_node(t),t?i?l.extend({type:t.type},this.settings.types[t.type]):t.type:!1},this.set_type=function(t,i){var r=this._model.data,a,n,c,d,h,o,f,_;if(l.vakata.is_array(t)){for(t=t.slice(),n=0,c=t.length;n<c;n++)this.set_type(t[n],i);return!0}if(a=this.settings.types,t=this.get_node(t),!a[i]||!t)return!1;if(f=this.get_node(t,!0),f&&f.length&&(_=f.children(".jstree-anchor")),d=t.type,h=this.get_icon(t),t.type=i,(h===!0||!a[d]||a[d].icon!==y&&h===a[d].icon)&&this.set_icon(t,a[i].icon!==y?a[i].icon:!0),a[d]&&a[d].li_attr!==y&&typeof a[d].li_attr=="object"){for(o in a[d].li_attr)if(a[d].li_attr.hasOwnProperty(o)){if(o==="id")continue;o==="class"?(r[t.id].li_attr.class=(r[t.id].li_attr.class||"").replace(a[d].li_attr[o],""),f&&f.removeClass(a[d].li_attr[o])):r[t.id].li_attr[o]===a[d].li_attr[o]&&(r[t.id].li_attr[o]=null,f&&f.removeAttr(o))}}if(a[d]&&a[d].a_attr!==y&&typeof a[d].a_attr=="object"){for(o in a[d].a_attr)if(a[d].a_attr.hasOwnProperty(o)){if(o==="id")continue;o==="class"?(r[t.id].a_attr.class=(r[t.id].a_attr.class||"").replace(a[d].a_attr[o],""),_&&_.removeClass(a[d].a_attr[o])):r[t.id].a_attr[o]===a[d].a_attr[o]&&(o==="href"?(r[t.id].a_attr[o]="#",_&&_.attr("href","#")):(delete r[t.id].a_attr[o],_&&_.removeAttr(o)))}}if(a[i].li_attr!==y&&typeof a[i].li_attr=="object"){for(o in a[i].li_attr)if(a[i].li_attr.hasOwnProperty(o)){if(o==="id")continue;r[t.id].li_attr[o]===y?(r[t.id].li_attr[o]=a[i].li_attr[o],f&&(o==="class"?f.addClass(a[i].li_attr[o]):f.attr(o,a[i].li_attr[o]))):o==="class"&&(r[t.id].li_attr.class=a[i].li_attr[o]+" "+r[t.id].li_attr.class,f&&f.addClass(a[i].li_attr[o]))}}if(a[i].a_attr!==y&&typeof a[i].a_attr=="object"){for(o in a[i].a_attr)if(a[i].a_attr.hasOwnProperty(o)){if(o==="id")continue;r[t.id].a_attr[o]===y?(r[t.id].a_attr[o]=a[i].a_attr[o],_&&(o==="class"?_.addClass(a[i].a_attr[o]):_.attr(o,a[i].a_attr[o]))):o==="href"&&r[t.id].a_attr[o]==="#"?(r[t.id].a_attr.href=a[i].a_attr.href,_&&_.attr("href",a[i].a_attr.href)):o==="class"&&(r[t.id].a_attr.class=a[i].a_attr.class+" "+r[t.id].a_attr.class,_&&_.addClass(a[i].a_attr[o]))}}return!0}},l.jstree.defaults.unique={case_sensitive:!1,trim_whitespace:!1,duplicate:function(e,s){return e+" ("+s+")"}},l.jstree.plugins.unique=function(e,s){this.check=function(t,i,r,a,n){if(s.check.call(this,t,i,r,a,n)===!1)return!1;if(i=i&&(i.id||i.id===0)?i:this.get_node(i),r=r&&(r.id||r.id===0)?r:this.get_node(r),!r||!r.children)return!0;var c=t==="rename_node"?a:i.text,d=[],h=this.settings.unique.case_sensitive,o=this.settings.unique.trim_whitespace,f=this._model.data,_,u,g;for(_=0,u=r.children.length;_<u;_++)g=f[r.children[_]].text,h||(g=g.toLowerCase()),o&&(g=g.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")),d.push(g);switch(h||(c=c.toLowerCase()),o&&(c=c.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")),t){case"delete_node":return!0;case"rename_node":return g=i.text||"",h||(g=g.toLowerCase()),o&&(g=g.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")),_=l.inArray(c,d)===-1||i.text&&g===c,_||(this._data.core.last_error={error:"check",plugin:"unique",id:"unique_01",reason:"Child with name "+c+" already exists. Preventing: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})}),_;case"create_node":return _=l.inArray(c,d)===-1,_||(this._data.core.last_error={error:"check",plugin:"unique",id:"unique_04",reason:"Child with name "+c+" already exists. Preventing: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})}),_;case"copy_node":return _=l.inArray(c,d)===-1,_||(this._data.core.last_error={error:"check",plugin:"unique",id:"unique_02",reason:"Child with name "+c+" already exists. Preventing: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})}),_;case"move_node":return _=i.parent===r.id&&(!n||!n.is_multi)||l.inArray(c,d)===-1,_||(this._data.core.last_error={error:"check",plugin:"unique",id:"unique_03",reason:"Child with name "+c+" already exists. Preventing: "+t,data:JSON.stringify({chk:t,pos:a,obj:i&&(i.id||i.id===0)?i.id:!1,par:r&&(r.id||r.id===0)?r.id:!1})}),_}return!0},this.create_node=function(t,i,r,a,n){if(!i||typeof i=="object"&&i.text===y){if(t===null&&(t=l.jstree.root),t=this.get_node(t),!t)return s.create_node.call(this,t,i,r,a,n);if(r=r===y?"last":r,!r.toString().match(/^(before|after)$/)&&!n&&!this.is_loaded(t))return s.create_node.call(this,t,i,r,a,n);i||(i={});var c,d,h,o,f,_=this._model.data,u=this.settings.unique.case_sensitive,g=this.settings.unique.trim_whitespace,p=this.settings.unique.duplicate,m;for(d=c=this.get_string("New node"),h=[],o=0,f=t.children.length;o<f;o++)m=_[t.children[o]].text,u||(m=m.toLowerCase()),g&&(m=m.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")),h.push(m);for(o=1,m=d,u||(m=m.toLowerCase()),g&&(m=m.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""));l.inArray(m,h)!==-1;)d=p.call(this,c,++o).toString(),m=d,u||(m=m.toLowerCase()),g&&(m=m.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""));i.text=d}return s.create_node.call(this,t,i,r,a,n)}};var U=T.createElement("DIV");if(U.setAttribute("unselectable","on"),U.setAttribute("role","presentation"),U.className="jstree-wholerow",U.innerHTML="&#160;",l.jstree.plugins.wholerow=function(e,s){this.bind=function(){s.bind.call(this),this.element.on("ready.jstree set_state.jstree",(function(){this.hide_dots()}).bind(this)).on("init.jstree loading.jstree ready.jstree",(function(){this.get_container_ul().addClass("jstree-wholerow-ul")}).bind(this)).on("deselect_all.jstree",(function(t,i){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked")}).bind(this)).on("changed.jstree",(function(t,i){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked");var r=!1,a,n;for(a=0,n=i.selected.length;a<n;a++)r=this.get_node(i.selected[a],!0),r&&r.length&&r.children(".jstree-wholerow").addClass("jstree-wholerow-clicked")}).bind(this)).on("open_node.jstree",(function(t,i){this.get_node(i.node,!0).find(".jstree-clicked").parent().children(".jstree-wholerow").addClass("jstree-wholerow-clicked")}).bind(this)).on("hover_node.jstree dehover_node.jstree",(function(t,i){t.type==="hover_node"&&this.is_disabled(i.node)||this.get_node(i.node,!0).children(".jstree-wholerow")[t.type==="hover_node"?"addClass":"removeClass"]("jstree-wholerow-hovered")}).bind(this)).on("contextmenu.jstree",".jstree-wholerow",(function(t){if(this._data.contextmenu){t.preventDefault();var i=l.Event("contextmenu",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,pageX:t.pageX,pageY:t.pageY});l(t.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(i)}}).bind(this)).on("click.jstree",".jstree-wholerow",function(t){t.stopImmediatePropagation();var i=l.Event("click",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey});l(t.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(i).trigger("focus")}).on("dblclick.jstree",".jstree-wholerow",function(t){t.stopImmediatePropagation();var i=l.Event("dblclick",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey});l(t.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(i).trigger("focus")}).on("click.jstree",".jstree-leaf > .jstree-ocl",(function(t){t.stopImmediatePropagation();var i=l.Event("click",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey});l(t.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(i).trigger("focus")}).bind(this)).on("mouseover.jstree",".jstree-wholerow, .jstree-icon",(function(t){return t.stopImmediatePropagation(),this.is_disabled(t.currentTarget)||this.hover_node(t.currentTarget),!1}).bind(this)).on("mouseleave.jstree",".jstree-node",(function(t){this.dehover_node(t.currentTarget)}).bind(this))},this.teardown=function(){this.settings.wholerow&&this.element.find(".jstree-wholerow").remove(),s.teardown.call(this)},this.redraw_node=function(t,i,r,a){if(t=s.redraw_node.apply(this,arguments),t){var n=U.cloneNode(!0);l.inArray(t.id,this._data.core.selected)!==-1&&(n.className+=" jstree-wholerow-clicked"),this._data.core.focused&&this._data.core.focused===t.id&&(n.className+=" jstree-wholerow-hovered"),t.insertBefore(n,t.childNodes[0])}return t}},window.customElements&&Object&&Object.create){var $=Object.create(HTMLElement.prototype);$.createdCallback=function(){var e={core:{},plugins:[]},s;for(s in l.jstree.plugins)l.jstree.plugins.hasOwnProperty(s)&&this.attributes[s]&&(e.plugins.push(s),this.getAttribute(s)&&JSON.parse(this.getAttribute(s))&&(e[s]=JSON.parse(this.getAttribute(s))));for(s in l.jstree.defaults.core)l.jstree.defaults.core.hasOwnProperty(s)&&this.attributes[s]&&(e.core[s]=JSON.parse(this.getAttribute(s))||this.getAttribute(s));l(this).jstree(e)};try{window.customElements.define("vakata-jstree",function(){},{prototype:$})}catch{}}}})})(ie);
