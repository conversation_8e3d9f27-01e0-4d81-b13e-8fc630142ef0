window.isRtl=window.Helpers.isRtl();window.isDarkStyle=window.Helpers.isDarkStyle();let y,v=!1;document.getElementById("layout-menu")&&(v=document.getElementById("layout-menu").classList.contains("menu-horizontal"));(function(){var S,L;setTimeout(function(){window.Helpers.initCustomOptionCheck()},1e3),typeof Waves<"u"&&(Waves.init(),Waves.attach(".btn[class*='btn-']:not(.position-relative):not([class*='btn-outline-']):not([class*='btn-label-'])",["waves-light"]),Waves.attach("[class*='btn-outline-']:not(.position-relative)"),Waves.attach("[class*='btn-label-']:not(.position-relative)"),Waves.attach(".pagination .page-item .page-link"),Waves.attach(".dropdown-menu .dropdown-item"),Waves.attach(".light-style .list-group .list-group-item-action"),Waves.attach(".dark-style .list-group .list-group-item-action",["waves-light"]),Waves.attach(".nav-tabs:not(.nav-tabs-widget) .nav-item .nav-link"),Waves.attach(".nav-pills .nav-item .nav-link",["waves-light"])),document.querySelectorAll("#layout-menu").forEach(function(e){y=new Menu(e,{orientation:v?"horizontal":"vertical",closeChildren:!!v,showDropdownOnHover:localStorage.getItem("templateCustomizer-"+templateName+"--ShowDropdownOnHover")?localStorage.getItem("templateCustomizer-"+templateName+"--ShowDropdownOnHover")==="true":window.templateCustomizer!==void 0?window.templateCustomizer.settings.defaultShowDropdownOnHover:!0}),window.Helpers.scrollToActive(!1),window.Helpers.mainMenu=y}),document.querySelectorAll(".layout-menu-toggle").forEach(e=>{e.addEventListener("click",a=>{if(a.preventDefault(),window.Helpers.toggleCollapsed(),config.enableMenuLocalStorage&&!window.Helpers.isSmallScreen())try{localStorage.setItem("templateCustomizer-"+templateName+"--LayoutCollapsed",String(window.Helpers.isCollapsed()));let o=document.querySelector(".template-customizer-layouts-options");if(o){let l=window.Helpers.isCollapsed()?"collapsed":"expanded";o.querySelector(`input[value="${l}"]`).click()}}catch{}})}),window.Helpers.swipeIn(".drag-target",function(e){window.Helpers.setCollapsed(!1)}),window.Helpers.swipeOut("#layout-menu",function(e){window.Helpers.isSmallScreen()&&window.Helpers.setCollapsed(!0)});let i=document.getElementsByClassName("menu-inner"),m=document.getElementsByClassName("menu-inner-shadow")[0];i.length>0&&m&&i[0].addEventListener("ps-scroll-y",function(){this.querySelector(".ps__thumb-y").offsetTop?m.style.display="block":m.style.display="none"});function f(e){e==="system"&&(window.matchMedia("(prefers-color-scheme: dark)").matches?e="dark":e="light"),[].slice.call(document.querySelectorAll("[data-app-"+e+"-img]")).map(function(o){const l=o.getAttribute("data-app-"+e+"-img");o.src=assetsPath+"img/"+l})}let p=document.querySelector(".dropdown-style-switcher");const h=document.documentElement.getAttribute("data-style");let w=localStorage.getItem("templateCustomizer-"+templateName+"--Style")||(((L=(S=window.templateCustomizer)==null?void 0:S.settings)==null?void 0:L.defaultStyle)??"light");//!if there is no Customizer then use default style as light
if(window.templateCustomizer&&p){[].slice.call(p.children[1].querySelectorAll(".dropdown-item")).forEach(function(o){o.classList.remove("active"),o.addEventListener("click",function(){let l=this.getAttribute("data-theme");l==="light"?window.templateCustomizer.setStyle("light"):l==="dark"?window.templateCustomizer.setStyle("dark"):window.templateCustomizer.setStyle("system")}),o.getAttribute("data-theme")===h&&o.classList.add("active")});const a=p.querySelector("i");w==="light"?(a.classList.add("ti-sun"),new bootstrap.Tooltip(a,{title:"Light Mode",fallbackPlacements:["bottom"]})):w==="dark"?(a.classList.add("ti-moon-stars"),new bootstrap.Tooltip(a,{title:"Dark Mode",fallbackPlacements:["bottom"]})):(a.classList.add("ti-device-desktop-analytics"),new bootstrap.Tooltip(a,{title:"System Mode",fallbackPlacements:["bottom"]}))}f(w);let s=document.getElementsByClassName("dropdown-language");if(s.length){let o=function(l){l==="rtl"?localStorage.getItem("templateCustomizer-"+templateName+"--Rtl")!=="true"&&window.templateCustomizer&&window.templateCustomizer.setRtl(!0):localStorage.getItem("templateCustomizer-"+templateName+"--Rtl")==="true"&&window.templateCustomizer&&window.templateCustomizer.setRtl(!1)};var k=o;let e=s[0].querySelectorAll(".dropdown-item");const a=s[0].querySelector(".dropdown-item.active");o(a.dataset.textDirection);for(let l=0;l<e.length;l++)e[l].addEventListener("click",function(){let x=this.getAttribute("data-text-direction");window.templateCustomizer.setLang(this.getAttribute("data-language")),o(x)})}setTimeout(function(){let e=document.querySelector(".template-customizer-reset-btn");e&&(e.onclick=function(){window.location.href=baseUrl+"lang/en"})},1500);const n=document.querySelector(".dropdown-notifications-all"),t=document.querySelectorAll(".dropdown-notifications-read");n&&n.addEventListener("click",e=>{t.forEach(a=>{a.closest(".dropdown-notifications-item").classList.add("marked-as-read")})}),t&&t.forEach(e=>{e.addEventListener("click",a=>{e.closest(".dropdown-notifications-item").classList.toggle("marked-as-read")})}),document.querySelectorAll(".dropdown-notifications-archive").forEach(e=>{e.addEventListener("click",a=>{e.closest(".dropdown-notifications-item").remove()})}),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bootstrap.Tooltip(e)});const u=function(e){e.type=="show.bs.collapse"||e.type=="show.bs.collapse"?e.target.closest(".accordion-item").classList.add("active"):e.target.closest(".accordion-item").classList.remove("active")};[].slice.call(document.querySelectorAll(".accordion")).map(function(e){e.addEventListener("show.bs.collapse",u),e.addEventListener("hide.bs.collapse",u)}),window.Helpers.setAutoUpdate(!0),window.Helpers.initPasswordToggle(),window.Helpers.initSpeechToText(),window.Helpers.initNavbarDropdownScrollbar();let g=document.querySelector("[data-template^='horizontal-menu']");if(g&&(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?window.Helpers.setNavbarFixed("fixed"):window.Helpers.setNavbarFixed("")),window.addEventListener("resize",function(e){window.innerWidth>=window.Helpers.LAYOUT_BREAKPOINT&&document.querySelector(".search-input-wrapper")&&(document.querySelector(".search-input-wrapper").classList.add("d-none"),document.querySelector(".search-input").value=""),g&&(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?window.Helpers.setNavbarFixed("fixed"):window.Helpers.setNavbarFixed(""),setTimeout(function(){window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT?document.getElementById("layout-menu")&&document.getElementById("layout-menu").classList.contains("menu-horizontal")&&y.switchMenu("vertical"):document.getElementById("layout-menu")&&document.getElementById("layout-menu").classList.contains("menu-vertical")&&y.switchMenu("horizontal")},100))},!0),!(v||window.Helpers.isSmallScreen())&&(typeof TemplateCustomizer<"u"&&(window.templateCustomizer.settings.defaultMenuCollapsed?window.Helpers.setCollapsed(!0,!1):window.Helpers.setCollapsed(!1,!1)),typeof config<"u"&&config.enableMenuLocalStorage))try{localStorage.getItem("templateCustomizer-"+templateName+"--LayoutCollapsed")!==null&&window.Helpers.setCollapsed(localStorage.getItem("templateCustomizer-"+templateName+"--LayoutCollapsed")==="true",!1)}catch{}})();typeof $<"u"&&$(function(){window.Helpers.initSidebarToggle();var b=$(".search-toggler"),r=$(".search-input-wrapper"),i=$(".search-input"),m=$(".content-backdrop");if(b.length&&b.on("click",function(){r.length&&(r.toggleClass("d-none"),i.focus())}),$(document).on("keydown",function(s){let n=s.ctrlKey,t=s.which===191;n&&t&&r.length&&(r.toggleClass("d-none"),i.focus())}),setTimeout(function(){var s=$(".twitter-typeahead");i.on("focus",function(){r.hasClass("container-xxl")?(r.find(s).addClass("container-xxl"),s.removeClass("container-fluid")):r.hasClass("container-fluid")&&(r.find(s).addClass("container-fluid"),s.removeClass("container-xxl"))})},10),i.length){var f=function(s){return function(t,c){let d;d=[],s.filter(function(u){if(u.name.toLowerCase().startsWith(t.toLowerCase()))d.push(u);else if(!u.name.toLowerCase().startsWith(t.toLowerCase())&&u.name.toLowerCase().includes(t.toLowerCase()))d.push(u),d.sort(function(C,g){return g.name<C.name?1:-1});else return[]}),c(d)}},p="search-vertical.json";if($("#layout-menu").hasClass("menu-horizontal"))var p="search-horizontal.json";var h=$.ajax({url:assetsPath+"json/"+p,dataType:"json",async:!1}).responseJSON;i.each(function(){var s=$(this);i.typeahead({hint:!1,classNames:{menu:"tt-menu navbar-search-suggestion",cursor:"active",suggestion:"suggestion d-flex justify-content-between px-4 py-2 w-100"}},{name:"pages",display:"name",limit:5,source:f(h.pages),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Pages</h6>',suggestion:function({url:n,icon:t,name:c}){return'<a href="'+baseUrl+n+'"><div><i class="ti '+t+' me-2"></i><span class="align-middle">'+c+"</span></div></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Pages</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}},{name:"files",display:"name",limit:4,source:f(h.files),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Files</h6>',suggestion:function({src:n,name:t,subtitle:c,meta:d}){return'<a href="javascript:;"><div class="d-flex w-50"><img class="me-3" src="'+assetsPath+n+'" alt="'+t+'" height="32"><div class="w-75"><h6 class="mb-0">'+t+'</h6><small class="text-muted">'+c+'</small></div></div><small class="text-muted">'+d+"</small></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Files</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}},{name:"members",display:"name",limit:4,source:f(h.members),templates:{header:'<h6 class="suggestions-header text-primary mb-0 mx-4 mt-3 pb-2">Members</h6>',suggestion:function({name:n,src:t,subtitle:c}){return'<a href="'+baseUrl+'app/user/view/account"><div class="d-flex align-items-center"><img class="rounded-circle me-3" src="'+assetsPath+t+'" alt="'+n+'" height="32"><div class="user-info"><h6 class="mb-0">'+n+'</h6><small class="text-muted">'+c+"</small></div></div></a>"},notFound:'<div class="not-found px-4 py-2"><h6 class="suggestions-header text-primary mb-2">Members</h6><p class="py-2 mb-0"><i class="ti ti-alert-circle ti-xs me-2"></i> No Results Found</p></div>'}}).bind("typeahead:render",function(){m.addClass("show").removeClass("fade")}).bind("typeahead:select",function(n,t){t.url!=="javascript:;"&&(window.location=baseUrl+t.url)}).bind("typeahead:close",function(){i.val(""),s.typeahead("val",""),r.addClass("d-none"),m.addClass("fade").removeClass("show")}),i.on("keyup",function(){i.val()==""&&m.addClass("fade").removeClass("show")})});var w;$(".navbar-search-suggestion").each(function(){w=new PerfectScrollbar($(this)[0],{wheelPropagation:!1,suppressScrollX:!0})}),i.on("keyup",function(){w.update()})}});
