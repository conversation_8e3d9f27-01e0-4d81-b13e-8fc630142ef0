<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SmsSimRental extends Model
{
    use HasFactory;

    protected $table = 'sms_sim_rentals';

    protected $fillable = [
        'sim_id',
        'phone_number',
        'server_id',
        'country_id',
        'service_id',
        'price',
        'status',
        'otp_code',
        'otp_received_at',
        'expires_at',
        'user_id',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'otp_received_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the server that owns this sim rental
     */
    public function server(): BelongsTo
    {
        return $this->belongsTo(SmsServer::class, 'server_id');
    }

    /**
     * Get the country that owns this sim rental
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(SmsCountry::class, 'country_id');
    }

    /**
     * Get the service that owns this sim rental
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(SmsService::class, 'service_id');
    }

    /**
     * Get the user that owns this sim rental
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active rentals
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for completed rentals
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for expired rentals
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Scope for waiting OTP rentals
     */
    public function scopeWaitingOtp($query)
    {
        return $query->where('status', 'waiting_otp');
    }

    /**
     * Check if rental is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Mark as completed with OTP
     */
    public function completeWithOtp(string $otpCode): bool
    {
        $this->update([
            'status' => 'completed',
            'otp_code' => $otpCode,
            'otp_received_at' => now(),
        ]);

        return true;
    }

    /**
     * Mark as expired
     */
    public function markAsExpired(): bool
    {
        $this->update(['status' => 'expired']);
        return true;
    }

    /**
     * Cancel the rental
     */
    public function cancel(): bool
    {
        $this->update(['status' => 'cancelled']);
        return true;
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'active' => 'bg-label-primary',
            'waiting_otp' => 'bg-label-warning',
            'completed' => 'bg-label-success',
            'expired' => 'bg-label-danger',
            'cancelled' => 'bg-label-secondary',
            default => 'bg-label-secondary',
        };
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'active' => 'Đang hoạt động',
            'waiting_otp' => 'Chờ OTP',
            'completed' => 'Hoàn thành',
            'expired' => 'Hết hạn',
            'cancelled' => 'Đã hủy',
            default => 'Không xác định',
        };
    }
}
