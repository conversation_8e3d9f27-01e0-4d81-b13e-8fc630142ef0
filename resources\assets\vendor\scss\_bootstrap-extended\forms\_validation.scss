// Validation states
// *******************************************************************************

@each $state, $data in $form-validation-states {
  @include template-form-validation-state($state, $data...);
}

// Currently supported for form-validation and jq-validation
form {
  .error:not(li):not(input) {
    color: $form-feedback-invalid-color;
    font-size: 85%;
    margin-top: 0.25rem;
  }

  .invalid,
  .is-invalid .invalid:before,
  .is-invalid::before {
    border-width: $input-focus-border-width;
    border-color: $form-feedback-invalid-color !important;
  }

  .form-label {
    &.invalid,
    &.is-invalid {
      border-width: $input-focus-border-width;
      border-color: $form-feedback-invalid-color;
      box-shadow: 0 0 0 2px rgba($form-feedback-invalid-color, 0.4) !important;
    }
  }

  select {
    &.invalid {
      & ~ .select2 {
        .select2-selection {
          border-width: $input-focus-border-width;
          border-color: $form-feedback-invalid-color;
        }
      }
    }

    // FormValidation

    //Select2
    &.is-invalid {
      & ~ .select2 {
        .select2-selection {
          border-width: $input-focus-border-width;
          border-color: $form-feedback-invalid-color !important;
        }
      }
    }
    // Bootstrap select
    &.selectpicker {
      &.is-invalid {
        ~ .btn {
          padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
          border-width: $input-focus-border-width;
          border-color: $form-feedback-invalid-color !important;
        }
      }
    }
  }
}

//!FIX: Input group form floating label .input-group-text border color on validation state
//? Can't use form-validation-state-selector mixin due to :has selector
.was-validated .input-group:has(.input-group-text):has(.form-control:invalid) .input-group-text,
.was-validated .input-group:has(.input-group-text):has(.form-control:valid) .input-group-text,
.input-group:has(.input-group-text):has(.form-control.is-valid) .input-group-text,
.input-group:has(.input-group-text):has(.form-control.is-invalid) .input-group-text {
  border-width: $input-focus-border-width;
}
//! FIX: Basic input (without floating) has shake effect due to padding
.was-validated .form-control:invalid,
.was-validated .form-control:valid,
.form-control.is-invalid,
.form-control.is-valid {
  padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
  ~ .input-group-text {
    padding: calc($input-padding-y - $input-focus-border-width) calc($input-padding-x - $input-border-width);
  }
}
.input-group {
  > :not(:first-child):not(.dropdown-menu):not(.btn):not(.dropdown-menu + .form-control):not(
      .btn + .form-control
    )#{$validation-messages}.form-control.is-invalid,
  > :not(:first-child):not(.dropdown-menu):not(.btn):not(.dropdown-menu + .form-control):not(
      .btn + .form-control
    )#{$validation-messages}.form-control.is-valid {
    margin-inline-start: calc($input-focus-border-width - 4px);
  }
  .was-validated & .form-control:invalid,
  .was-validated & .form-control:valid,
  .form-control.is-invalid,
  .form-control.is-valid,
  .form-select.is-invalid,
  .form-select.is-valid {
    &:first-child {
      padding-inline-start: calc($input-padding-x - $input-focus-border-width);
    }
  }
}
// ! Fix: FormValidation: Set border color to .form-control in touch devices for HTML5 inputs i.e date picker
@media (hover: none) {
  .fv-plugins-bootstrap5-row-invalid {
    .form-control {
      &.flatpickr-mobile {
        border-color: $form-feedback-invalid-color;
      }
    }
  }
}
// ! Fix: FormValidation: Validation error message display fix for those inputs where .invalid-feedback/tooltip is not a sibling element
.fv-plugins-bootstrap5 {
  .invalid-feedback,
  .invalid-tooltip {
    display: block;
  }
}

//! Fix: FormValidation: Tagify validation error (border color)
.fv-plugins-bootstrap5-row-invalid .tagify.tagify--empty {
  border-width: $input-focus-border-width;
  border-color: $form-feedback-invalid-color !important;
}
// ? Uncomment if required
// .fv-plugins-bootstrap5-row-valid .tagify:not(.tagify--empty) {
//   border-color: $form-feedback-valid-color;
// }
