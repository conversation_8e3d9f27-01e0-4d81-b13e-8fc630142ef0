<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use App\Services\HTMLMinifier;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class CacheGuestPages
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);
        // Kiểm tra cache có được bật không
        if (!Config::get('guest-cache.enabled', true)) {
            return $next($request);
        }
        // Chỉ cache cho guest users (chưa đăng nhập)
        if (Auth::check()) {
            return $next($request);
        }

        // Chỉ cache cho GET requests
        if (!$request->isMethod('GET')) {
            return $next($request);
        }

        // Kiểm tra excluded paths
        if ($this->isExcludedPath($request)) {
            return $next($request);
        }

        // Kiểm tra excluded user agents
        if ($this->isExcludedUserAgent($request)) {
            return $next($request);
        }

        // Bỏ qua các request có query parameters không được phép
        if ($this->hasUncacheableParams($request)) {
            return $next($request);
        }

        // Tạo cache key từ URL
        $cacheKey = $this->generateCacheKey($request);
        $cachePath = $this->getCachePath($cacheKey);

        // Kiểm tra cache có tồn tại và còn hạn không
        if ($this->isCacheValid($cachePath)) {
            $this->autoCleanup();
            return $this->serveCachedResponse($cachePath);
        }

        // Xử lý request và cache response
        $response = $next($request);

        // Chỉ cache response thành công và là HTML
        if ($this->shouldCacheResponse($response)) {
            $this->cacheResponse($response, $cachePath);
        }

        $this->autoCleanup();
        return $response;
    }

    /**
     * Check if path is excluded from caching
     */
    private function isExcludedPath(Request $request): bool
    {
        $path = $request->getPathInfo();
        $excludedPaths = Config::get('guest-cache.excluded_paths', []);

        // dd($excludedPaths-,$path);
        foreach ($excludedPaths as $excludedPath) {
            if (Str::is($excludedPath, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user agent is excluded from caching
     */
    private function isExcludedUserAgent(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');
        $excludedAgents = Config::get('guest-cache.excluded_user_agents', []);

        foreach ($excludedAgents as $excludedAgent) {
            if (Str::contains($userAgent, strtolower($excludedAgent))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if request has uncacheable parameters
     */
    private function hasUncacheableParams(Request $request): bool
    {
        $allowedParams = Config::get('guest-cache.allowed_query_params', []);
        $queryParams = array_keys($request->query());

        foreach ($queryParams as $param) {
            if (!in_array($param, $allowedParams)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate cache key from request
     */
    private function generateCacheKey(Request $request): string
    {
        $url = $request->getPathInfo();
        $query = $request->getQueryString();

        // Get theme and language from cookies/session
        $theme = $request->cookie('theme', 'light');
        $language = $request->cookie('locale', session('locale', app()->getLocale()));

        $key = $url;
        if ($query) {
            $key .= '?' . $query;
        }

        // Add theme and language to cache key
        $key .= '|theme:' . $theme . '|lang:' . $language;

        return md5($key);
    }

    /**
     * Get cache file path
     */
    private function getCachePath(string $cacheKey): string
    {
        $cacheDir = public_path(Config::get('guest-cache.directory', 'cache/guest-pages'));

        if (!File::exists($cacheDir)) {
            File::makeDirectory($cacheDir, 0755, true);
        }

        return $cacheDir . '/' . $cacheKey . '.html';
    }

    /**
     * Check if cache is valid
     */
    private function isCacheValid(string $cachePath): bool
    {
        if (!File::exists($cachePath)) {
            return false;
        }

        $cacheTime = File::lastModified($cachePath);
        $cacheDuration = Config::get('guest-cache.duration', 10);
        $expiryTime = $cacheTime + ($cacheDuration * 60);

        return time() < $expiryTime;
    }

    /**
     * Serve cached response
     */
    private function serveCachedResponse(string $cachePath): Response
    {
        $request = request();
        $acceptsGzip = $this->acceptsGzipEncoding($request);
        $compressionEnabled = Config::get('guest-cache.compression.enabled', false);

        // Try to serve compressed version if available and client accepts it
        $gzipPath = $cachePath . '.gz';
        if ($compressionEnabled && $acceptsGzip && File::exists($gzipPath) && $this->isCacheValid($gzipPath)) {
            $content = File::get($gzipPath);
            $isCompressed = true;
        } else {
            $content = File::get($cachePath);
            $isCompressed = false;
        }

        $cacheDuration = Config::get('guest-cache.duration', 10);
        $headers = Config::get('guest-cache.headers', []);

        $defaultHeaders = [
            'Content-Type' => 'text/html; charset=UTF-8',
            'X-Cache-Status' => 'HIT',
            'X-Cache-Time' => date('Y-m-d H:i:s', File::lastModified($cachePath)),
            'Cache-Control' => 'public, max-age=' . ($cacheDuration * 60),
            'Vary' => 'Accept-Encoding',
        ];

        if ($isCompressed) {
            $defaultHeaders['Content-Encoding'] = 'gzip';
        }

        if (Config::get('guest-cache.debug', false)) {
            $defaultHeaders['X-Cache-File'] = basename($cachePath);
            $defaultHeaders['X-Cache-Size'] = strlen($content);
            $defaultHeaders['X-Cache-Compressed'] = $isCompressed ? 'true' : 'false';
        }

        return response($content, 200, array_merge($defaultHeaders, $headers));
    }

    /**
     * Check if response should be cached
     */
    private function shouldCacheResponse($response): bool
    {
        // Chỉ cache response thành công
        if ($response->getStatusCode() !== 200) {
            return false;
        }

        // Chỉ cache HTML content
        $contentType = $response->headers->get('Content-Type', '');
        if (!Str::contains($contentType, 'text/html')) {
            return false;
        }

        return true;
    }

    /**
     * Cache the response
     */
    private function cacheResponse($response, string $cachePath): void
    {
        $content = $response->getContent();

        // Minify HTML
        $minifiedContent = $this->minifyHtml($content);

        // Compress content if enabled
        $compressionEnabled = Config::get('guest-cache.compression.enabled', false);
        if ($compressionEnabled && function_exists('gzencode')) {
            $compressedContent = gzencode($minifiedContent, Config::get('guest-cache.compression.level', 6));
            File::put($cachePath . '.gz', $compressedContent);
        }

        // Save to cache file
        File::put($cachePath, $minifiedContent);

        // Add cache headers to response
        $cacheDuration = Config::get('guest-cache.duration', 10);
        $response->headers->set('X-Cache-Status', 'MISS');
        $response->headers->set('Cache-Control', 'public, max-age=' . ($cacheDuration * 60));
        $response->headers->set('Vary', 'Accept-Encoding');
    }

    /**
     * Minify HTML content
     */
    private function minifyHtml(string $html): string
    {
        if (!Config::get('guest-cache.minification.enabled', true)) {
            return $html;
        }

        $options = Config::get('guest-cache.minification', []);
        return HTMLMinifier::minifyAdvanced($html, $options);
    }

    /**
     * Check if client accepts gzip encoding
     */
    private function acceptsGzipEncoding($request): bool
    {
        $acceptEncoding = $request->header('Accept-Encoding', '');
        return strpos(strtolower($acceptEncoding), 'gzip') !== false;
    }

    /**
     * Auto cleanup expired cache files
     */
    private function autoCleanup(): void
    {
        $autoCleanup = Config::get('guest-cache.auto_cleanup', []);

        if (!($autoCleanup['enabled'] ?? true)) {
            return;
        }

        $probability = $autoCleanup['probability'] ?? 2;

        // Random cleanup based on probability
        if (rand(1, 100) <= $probability) {
            $this->clearExpiredCache();
        }
    }

    /**
     * Clear expired cache files
     */
    public static function clearExpiredCache(): int
    {
        $cacheDir = public_path(Config::get('guest-cache.directory', 'cache/guest-pages'));
        $cleared = 0;

        if (!File::exists($cacheDir)) {
            return $cleared;
        }

        $files = File::files($cacheDir);
        $cacheDuration = Config::get('guest-cache.duration', 10);
        $expiryThreshold = time() - ($cacheDuration * 60);
        $maxFiles = Config::get('guest-cache.auto_cleanup.max_files_per_cleanup', 100);

        foreach ($files as $file) {
            if ($cleared >= $maxFiles) {
                break;
            }

            if ($file->getMTime() < $expiryThreshold) {
                File::delete($file->getPathname());

                // Also delete compressed version if exists
                $gzipPath = $file->getPathname() . '.gz';
                if (File::exists($gzipPath)) {
                    File::delete($gzipPath);
                }

                $cleared++;
            }
        }

        return $cleared;
    }

    /**
     * Clear all cache files
     */
    public static function clearAllCache(): int
    {
        $cacheDir = public_path(Config::get('guest-cache.directory', 'cache/guest-pages'));
        $cleared = 0;

        if (!File::exists($cacheDir)) {
            return $cleared;
        }

        $files = File::files($cacheDir);

        foreach ($files as $file) {
            File::delete($file->getPathname());

            // Also delete compressed version if exists
            $gzipPath = $file->getPathname() . '.gz';
            if (File::exists($gzipPath)) {
                File::delete($gzipPath);
            }

            $cleared++;
        }

        return $cleared;
    }
}
