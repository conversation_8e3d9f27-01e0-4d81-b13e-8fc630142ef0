<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Artisan::call('db:wipe');
        Artisan::call('migrate');
        User::create([
            'username' => 'phuongnv.cvds',
            'email' => '<EMAIL>',
            'password' => bcrypt('phuongnv.cvds'),
            'role' => 'admin',
            'balance' => 1333333444
        ]);
        $this->call(SoftwareSeeder::class);
        $this->call(SimRentalSeeder::class);
    }
}
