
import 'toastr/build/toastr.min.css';
import toastr from "toastr";
// function showSuccess(message) {
//   Swal.fire({
//     icon: 'success',
//     title: message,
//     showConfirmButton: false,
//     timer: 3888
//   });
// }

// export function showError(message) {
//   Swal.fire({
//     icon: 'error',
//     title: message,
//     showConfirmButton: false,
//     timer: 3888
//   });
// }

// export function showWarning(message) {
//   Swal.fire({
//     icon: 'warning',
//     title: message,
//     showConfirmButton: false,
//     timer: 3888
//   });
// }
export function notifySuccess(message) {
  toastr.options = {
    closeButton: true,
    progressBar: true,
    timeOut: 5000,
    extendedTimeOut: 5000
  };
  toastr.success(message, 'Thông báo');
}
export function notifyWarning(message) {
  toastr.options = {
    closeButton: true,
    progressBar: true,
    timeOut: 5000,
    extendedTimeOut: 5000
  };
  toastr.warning(message, 'Thông báo');
}
export function notifyError(message) {
  console.log(message, 'message');
  toastr.options = {
    closeButton: true,
    progressBar: true,
    timeOut: 5000,
    extendedTimeOut: 5000
  };
  toastr.error(message, 'Thông báo');
}

// Add this CSS somewhere in your project to make the title smaller:
// .swal2-smaller-title { font-size: 1rem !important; }

// window.showSuccess = showSuccess;
// window.showError = showError;
// window.showWarning = showWarning;
window.notifyWarning = notifyWarning;
window.notifySuccess = notifySuccess;
window.notifyError = notifyError;




