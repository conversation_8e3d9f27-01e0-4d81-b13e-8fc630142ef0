@import '../../scss/_bootstrap-extended/functions';

@mixin bs-datepicker-theme($background, $color: null) {
  $color: if($color, $color, color-contrast($background));
  $range-bg: rgba-to-hex(rgba($background, 0.16), $card-bg);
  $range-color: $background;

  .datepicker {
    table {
      tr td {
        &.active,
        &.active.highlighted,
        .focused,
        span.active,
        span.active.disabled,
        &.range-start,
        &.range-end {
          background: $background !important;
          color: $color !important;
          box-shadow: 0 0.125rem 0.375rem 0 rgba($background, 0.3);
        }

        &.range,
        &.range.highlighted,
        &.range.today {
          color: $range-color !important;
          background: $range-bg !important;

          &.focused {
            background: rgba-to-hex(rgba($background, 0.24), $card-bg) !important;
          }

          &.disabled {
            background: transparentize($range-bg, 0.5) !important;
            color: transparentize($range-color, 0.5) !important;
          }
        }

        &.today:not(.active),
        &.today:not(.active):hover {
          color: $background;
          background-color: rgba-to-hex(rgba($background, 0.16), $card-bg);
        }
      }
    }
  }
}

@mixin bs-datepicker-dark-theme($background, $color: null) {
  $color: if($color, $color, color-contrast($background));
  $range-bg: rgba-to-hex(rgba($background, 0.24), $card-bg);
  $range-color: $background;

  .datepicker {
    table {
      tr td {
        &.active,
        &.active.highlighted,
        .focused,
        span.active,
        span.active.disabled,
        &.range-start,
        &.range-end {
          color: $color !important;
          background: $background !important;
          box-shadow: 0 0.125rem 0.375rem 0 rgba($background, 0.3);
        }

        &.range,
        &.range.highlighted,
        &.range.today {
          color: $range-color !important;
          background: $range-bg !important;

          &.disabled {
            color: transparentize($range-color, 0.5) !important;
            background: transparentize($range-bg, 0.5) !important;
          }

          &.focused {
            background: rgba-to-hex(rgba($background, 0.24), $card-bg) !important;
          }
        }

        &.today:not(.active),
        &.today:not(.active):hover {
          color: $background;
          background-color: rgba-to-hex(rgba($background, 0.16), $card-bg);
        }
      }
    }
  }
}
