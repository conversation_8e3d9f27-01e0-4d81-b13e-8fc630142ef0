@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Hotmail Accounts - Premium Email Services')

@section('seo-meta')
    <x-seo-meta page="hotmail" :data="['hotmails' => $hotmails ?? []]" />
@endsection

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('page-style')
    {{-- Critical CSS for above-the-fold content --}}
    {{-- <x-critical-css page="hotmail" /> --}}

    {{-- Preload critical images for LCP optimization --}}
    @if(isset($hotmails) && count($hotmails) > 0)
        @foreach(array_slice($hotmails, 0, 3) as $index => $hotmail)
            <link rel="preload" as="image" href="{{ $hotmail['Image'] }}" fetchpriority="{{ $index === 0 ? 'high' : 'low' }}">
        @endforeach
    @endif
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/select2/select2.js'])
@endsection

@section('page-script')
    @vite(['resources/assets/js/notify.js'])
    @vite(['resources/assets/js/card/cards-advance.js'])
    @vite(['resources/assets/js/hotmail-index.js'])
@endsection

@section('content')
    <style>
        .img-tool {
            width: 100%;
            height: 30vh;
            object-fit: cover;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <div class="row">
        <form method="GET" action="{{ route('hotmail-index') }}" class="mb-4">
            <div class="row justify-content-center align-items-center">
                <div class="col-12 col-md-4 mb-2 mb-md-0">
                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}"
                        placeholder="Search Hotmail...">
                </div>
                <div class="col-12 col-md-6 mb-2 mb-md-0">
                    <select name="tag[]" id="tag[]" class="select2 form-select" multiple>
                        @foreach ($tags as $tag)
                            <option value="{{ $tag }}" {{ in_array($tag, request('tag', [])) ? 'selected' : '' }}>
                                {{ $tag }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-12 col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> {{ __('messages.filter') }}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="row">
        <!-- Hotmail Card -->
        @foreach ($hotmails as $hotmail)
            <div class="col-md-6 col-xxl-4 mb-6">
                <div class="card h-100">
                    <div class="card-body p-2 pb-0">
                        <div class="bg-label-primary rounded text-center mb-2 position-relative">
                            <span class="badge badge-free position-absolute bg-warning fs-6 p-3"
                                style="top: 10px; left: 10px; z-index: 2;">{{ number_format($hotmail['ViPrice']) }}đ</span>
                            @if ($hotmail['Instock'] == 0)
                                <span class="badge bg-danger position-absolute fs-6 p-3"
                                    style="top: 10px; right: 10px; z-index: 2;">{{ __('messages.instock') }}:
                                    {{ $hotmail['Instock'] }}</span>
                            @else
                                <span class="badge bg-success position-absolute fs-6 p-3"
                                    style="top: 10px; right: 10px; z-index: 2;">{{ __('messages.instock') }}:
                                    {{ $hotmail['Instock'] }}</span>
                            @endif
                            <x-responsive-image
                                :src="$hotmail['Image']"
                                alt="Hotmail image"
                                class="img-fluid img-tool"
                                width="140"
                                height="200"
                                :critical="$loop->index < 3"
                                sizes="(max-width: 576px) 140px, (max-width: 768px) 140px, 140px" />
                            <i class="bi bi-heart-fill text-danger icon-overlay"></i>
                        </div>
                        <h5 class="mb-1">{{ $hotmail['Name'] }}</h5>
                    </div>
                    <div class="card-footer p-2 pt-0">
                        <div class="demo-inline-spacing pb-0 m-0">
                            @foreach ($hotmail['Tags'] as $tag)
                                <span class="badge rounded-pill m-0 mb-1"
                                    style="background-color: #666;">{{ $tag }}</span>
                            @endforeach
                        </div>
                        <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal"
                            data-bs-target="#modalPurchase" data-type="{{ $hotmail['AccountCode'] }}"
                            data-name="{{ $hotmail['Name'] }}" data-price="{{ $hotmail['ViPrice'] }}"
                            @if (!$hotmail['Instock']) disabled @endif>
                            {{ __('messages.buy') }}
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
        <!--/ Hotmail Card -->
    </div>

    <!-- Modal purchase -->
    <div class="modal fade" id="modalPurchase" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCenterTitle">{{ __('messages.order_payment') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="price" value="">
                    <input type="hidden" id="type" value="">
                    <div class="row">
                        <div class="col mb-4">
                            <label for="name" class="form-label">{{ __('messages.product_name') }}:</label>
                            <input type="text" id="name" class="form-control" disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col mb-4">
                            <label for="quantity" class="form-label">{{ __('messages.product_quantity_buy') }}:</label>
                            <input type="number" id="quantity" class="form-control" placeholder="Enter Quantity"
                                min="1">
                        </div>
                    </div>
                    <div class="onboarding-content mb-0">
                        <h5 class="onboarding-title text-body text-center">{{ __('messages.total_need_pay') }}: <span
                                class="text-danger" id="total"></span></h5>
                    </div>
                    <div class="demo-inline-spacing text-center d-none" id="loading">
                        <div class="spinner-border spinner-border-lg text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="spinner-border spinner-border-lg text-secondary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="spinner-border spinner-border-lg text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    @guest
                        <a href="{{ route('auth-login') }}"
                            class="btn btn-warning">{{ __('messages.login_to_continue') }}</a>
                    @else
                        <button type="submit" id="purchase" class="btn btn-primary">
                            {{ __('messages.confirm_purchase') }}

                        </button>
                    @endguest
                </div>
            </div>
        </div>
    </div>

    <!-- Modal purchase -->
    <div class="modal fade" id="modalDataPurchase" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header d-flex justify-content-between align-items-center">
                    <h5 class="modal-title" id="modalCenterTitle">{{ __('messages.order_result') }}</h5>
                    <div class="d-flex flex-column flex-sm-row align-items-center ms-sm-auto gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="downloadTxt">
                            <i class="ti ti-download ti-md"></i> TXT
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" id="downloadJson">
                            <i class="ti ti-download ti-md"></i> JSON
                        </button>
                        <button type="button" class="btn-close ms-sm-2" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <textarea name="" id="dataPurchase" rows="30" class="form-control w-100"></textarea>
                </div>
            </div>
        </div>
    </div>
@endsection
