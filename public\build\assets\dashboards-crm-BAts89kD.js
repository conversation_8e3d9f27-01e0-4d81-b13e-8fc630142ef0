(function(){let e,c,p,f,a,t;isDarkStyle?(config.colors_dark.cardColor,e=config.colors_dark.textMuted,f=config.colors_dark.bodyColor,a=config.colors_dark.borderColor,c=config.colors_dark.headingColor,t="#3d4157",p="dark"):(config.colors.cardColor,e=config.colors.textMuted,f=config.colors.bodyColor,a=config.colors.borderColor,c=config.colors.headingColor,t="#efeef0",p="");const h=document.querySelector("#ordersLastWeek"),R={chart:{height:75,parentHeightOffset:0,type:"bar",toolbar:{show:!1}},tooltip:{enabled:!1},plotOptions:{bar:{barHeight:"100%",columnWidth:"30px",startingShape:"rounded",endingShape:"rounded",borderRadius:4,colors:{backgroundBarColors:[t,t,t,t,t,t,t],backgroundBarRadius:4}}},colors:[config.colors.primary],grid:{show:!1,padding:{top:-30,left:-16,bottom:0,right:-6}},dataLabels:{enabled:!1},series:[{data:[60,50,20,45,50,30,70]}],legend:{show:!1},xaxis:{categories:["M","T","W","T","F","S","S"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{show:!1}},yaxis:{labels:{show:!1}},responsive:[{breakpoint:1441,options:{plotOptions:{bar:{columnWidth:"40%",borderRadius:4}}}},{breakpoint:1368,options:{plotOptions:{bar:{columnWidth:"48%"}}}},{breakpoint:1200,options:{plotOptions:{bar:{borderRadius:6,columnWidth:"30%",colors:{backgroundBarRadius:6}}}}},{breakpoint:991,options:{plotOptions:{bar:{columnWidth:"35%",borderRadius:6}}}},{breakpoint:883,options:{plotOptions:{bar:{columnWidth:"40%"}}}},{breakpoint:768,options:{plotOptions:{bar:{columnWidth:"25%"}}}},{breakpoint:576,options:{plotOptions:{bar:{borderRadius:9},colors:{backgroundBarRadius:9}}}},{breakpoint:479,options:{plotOptions:{bar:{borderRadius:4,columnWidth:"35%"},colors:{backgroundBarRadius:4}},grid:{padding:{right:-15,left:-15}}}},{breakpoint:376,options:{plotOptions:{bar:{borderRadius:3}}}}]};typeof h!==void 0&&h!==null&&new ApexCharts(h,R).render();const b=document.querySelector("#salesLastYear"),O={chart:{height:75,type:"area",parentHeightOffset:0,toolbar:{show:!1},sparkline:{enabled:!0}},markers:{colors:"transparent",strokeColors:"transparent"},grid:{show:!1},colors:[config.colors.success],fill:{type:"gradient",gradient:{shade:p,shadeIntensity:.8,opacityFrom:.6,opacityTo:.25}},dataLabels:{enabled:!1},stroke:{width:2,curve:"smooth"},series:[{data:[200,55,400,250]}],xaxis:{show:!0,lines:{show:!1},labels:{show:!1},stroke:{width:0},axisBorder:{show:!1}},yaxis:{stroke:{width:0},show:!1},tooltip:{enabled:!1}};typeof b!==void 0&&b!==null&&new ApexCharts(b,O).render();const u=document.querySelector("#revenueGrowth"),T={chart:{height:170,type:"bar",parentHeightOffset:0,toolbar:{show:!1}},plotOptions:{bar:{barHeight:"80%",columnWidth:"30%",startingShape:"rounded",endingShape:"rounded",borderRadius:6,distributed:!0}},tooltip:{enabled:!1},grid:{show:!1,padding:{top:-20,bottom:-12,left:-10,right:0}},colors:[config.colors_label.success,config.colors_label.success,config.colors_label.success,config.colors_label.success,config.colors.success,config.colors_label.success,config.colors_label.success],dataLabels:{enabled:!1},series:[{data:[25,40,55,70,85,70,55]}],legend:{show:!1},xaxis:{categories:["M","T","W","T","F","S","S"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:e,fontSize:"13px",fontFamily:"Public Sans"}}},yaxis:{labels:{show:!1}},states:{hover:{filter:{type:"none"}}},responsive:[{breakpoint:1471,options:{plotOptions:{bar:{columnWidth:"50%"}}}},{breakpoint:1350,options:{plotOptions:{bar:{columnWidth:"57%"}}}},{breakpoint:1032,options:{plotOptions:{bar:{columnWidth:"60%"}}}},{breakpoint:992,options:{plotOptions:{bar:{columnWidth:"40%",borderRadius:8}}}},{breakpoint:855,options:{plotOptions:{bar:{columnWidth:"50%",borderRadius:6}}}},{breakpoint:440,options:{plotOptions:{bar:{columnWidth:"40%"}}}},{breakpoint:381,options:{plotOptions:{bar:{columnWidth:"45%"}}}}]};typeof u!==void 0&&u!==null&&new ApexCharts(u,T).render();function i(o,n){const l=config.colors_label.primary,C=config.colors.primary;var d=[];for(let r=0;r<o.length;r++)r===n?d.push(C):d.push(l);return{chart:{height:231,parentHeightOffset:0,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{columnWidth:"32%",startingShape:"rounded",borderRadius:6,distributed:!0,dataLabels:{position:"top"}}},grid:{show:!1,padding:{top:0,bottom:0,left:-10,right:-10}},colors:d,dataLabels:{enabled:!0,formatter:function(r){return r+"k"},offsetY:-30,style:{fontSize:"15px",colors:[c],fontWeight:"500",fontFamily:"Public Sans"}},series:[{data:o}],legend:{show:!1},tooltip:{enabled:!1},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep"],axisBorder:{show:!0,color:a},axisTicks:{show:!1},labels:{style:{colors:e,fontSize:"13px",fontFamily:"Public Sans"}}},yaxis:{labels:{offsetX:-15,formatter:function(r){return parseInt(r/1)+"k"},style:{fontSize:"13px",colors:e,fontFamily:"Public Sans"},min:0,max:6e4,tickAmount:6}},responsive:[{breakpoint:1441,options:{plotOptions:{bar:{columnWidth:"41%"}}}},{breakpoint:590,options:{plotOptions:{bar:{columnWidth:"61%",borderRadius:5}},yaxis:{labels:{show:!1}},grid:{padding:{right:0,left:-20}},dataLabels:{style:{fontSize:"12px",fontWeight:"400"}}}}]}}var W="earning-reports-charts.json",s=$.ajax({url:assetsPath+"json/"+W,dataType:"json",async:!1}).responseJSON;const g=document.querySelector("#earningReportsTabsOrders"),L=i(s.data[0].chart_data,s.data[0].active_option);typeof g!==void 0&&g!==null&&new ApexCharts(g,L).render();const m=document.querySelector("#earningReportsTabsSales"),_=i(s.data[1].chart_data,s.data[1].active_option);typeof m!==void 0&&m!==null&&new ApexCharts(m,_).render();const y=document.querySelector("#earningReportsTabsProfit"),v=i(s.data[2].chart_data,s.data[2].active_option);typeof y!==void 0&&y!==null&&new ApexCharts(y,v).render();const w=document.querySelector("#earningReportsTabsIncome"),B=i(s.data[3].chart_data,s.data[3].active_option);typeof w!==void 0&&w!==null&&new ApexCharts(w,B).render();const k=document.querySelector("#salesLastMonth"),A={series:[{name:"Sales",data:[32,27,27,30,25,25]},{name:"Visits",data:[25,35,20,20,20,20]}],chart:{height:340,type:"radar",toolbar:{show:!1}},plotOptions:{radar:{polygons:{strokeColors:a,connectorColors:a}}},stroke:{show:!1,width:0},legend:{show:!0,fontSize:"13px",position:"bottom",labels:{colors:f,useSeriesColors:!1},markers:{height:12,width:12,offsetX:-5},itemMargin:{horizontal:10},onItemHover:{highlightDataSeries:!1}},colors:[config.colors.primary,config.colors.info],fill:{opacity:[1,.85]},markers:{size:0},grid:{show:!1,padding:{top:0,bottom:-5}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"],labels:{show:!0,style:{colors:[e,e,e,e,e,e],fontSize:"13px",fontFamily:"Public Sans"}}},yaxis:{show:!1,min:0,max:40,tickAmount:4},responsive:[{breakpoint:769,options:{chart:{height:400}}}]};typeof k!==void 0&&k!==null&&new ApexCharts(k,A).render();function M(o,n){return{chart:{height:48,width:38,type:"radialBar"},plotOptions:{radialBar:{hollow:{size:"25%"},dataLabels:{show:!1},track:{background:config.colors_label.secondary}}},stroke:{lineCap:"round"},colors:[o],grid:{padding:{top:-15,bottom:-15,left:-5,right:-15}},series:[n],labels:["Progress"]}}const S=document.querySelectorAll(".chart-progress");S&&S.forEach(function(o){const n=config.colors[o.dataset.color],l=o.dataset.series,C=M(n,l);new ApexCharts(o,C).render()});const x=document.querySelector("#projectStatusChart"),F={chart:{height:230,type:"area",toolbar:!1},markers:{strokeColor:"transparent"},series:[{data:[2e3,2e3,4e3,4e3,3050,3050,2e3,2e3,3050,3050,4700,4700,2750,2750,5700,5700]}],dataLabels:{enabled:!1},grid:{show:!1,padding:{left:-10,right:-5}},stroke:{width:3,curve:"straight"},colors:[config.colors.warning],fill:{type:"gradient",gradient:{opacityFrom:.6,opacityTo:.15,stops:[0,95,100]}},xaxis:{labels:{show:!1},axisBorder:{show:!1},axisTicks:{show:!1},lines:{show:!1}},yaxis:{labels:{show:!1},min:1e3,max:6e3,tickAmount:5},tooltip:{enabled:!1}};typeof x!==void 0&&x!==null&&new ApexCharts(x,F).render()})();
