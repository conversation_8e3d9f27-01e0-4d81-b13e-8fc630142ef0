// Pagination
// *******************************************************************************

// Pagination Mixins
@each $color, $value in $theme-colors {
  @if $color != primary and $color != light {
    @include template-pagination-variant('.pagination-#{$color}', $value);
    @include template-pagination-outline-variant('.pagination-outline-#{$color}', $value);
  }
}
// Pagination next, prev, first & last css padding
.page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding: $pagination-padding-y - 0.043rem $pagination-padding-x - 0.067rem;
    }
  }
  &.disabled,
  &[disabled] {
    .page-link {
      pointer-events: none;
    }
  }
}
.pagination {
  &.disabled,
  &[disabled] {
    .page-item .page-link {
      opacity: $pagination-disabled-opacity;
    }
  }
}

// Pagination basic style
.page-link,
.page-link > a {
  @include border-radius($border-radius);

  text-align: center;
  min-width: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-x * 1.923)} + calc(#{$pagination-border-width} * 2)'}
  );
  min-height: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-y * 2)} + calc(#{$pagination-border-width} * 2)'}
  );

  &:focus {
    color: $pagination-hover-color;
  }
  display: inline-flex !important;
  justify-content: center;
  align-items: center;
}

// Add spacing between pagination items
.page-item + .page-item .page-link,
.pagination li + li > a:not(.page-link) {
  .pagination-sm & {
    margin-left: 0.25rem;
  }
  .pagination-lg & {
    margin-left: 0.5rem;
  }
}

// Removed border from default pagination and set line-height of icons
.pagination {
  &:not([class*='pagination-outline-']) {
    .page-link {
      border-color: transparent;
    }
    & .page-item.active .waves-ripple {
      background: none;
    }
  }
  &[class*='pagination-outline-'] {
    .page-item.active .page-link {
      box-shadow: none;
    }
    .page-item:not(.active) .page-link,
    .pagination li > a:not(.page-link) {
      background-color: transparent;
      &:hover,
      &:focus {
        background-color: rgba-to-hex(rgba($black, 0.06), $rgba-to-hex-bg);
        border-color: rgba-to-hex(rgba($black, 0.22), $rgba-to-hex-bg);
        color: $headings-color;
      }
      &.waves-effect {
        .waves-ripple {
          background: radial-gradient(
            rgba($black, 0.3) 0,
            rgba($black, 0.4) 40%,
            rgba($black, 0.5) 50%,
            rgba($black, 0.6) 60%,
            rgba($black, 0) 70%
          );
        }
      }
    }
  }
}

.page-link.btn-primary {
  box-shadow: none !important;
}

// Pagination shape rounded & Square
.pagination {
  &.pagination-square .page-item a {
    @include border-radius(0);
  }
  &.pagination-round .page-item a {
    @include border-radius(50%);
  }
  &.pagination-rounded .page-item a {
    @include border-radius($border-radius);
  }
  &.pagination-sm.pagination-rounded .page-item a {
    @include border-radius($border-radius-sm);
  }
  &.pagination-lg.pagination-rounded .page-item a {
    @include border-radius($border-radius-lg);
  }
}

// Sizing
// *******************************************************************************

// Pagination Large
.pagination-lg .page-link,
.pagination-lg > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-x-lg * 1.615)} + calc(#{$pagination-border-width} * 2)'}
  );
  min-height: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-y-lg * 2.33)} + calc(#{$pagination-border-width} * 2)'}
  );
}

// Pagination Small
.pagination-sm .page-link,
.pagination-sm > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-sm * $pagination-line-height) + ($pagination-padding-x-sm * 2.356)} + calc(#{$pagination-border-width} * 2)'}
  );
  min-height: calc(
    #{'#{($font-size-sm * $pagination-line-height) + ($pagination-padding-y-sm * 2)} + calc(#{$pagination-border-width} * 2)'}
  );
}
.pagination-sm > .page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding: $pagination-padding-y-sm - 0.1055rem;
    }
  }
}

// RTL pagination
// *******************************************************************************

@include rtl-only {
  .pagination {
    padding-right: 0;
  }

  // Add spacing between pagination items
  .page-item + .page-item .page-link,
  .pagination li + li > a:not(.page-link) {
    margin-left: 0;
    margin-right: $pagination-margin-start;
    .pagination-sm & {
      margin-right: 0.25rem;
    }
    .pagination-lg & {
      margin-right: 0.5rem;
    }
  }

  .page-item {
    &.first,
    &.last,
    &.next,
    &.prev,
    &.previous {
      .page-link {
        i {
          transform: rotate(180deg);
        }
      }
    }
  }
}

@include dark-layout-only {
  .pagination[class*='pagination-outline-'] {
    .page-item .page-link,
    .pagination li > a:not(.page-link) {
      background-color: transparent;
      &:hover,
      &:focus {
        background-color: rgba-to-hex(rgba($base, 0.06), $rgba-to-hex-bg);
        border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg);
      }
      &.waves-effect {
        .waves-ripple {
          background: radial-gradient(
            rgba(#000, 0.3) 0,
            rgba(#000, 0.4) 40%,
            rgba(#000, 0.5) 50%,
            rgba(#000, 0.6) 60%,
            rgba(#000, 0) 70%
          );
        }
      }
    }
  }
}
