<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Software extends Model
{
    protected $fillable = [
        'name',
        'url',
        'en_name',
        'description',
        'en_description',
        'video',
        'image',
        'is_free',
        'price_1_m',
        'price_3_m',
        'price_6_m',
        'price_1_y',
        'price_lifetime',
        'price_code',
        'status',
    ];

    protected $casts = [
        // 'tags' => 'array',
    ];

    /**
     * Relationship với Tag model (many-to-many)
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'software_tags');
    }
}
