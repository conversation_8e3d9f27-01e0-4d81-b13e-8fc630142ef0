<?php

namespace App\Http\Controllers\authentications;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LoginBasic extends Controller
{
    public function index()
    {
        $pageConfigs = ['myLayout' => 'blank'];
        return view('content.authentications.auth-login-basic', ['pageConfigs' => $pageConfigs]);
    }

    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required'
        ]);
        $credentials = $request->only('username', 'password');
        $remember = $request->has('remember');
        if (!auth()->attempt($credentials, $remember)) {
            return redirect()->back()->with('error', __('messages.invalid_credentials'))->withInput($request->only('username'));
        }
        $request->session()->regenerate();
        return redirect()->route('software-all')->with('success', __('messages.login_successful'));
    }
}
