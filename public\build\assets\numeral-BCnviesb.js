import{c as O,g as j}from"./_commonjsHelpers-BosuxZz1.js";var T={exports:{}};/*! @preserve
 * numeral.js
 * version : 2.0.6
 * author : <PERSON>
 * license : MIT
 * http://adamwdraper.github.com/Numeral-js/
 */var S;function L(){return S||(S=1,function(B){(function(n,s){B.exports?B.exports=s():n.numeral=s()})(O,function(){var n,s,c="2.0.6",y={},F={},_={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},b={currentLocale:_.currentLocale,zeroFormat:_.zeroFormat,nullFormat:_.nullFormat,defaultFormat:_.defaultFormat,scalePercentBy100:_.scalePercentBy100};function M(i,e){this._input=i,this._value=e}return n=function(i){var e,l,t,r;if(n.isNumeral(i))e=i.value();else if(i===0||typeof i>"u")e=0;else if(i===null||s.isNaN(i))e=null;else if(typeof i=="string")if(b.zeroFormat&&i===b.zeroFormat)e=0;else if(b.nullFormat&&i===b.nullFormat||!i.replace(/[^0-9]+/g,"").length)e=null;else{for(l in y)if(r=typeof y[l].regexps.unformat=="function"?y[l].regexps.unformat():y[l].regexps.unformat,r&&i.match(r)){t=y[l].unformat;break}t=t||n._.stringToNumber,e=t(i)}else e=Number(i)||null;return new M(i,e)},n.version=c,n.isNumeral=function(i){return i instanceof M},n._=s={numberToFormat:function(i,e,l){var t=F[n.options.currentLocale],r=!1,o=!1,a=0,u="",f=1e12,d=1e9,h=1e6,k=1e3,p="",N=!1,g,w,m,v,$,E,x;if(i=i||0,w=Math.abs(i),n._.includes(e,"(")?(r=!0,e=e.replace(/[\(|\)]/g,"")):(n._.includes(e,"+")||n._.includes(e,"-"))&&($=n._.includes(e,"+")?e.indexOf("+"):i<0?e.indexOf("-"):-1,e=e.replace(/[\+|\-]/g,"")),n._.includes(e,"a")&&(g=e.match(/a(k|m|b|t)?/),g=g?g[1]:!1,n._.includes(e," a")&&(u=" "),e=e.replace(new RegExp(u+"a[kmbt]?"),""),w>=f&&!g||g==="t"?(u+=t.abbreviations.trillion,i=i/f):w<f&&w>=d&&!g||g==="b"?(u+=t.abbreviations.billion,i=i/d):w<d&&w>=h&&!g||g==="m"?(u+=t.abbreviations.million,i=i/h):(w<h&&w>=k&&!g||g==="k")&&(u+=t.abbreviations.thousand,i=i/k)),n._.includes(e,"[.]")&&(o=!0,e=e.replace("[.]",".")),m=i.toString().split(".")[0],v=e.split(".")[1],E=e.indexOf(","),a=(e.split(".")[0].split(",")[0].match(/0/g)||[]).length,v?(n._.includes(v,"[")?(v=v.replace("]",""),v=v.split("["),p=n._.toFixed(i,v[0].length+v[1].length,l,v[1].length)):p=n._.toFixed(i,v.length,l),m=p.split(".")[0],n._.includes(p,".")?p=t.delimiters.decimal+p.split(".")[1]:p="",o&&Number(p.slice(1))===0&&(p="")):m=n._.toFixed(i,0,l),u&&!g&&Number(m)>=1e3&&u!==t.abbreviations.trillion)switch(m=String(Number(m)/1e3),u){case t.abbreviations.thousand:u=t.abbreviations.million;break;case t.abbreviations.million:u=t.abbreviations.billion;break;case t.abbreviations.billion:u=t.abbreviations.trillion;break}if(n._.includes(m,"-")&&(m=m.slice(1),N=!0),m.length<a)for(var P=a-m.length;P>0;P--)m="0"+m;return E>-1&&(m=m.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+t.delimiters.thousands)),e.indexOf(".")===0&&(m=""),x=m+p+(u||""),r?x=(r&&N?"(":"")+x+(r&&N?")":""):$>=0?x=$===0?(N?"-":"+")+x:x+(N?"-":"+"):N&&(x="-"+x),x},stringToNumber:function(i){var e=F[b.currentLocale],l=i,t={thousand:3,million:6,billion:9,trillion:12},r,o,a;if(b.zeroFormat&&i===b.zeroFormat)o=0;else if(b.nullFormat&&i===b.nullFormat||!i.replace(/[^0-9]+/g,"").length)o=null;else{o=1,e.delimiters.decimal!=="."&&(i=i.replace(/\./g,"").replace(e.delimiters.decimal,"."));for(r in t)if(a=new RegExp("[^a-zA-Z]"+e.abbreviations[r]+"(?:\\)|(\\"+e.currency.symbol+")?(?:\\))?)?$"),l.match(a)){o*=Math.pow(10,t[r]);break}o*=(i.split("-").length+Math.min(i.split("(").length-1,i.split(")").length-1))%2?1:-1,i=i.replace(/[^0-9\.]+/g,""),o*=Number(i)}return o},isNaN:function(i){return typeof i=="number"&&isNaN(i)},includes:function(i,e){return i.indexOf(e)!==-1},insert:function(i,e,l){return i.slice(0,l)+e+i.slice(l)},reduce:function(i,e){if(this===null)throw new TypeError("Array.prototype.reduce called on null or undefined");if(typeof e!="function")throw new TypeError(e+" is not a function");var l=Object(i),t=l.length>>>0,r=0,o;if(arguments.length===3)o=arguments[2];else{for(;r<t&&!(r in l);)r++;if(r>=t)throw new TypeError("Reduce of empty array with no initial value");o=l[r++]}for(;r<t;r++)r in l&&(o=e(o,l[r],r,l));return o},multiplier:function(i){var e=i.toString().split(".");return e.length<2?1:Math.pow(10,e[1].length)},correctionFactor:function(){var i=Array.prototype.slice.call(arguments);return i.reduce(function(e,l){var t=s.multiplier(l);return e>t?e:t},1)},toFixed:function(i,e,l,t){var r=i.toString().split("."),o=e-(t||0),a,u,f,d;return r.length===2?a=Math.min(Math.max(r[1].length,o),e):a=o,f=Math.pow(10,a),d=(l(i+"e+"+a)/f).toFixed(a),t>e-a&&(u=new RegExp("\\.?0{1,"+(t-(e-a))+"}$"),d=d.replace(u,"")),d}},n.options=b,n.formats=y,n.locales=F,n.locale=function(i){return i&&(b.currentLocale=i.toLowerCase()),b.currentLocale},n.localeData=function(i){if(!i)return F[b.currentLocale];if(i=i.toLowerCase(),!F[i])throw new Error("Unknown locale : "+i);return F[i]},n.reset=function(){for(var i in _)b[i]=_[i]},n.zeroFormat=function(i){b.zeroFormat=typeof i=="string"?i:null},n.nullFormat=function(i){b.nullFormat=typeof i=="string"?i:null},n.defaultFormat=function(i){b.defaultFormat=typeof i=="string"?i:"0.0"},n.register=function(i,e,l){if(e=e.toLowerCase(),this[i+"s"][e])throw new TypeError(e+" "+i+" already registered.");return this[i+"s"][e]=l,l},n.validate=function(i,e){var l,t,r,o,a,u,f,d;if(typeof i!="string"&&(i+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",i)),i=i.trim(),i.match(/^\d+$/))return!0;if(i==="")return!1;try{f=n.localeData(e)}catch{f=n.localeData(n.locale())}return r=f.currency.symbol,a=f.abbreviations,l=f.delimiters.decimal,f.delimiters.thousands==="."?t="\\.":t=f.delimiters.thousands,d=i.match(/^[^\d]+/),d!==null&&(i=i.substr(1),d[0]!==r)||(d=i.match(/[^\d]+$/),d!==null&&(i=i.slice(0,-1),d[0]!==a.thousand&&d[0]!==a.million&&d[0]!==a.billion&&d[0]!==a.trillion))?!1:(u=new RegExp(t+"{2}"),i.match(/[^\d.,]/g)?!1:(o=i.split(l),o.length>2?!1:o.length<2?!!o[0].match(/^\d+.*\d$/)&&!o[0].match(u):o[0].length===1?!!o[0].match(/^\d+$/)&&!o[0].match(u)&&!!o[1].match(/^\d+$/):!!o[0].match(/^\d+.*\d$/)&&!o[0].match(u)&&!!o[1].match(/^\d+$/)))},n.fn=M.prototype={clone:function(){return n(this)},format:function(i,e){var l=this._value,t=i||b.defaultFormat,r,o,a;if(e=e||Math.round,l===0&&b.zeroFormat!==null)o=b.zeroFormat;else if(l===null&&b.nullFormat!==null)o=b.nullFormat;else{for(r in y)if(t.match(y[r].regexps.format)){a=y[r].format;break}a=a||n._.numberToFormat,o=a(l,t,e)}return o},value:function(){return this._value},input:function(){return this._input},set:function(i){return this._value=Number(i),this},add:function(i){var e=s.correctionFactor.call(null,this._value,i);function l(t,r,o,a){return t+Math.round(e*r)}return this._value=s.reduce([this._value,i],l,0)/e,this},subtract:function(i){var e=s.correctionFactor.call(null,this._value,i);function l(t,r,o,a){return t-Math.round(e*r)}return this._value=s.reduce([i],l,Math.round(this._value*e))/e,this},multiply:function(i){function e(l,t,r,o){var a=s.correctionFactor(l,t);return Math.round(l*a)*Math.round(t*a)/Math.round(a*a)}return this._value=s.reduce([this._value,i],e,1),this},divide:function(i){function e(l,t,r,o){var a=s.correctionFactor(l,t);return Math.round(l*a)/Math.round(t*a)}return this._value=s.reduce([this._value,i],e),this},difference:function(i){return Math.abs(n(this._value).subtract(i).value())}},n.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(i){var e=i%10;return~~(i%100/10)===1?"th":e===1?"st":e===2?"nd":e===3?"rd":"th"},currency:{symbol:"$"}}),function(){n.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(i,e,l){var t=n._.includes(e," BPS")?" ":"",r;return i=i*1e4,e=e.replace(/\s?BPS/,""),r=n._.numberToFormat(i,e,l),n._.includes(r,")")?(r=r.split(""),r.splice(-1,0,t+"BPS"),r=r.join("")):r=r+t+"BPS",r},unformat:function(i){return+(n._.stringToNumber(i)*1e-4).toFixed(15)}})}(),function(){var i={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},e={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},l=i.suffixes.concat(e.suffixes.filter(function(r){return i.suffixes.indexOf(r)<0})),t=l.join("|");t="("+t.replace("B","B(?!PS)")+")",n.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(t)},format:function(r,o,a){var u,f=n._.includes(o,"ib")?e:i,d=n._.includes(o," b")||n._.includes(o," ib")?" ":"",h,k,p;for(o=o.replace(/\s?i?b/,""),h=0;h<=f.suffixes.length;h++)if(k=Math.pow(f.base,h),p=Math.pow(f.base,h+1),r===null||r===0||r>=k&&r<p){d+=f.suffixes[h],k>0&&(r=r/k);break}return u=n._.numberToFormat(r,o,a),u+d},unformat:function(r){var o=n._.stringToNumber(r),a,u;if(o){for(a=i.suffixes.length-1;a>=0;a--){if(n._.includes(r,i.suffixes[a])){u=Math.pow(i.base,a);break}if(n._.includes(r,e.suffixes[a])){u=Math.pow(e.base,a);break}}o*=u||1}return o}})}(),function(){n.register("format","currency",{regexps:{format:/(\$)/},format:function(i,e,l){var t=n.locales[n.options.currentLocale],r={before:e.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:e.match(/([\+|\-|\)|\s|\$]*)$/)[0]},o,a,u;for(e=e.replace(/\s?\$\s?/,""),o=n._.numberToFormat(i,e,l),i>=0?(r.before=r.before.replace(/[\-\(]/,""),r.after=r.after.replace(/[\-\)]/,"")):i<0&&!n._.includes(r.before,"-")&&!n._.includes(r.before,"(")&&(r.before="-"+r.before),u=0;u<r.before.length;u++)switch(a=r.before[u],a){case"$":o=n._.insert(o,t.currency.symbol,u);break;case" ":o=n._.insert(o," ",u+t.currency.symbol.length-1);break}for(u=r.after.length-1;u>=0;u--)switch(a=r.after[u],a){case"$":o=u===r.after.length-1?o+t.currency.symbol:n._.insert(o,t.currency.symbol,-(r.after.length-(1+u)));break;case" ":o=u===r.after.length-1?o+" ":n._.insert(o," ",-(r.after.length-(1+u)+t.currency.symbol.length-1));break}return o}})}(),function(){n.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(i,e,l){var t,r=typeof i=="number"&&!n._.isNaN(i)?i.toExponential():"0e+0",o=r.split("e");return e=e.replace(/e[\+|\-]{1}0/,""),t=n._.numberToFormat(Number(o[0]),e,l),t+"e"+o[1]},unformat:function(i){var e=n._.includes(i,"e+")?i.split("e+"):i.split("e-"),l=Number(e[0]),t=Number(e[1]);t=n._.includes(i,"e-")?t*=-1:t;function r(o,a,u,f){var d=n._.correctionFactor(o,a),h=o*d*(a*d)/(d*d);return h}return n._.reduce([l,Math.pow(10,t)],r,1)}})}(),function(){n.register("format","ordinal",{regexps:{format:/(o)/},format:function(i,e,l){var t=n.locales[n.options.currentLocale],r,o=n._.includes(e," o")?" ":"";return e=e.replace(/\s?o/,""),o+=t.ordinal(i),r=n._.numberToFormat(i,e,l),r+o}})}(),function(){n.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(i,e,l){var t=n._.includes(e," %")?" ":"",r;return n.options.scalePercentBy100&&(i=i*100),e=e.replace(/\s?\%/,""),r=n._.numberToFormat(i,e,l),n._.includes(r,")")?(r=r.split(""),r.splice(-1,0,t+"%"),r=r.join("")):r=r+t+"%",r},unformat:function(i){var e=n._.stringToNumber(i);return n.options.scalePercentBy100?e*.01:e}})}(),function(){n.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(i,e,l){var t=Math.floor(i/60/60),r=Math.floor((i-t*60*60)/60),o=Math.round(i-t*60*60-r*60);return t+":"+(r<10?"0"+r:r)+":"+(o<10?"0"+o:o)},unformat:function(i){var e=i.split(":"),l=0;return e.length===3?(l=l+Number(e[0])*60*60,l=l+Number(e[1])*60,l=l+Number(e[2])):e.length===2&&(l=l+Number(e[0])*60,l=l+Number(e[1])),Number(l)}})}(),n})}(T)),T.exports}var z=L();const R=j(z);var C={exports:{}};/*! @preserve
 * numeral.js
 * locales : 2.0.6
 * license : MIT
 * http://adamwdraper.github.com/Numeral-js/
 */(function(B){(function(n,s){B.exports?s(L()):s(n.numeral)})(O,function(n){(function(){n.register("locale","bg",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"хил",million:"млн",billion:"млрд",trillion:"трлн"},ordinal:function(s){return""},currency:{symbol:"лв"}})})(),function(){n.register("locale","chs",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(s){return"."},currency:{symbol:"¥"}})}(),function(){n.register("locale","cs",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Kč"}})}(),function(){n.register("locale","da-dk",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mio",billion:"mia",trillion:"b"},ordinal:function(s){return"."},currency:{symbol:"DKK"}})}(),function(){n.register("locale","de-ch",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"CHF"}})}(),function(){n.register("locale","de",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","en-au",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"$"}})}(),function(){n.register("locale","en-gb",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"£"}})}(),function(){n.register("locale","en-za",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"R"}})}(),function(){n.register("locale","es-es",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return c===1||c===3?"er":c===2?"do":c===7||c===0?"mo":c===8?"vo":c===9?"no":"to"},currency:{symbol:"€"}})}(),function(){n.register("locale","es",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return c===1||c===3?"er":c===2?"do":c===7||c===0?"mo":c===8?"vo":c===9?"no":"to"},currency:{symbol:"$"}})}(),function(){n.register("locale","et",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:" tuh",million:" mln",billion:" mld",trillion:" trl"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","fi",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","fr-ca",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"$"}})}(),function(){n.register("locale","fr-ch",{delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"CHF"}})}(),function(){n.register("locale","fr",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"€"}})}(),function(){n.register("locale","hu",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"E",million:"M",billion:"Mrd",trillion:"T"},ordinal:function(s){return"."},currency:{symbol:" Ft"}})}(),function(){n.register("locale","it",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"€"}})}(),function(){n.register("locale","ja",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十億",trillion:"兆"},ordinal:function(s){return"."},currency:{symbol:"¥"}})}(),function(){n.register("locale","lv",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:" tūkst.",million:" milj.",billion:" mljrd.",trillion:" trilj."},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","nl-be",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:" mln",billion:" mld",trillion:" bln"},ordinal:function(s){var c=s%100;return s!==0&&c<=1||c===8||c>=20?"ste":"de"},currency:{symbol:"€ "}})}(),function(){n.register("locale","nl-nl",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mrd",trillion:"bln"},ordinal:function(s){var c=s%100;return s!==0&&c<=1||c===8||c>=20?"ste":"de"},currency:{symbol:"€ "}})}(),function(){n.register("locale","no",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"kr"}})}(),function(){n.register("locale","pl",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tys.",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(s){return"."},currency:{symbol:"PLN"}})}(),function(){n.register("locale","pt-br",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mil",million:"milhões",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"R$"}})}(),function(){n.register("locale","pt-pt",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"€"}})}(),function(){n.register("locale","ru-ua",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"₴"}})}(),function(){n.register("locale","ru",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн.",billion:"млрд.",trillion:"трлн."},ordinal:function(){return"."},currency:{symbol:"руб."}})}(),function(){n.register("locale","sk",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","sl",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mio",billion:"mrd",trillion:"trilijon"},ordinal:function(){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","th",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"พัน",million:"ล้าน",billion:"พันล้าน",trillion:"ล้านล้าน"},ordinal:function(s){return"."},currency:{symbol:"฿"}})}(),function(){var s={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};n.register("locale","tr",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"bin",million:"milyon",billion:"milyar",trillion:"trilyon"},ordinal:function(c){if(c===0)return"'ıncı";var y=c%10,F=c%100-y,_=c>=100?100:null;return s[y]||s[F]||s[_]},currency:{symbol:"₺"}})}(),function(){n.register("locale","uk-ua",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тис.",million:"млн",billion:"млрд",trillion:"блн"},ordinal:function(){return""},currency:{symbol:"₴"}})}(),function(){n.register("locale","vi",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:" nghìn",million:" triệu",billion:" tỷ",trillion:" nghìn tỷ"},ordinal:function(){return"."},currency:{symbol:"₫"}})}()})})(C);try{window.numeral=R}catch{}
