// Timeline
// *******************************************************************************

// Timeline point
@mixin template-timeline-point-variant($point-color, $background) {
  .timeline {
    #{$point-color} {
      background-color: $background !important;
      outline: 3px solid rgba($background, 0.12);
    }

    // Timeline-outline styles
    &.timeline-outline {
      #{$point-color} {
        border: 2px solid $background !important;
      }
    }
  }
}

@mixin template-timeline-indicator-variant($indicator-color, $background) {
  $color: $background;
  $background: rgba-to-hex(rgba($background, 0.16), $rgba-to-hex-bg);

  .timeline {
    #{$indicator-color} {
      background-color: $background;
      i {
        color: $color !important;
      }
    }
  }
}
