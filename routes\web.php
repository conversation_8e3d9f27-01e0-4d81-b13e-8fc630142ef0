<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\authentications\LoginBasic;
use App\Http\Controllers\authentications\RegisterBasic;
use App\Http\Controllers\authentications\LogoutBasic;
use App\Http\Controllers\deposit\DepositIndex;
use App\Http\Controllers\deposit\DepositList;
use App\Http\Controllers\hotmail\HotmailIndex;
use App\Http\Controllers\hotmail\HotmailPurchase;
use App\Http\Controllers\order\OrderList;
use App\Http\Controllers\software\SoftwareCreate;
use App\Http\Controllers\software\SoftwareEdit;
use App\Http\Controllers\software\SoftwareIndex;
use App\Http\Controllers\software\SoftwareList;
use App\Http\Controllers\software\SoftwareShow;
use App\Http\Controllers\simrental\SimRentalController;
use App\Http\Controllers\admin\SimRentalAdminController;


Route::group(['middleware' => ['isAdmin']], function () {
    Route::get('/software/create', [SoftwareCreate::class, 'index'])->name('software-create');
    Route::post('/software/create', [SoftwareCreate::class, 'create'])->name('software-create');

    Route::get('/software/list', [SoftwareList::class, 'index'])->name('software-list');
    Route::get('/software/edit/{id}', [SoftwareEdit::class, 'index'])->name('software-edit');
    Route::put('/software/edit/{id}', [SoftwareEdit::class, 'update'])->name('software-update');
    Route::delete('/software/{id}', [SoftwareList::class, 'destroy'])->name('software-delete');
    Route::patch('/software/{id}/status', [SoftwareList::class, 'updateStatus'])->name('software-update-status');
});

Route::get('/', [SoftwareIndex::class, 'index'])->name('software-all');
Route::get('/demo-sim-rental', [SimRentalController::class, 'index'])->name('demo-sim-rental');
Route::get('/software/all', [SoftwareIndex::class, 'index'])->name('software-all');
Route::get('/software/free', [SoftwareIndex::class, 'free'])->name('software-free');
Route::get('/software/paid', [SoftwareIndex::class, 'paid'])->name('software-paid');
Route::get('/software/{url}', [SoftwareShow::class, 'index'])->name('software-show');


Route::get('/hotmail', [HotmailIndex::class, 'index'])->name('hotmail-index');

Route::get('/user/deposit', [DepositIndex::class, 'index'])->name('deposit-index');

// Sim Rental Routes
Route::get('/sim-rental', [SimRentalController::class, 'index'])->name('sim-rental-index');
Route::get('/api/sim-rental/servers', [SimRentalController::class, 'getServers'])->name('sim-rental-servers');
Route::get('/api/sim-rental/countries/{serverId}', [SimRentalController::class, 'getCountries'])->name('sim-rental-countries');
Route::get('/api/sim-rental/services/{serverId}/{countryId?}', [SimRentalController::class, 'getServices'])->name('sim-rental-services');
Route::get('/api/sim-rental/sim-cards', [SimRentalController::class, 'getSimCards'])->name('sim-rental-sim-cards');

// SEO Routes
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', function () {
    $content = "User-agent: *\nAllow: /\n\nSitemap: " . url('/sitemap.xml');
    return response($content, 200)->header('Content-Type', 'text/plain');
})->name('robots');

Route::middleware('auth')->group(function () {
    Route::post('/hotmail/purchase', [HotmailPurchase::class, 'purchase'])->name('hotmail-purchase');
    Route::get('/deposit', [DepositList::class, 'index'])->name('deposit-list');
    Route::get('/order', [OrderList::class, 'index'])->name('order-list');

    // Protected Sim Rental Routes
    Route::post('/api/sim-rental/rent', [SimRentalController::class, 'rentSim'])->name('sim-rental-rent');

    // Admin Sim Rental Routes
    Route::prefix('admin/sim-rental')->name('admin.sim-rental.')->middleware('isAdmin')->group(function () {
        Route::get('/', [SimRentalAdminController::class, 'index'])->name('index');

        // Server management
        Route::post('/servers', [SimRentalAdminController::class, 'storeServer'])->name('servers.store');
        Route::put('/servers/{id}', [SimRentalAdminController::class, 'updateServer'])->name('servers.update');
        Route::delete('/servers/{id}', [SimRentalAdminController::class, 'deleteServer'])->name('servers.delete');

        // Country management
        Route::post('/countries', [SimRentalAdminController::class, 'storeCountry'])->name('countries.store');
        Route::put('/countries/{id}', [SimRentalAdminController::class, 'updateCountry'])->name('countries.update');
        Route::delete('/countries/{id}', [SimRentalAdminController::class, 'deleteCountry'])->name('countries.delete');

        // Service management
        Route::post('/services', [SimRentalAdminController::class, 'storeService'])->name('services.store');
        Route::put('/services/{id}', [SimRentalAdminController::class, 'updateService'])->name('services.update');
        Route::delete('/services/{id}', [SimRentalAdminController::class, 'deleteService'])->name('services.delete');
    });
});

// Main Page Route
// Route::get('/', [Analytics::class, 'index'])->name('dashboard-analytics');
// Route::get('/dashboard/analytics', [Analytics::class, 'index'])->name('dashboard-analytics');
// Route::get('/dashboard/crm', [Crm::class, 'index'])->name('dashboard-crm');
// locale
Route::get('/lang/{locale}', [LanguageController::class, 'swap']);

// authentication
Route::get('/auth/login', [LoginBasic::class, 'index'])->name('auth-login');
Route::post('/auth/login', [LoginBasic::class, 'login'])->name('auth-login');


Route::get('/auth/register', [RegisterBasic::class, 'index'])->name('auth-register');
Route::post('/auth/register', [RegisterBasic::class, 'register'])->name('auth-register');

Route::get('/auth/logout', [LogoutBasic::class, 'logout'])->name('auth-logout');
