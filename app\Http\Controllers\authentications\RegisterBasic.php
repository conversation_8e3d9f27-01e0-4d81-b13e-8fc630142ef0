<?php

namespace App\Http\Controllers\authentications;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class RegisterBasic extends Controller
{
    public function index()
    {
        $pageConfigs = ['myLayout' => 'blank'];
        return view('content.authentications.auth-register-basic', ['pageConfigs' => $pageConfigs]);
    }

    public function register(Request $request)
    {
        $request->validate([
            'username' => 'required|string|min:6|max:14|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6'
        ]);
        $data = $request->only('username', 'email', 'password');
        $data['password'] = bcrypt($data['password']);
        // dd($data);
        $user = User::create($data);
        auth()->login($user);
        return redirect()->route('software-all')->with('success', __('messages.registration_successful'));
    }
}
