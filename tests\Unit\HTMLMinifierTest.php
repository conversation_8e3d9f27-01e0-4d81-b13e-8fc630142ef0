<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\HTMLMinifier;

class HTMLMinifierTest extends TestCase
{
    /** @test */
    public function it_removes_html_comments()
    {
        $html = '<!-- This is a comment --><div>Content</div><!-- Another comment -->';
        $expected = '<div>Content</div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_preserves_ie_conditional_comments()
    {
        $html = '<!--[if IE]><div>IE Content</div><![endif]--><div>Normal content</div>';
        $expected = '<!--[if IE]><div>IE Content</div><![endif]--><div>Normal content</div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_removes_extra_whitespace()
    {
        $html = '<div>   <p>  Content  </p>   </div>';
        $expected = '<div><p> Content </p></div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_preserves_content_in_pre_tags()
    {
        $html = '<pre>  Code with   spaces  </pre><div>  Normal content  </div>';
        $expected = '<pre>  Code with   spaces  </pre><div>Normal content</div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_preserves_content_in_code_tags()
    {
        $html = '<code>  var x = 1;  </code><div>  Normal content  </div>';
        $expected = '<code>  var x = 1;  </code><div>Normal content</div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_preserves_content_in_textarea_tags()
    {
        $html = '<textarea>  User input with   spaces  </textarea><div>  Normal content  </div>';
        $expected = '<textarea>  User input with   spaces  </textarea><div>Normal content</div>';
        
        $result = HTMLMinifier::minify($html);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_minifies_css()
    {
        $css = '/* Comment */ body { margin: 0 ; padding: 10px ; }';
        $expected = 'body{margin:0;padding:10px}';
        
        $result = HTMLMinifier::minifyCSS($css);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_minifies_javascript()
    {
        $js = '// Comment
        var x = 1 ;
        function test ( ) {
            return x ;
        }';
        $expected = 'var x=1;function test(){return x}';
        
        $result = HTMLMinifier::minifyJS($js);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_minifies_inline_css_in_html()
    {
        $html = '<style>/* Comment */ body { margin: 0 ; }</style><div>Content</div>';
        $expected = '<style>body{margin:0}</style><div>Content</div>';
        
        $result = HTMLMinifier::minifyAdvanced($html, ['minify_css' => true]);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_minifies_inline_js_in_html()
    {
        $html = '<script>// Comment
        var x = 1 ;</script><div>Content</div>';
        $expected = '<script>var x=1</script><div>Content</div>';
        
        $result = HTMLMinifier::minifyAdvanced($html, ['minify_js' => true]);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_removes_empty_attributes()
    {
        $html = '<div class="" id="" style="">Content</div>';
        $expected = '<div>Content</div>';
        
        $result = HTMLMinifier::minifyAdvanced($html, ['remove_empty_attributes' => true]);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_removes_default_attributes()
    {
        $html = '<form method="get"><script type="text/javascript">alert("test");</script></form>';
        $expected = '<form><script>alert("test");</script></form>';
        
        $result = HTMLMinifier::minifyAdvanced($html, ['remove_default_attributes' => true]);
        $this->assertEquals($expected, $result);
    }

    /** @test */
    public function it_calculates_compression_stats()
    {
        $original = '<div>   <p>  Content with lots of spaces  </p>   </div>';
        $minified = HTMLMinifier::minify($original);
        
        $stats = HTMLMinifier::getCompressionStats($original, $minified);
        
        $this->assertArrayHasKey('original_size', $stats);
        $this->assertArrayHasKey('minified_size', $stats);
        $this->assertArrayHasKey('saved_bytes', $stats);
        $this->assertArrayHasKey('compression_ratio', $stats);
        
        $this->assertGreaterThan(0, $stats['saved_bytes']);
        $this->assertGreaterThan(0, $stats['compression_ratio']);
    }

    /** @test */
    public function it_handles_complex_html_structure()
    {
        $html = '<!DOCTYPE html>
        <html>
        <head>
            <title>  Test Page  </title>
            <style>
                /* CSS Comment */
                body { margin: 0 ; }
            </style>
        </head>
        <body>
            <!-- HTML Comment -->
            <div class="">
                <p>  Content  </p>
            </div>
            <script>
                // JS Comment
                var x = 1 ;
            </script>
        </body>
        </html>';
        
        $result = HTMLMinifier::minifyAdvanced($html);
        
        // Should remove comments and extra whitespace
        $this->assertStringNotContainsString('<!-- HTML Comment -->', $result);
        $this->assertStringNotContainsString('/* CSS Comment */', $result);
        $this->assertStringNotContainsString('// JS Comment', $result);
        
        // Should preserve structure
        $this->assertStringContainsString('<html>', $result);
        $this->assertStringContainsString('<head>', $result);
        $this->assertStringContainsString('<body>', $result);
        $this->assertStringContainsString('Content', $result);
    }
}
