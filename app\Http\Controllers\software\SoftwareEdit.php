<?php

namespace App\Http\Controllers\software;

use App\Http\Controllers\Controller;
use App\Models\Software;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SoftwareEdit extends Controller
{
    public function index($id)
    {
        $software = Software::with('tags')->findOrFail($id);
        $allTags = Tag::all();

        return view('content.software.software-edit', compact('software', 'allTags'));
    }

    public function update(Request $request, $id)
    {
        $software = Software::findOrFail($id);

        // Custom validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'en_name' => 'nullable|string|max:255',
            'description' => 'required|string',
            'video' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_free' => 'sometimes|accepted',
            'tags' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ];

        // Add price validation only if not free
        if (!$request->has('is_free')) {
            $rules['price_1_m'] = 'nullable|integer|min:0';
            $rules['price_3_m'] = 'nullable|integer|min:0';
            $rules['price_6_m'] = 'nullable|integer|min:0';
            $rules['price_1_y'] = 'nullable|integer|min:0';
            $rules['price_lifetime'] = 'nullable|integer|min:0';
            $rules['price_code'] = 'nullable|integer|min:0';
        }

        $data = $request->validate($rules);

        // Set is_free flag
        $data['is_free'] = $request->has('is_free');

        // Cập nhật URL nếu tên thay đổi
        if ($data['name'] !== $software->name) {
            $data['url'] = Str::slug($data['name']) . '-' . $software->id;
        }

        // Xử lý tags - tách ra khỏi data chính
        $tagData = $data['tags'] ?? '';
        unset($data['tags']); // Loại bỏ tags khỏi data để tránh lỗi khi update software

        $tagValues = [];
        $decodedTags = json_decode($tagData, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decodedTags)) {
            $tagValues = array_column($decodedTags, 'value');
        }

        // Xử lý upload ảnh mới
        if ($request->hasFile('image')) {
            // Xóa ảnh cũ
            if ($software->image && file_exists(public_path($software->image))) {
                unlink(public_path($software->image));
            }

            $file = $request->file('image');
            $newUrl = isset($data['url']) ? $data['url'] : $software->url;
            $fileName = $newUrl . '.' . $file->getClientOriginalExtension();
            $destinationPath = public_path('tool_image');

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            $file->move($destinationPath, $fileName);
            $data['image'] = '/tool_image/' . $fileName;
        }

        // Clear price fields if free
        if ($data['is_free']) {
            $data['price_1_m'] = 0;
            $data['price_3_m'] = 0;
            $data['price_6_m'] = 0;
            $data['price_1_y'] = 0;
            $data['price_lifetime'] = 0;
            $data['price_code'] = 0;
        }

        $software->update($data);

        // Xử lý tags sau khi update software
        if (!empty($tagValues)) {
            $tagIds = [];
            foreach ($tagValues as $tagName) {
                // Tìm hoặc tạo tag
                $tag = Tag::firstOrCreate(['name' => $tagName]);
                $tagIds[] = $tag->id;
            }
            // Sync tags (thay thế toàn bộ tags cũ bằng tags mới)
            $software->tags()->sync($tagIds);
        } else {
            // Nếu không có tags, xóa tất cả tags hiện tại
            $software->tags()->detach();
        }

        return redirect()->route('software-list')->with('success', 'Cập nhật software thành công!');
    }
}
