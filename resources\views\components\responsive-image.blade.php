@props([
    'src',
    'alt' => '',
    'class' => '',
    'width' => null,
    'height' => null,
    'lazy' => true,
    'critical' => false,
    'sizes' => null
])

@php
    $responsive = \App\Helpers\ImageHelper::generateResponsiveSrcset($src);
    $loading = $critical ? 'eager' : ($lazy ? 'lazy' : 'eager');
    $decoding = $critical ? 'sync' : 'async';
    $fetchpriority = $critical ? 'high' : 'auto';
    $imageSizes = $sizes ?? $responsive['sizes'];
@endphp

<img 
    src="{{ $responsive['default'] }}" 
    srcset="{{ $responsive['srcset'] }}"
    sizes="{{ $imageSizes }}"
    alt="{{ $alt }}"
    @if($class) class="{{ $class }}" @endif
    @if($width) width="{{ $width }}" @endif
    @if($height) height="{{ $height }}" @endif
    loading="{{ $loading }}"
    decoding="{{ $decoding }}"
    @if($fetchpriority !== 'auto') fetchpriority="{{ $fetchpriority }}" @endif
    {{ $attributes }}
/>
