$(function(){var l=$(".software-list-table");if(l.length)var p=l.DataTable({ajax:assetsPath+"json/invoice-list.json",columns:[{data:"invoice_id"},{data:"invoice_id"},{data:"invoice_id"},{data:"invoice_status"},{data:"issued_date"},{data:"client_name"},{data:"total"},{data:"balance"},{data:"invoice_status"},{data:"action"}],columnDefs:[{className:"control",responsivePriority:2,searchable:!1,targets:0,render:function(a,n,e,s){return""}},{targets:1,orderable:!1,checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'},render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input" >'},searchable:!1},{targets:2,render:function(a,n,e,s){var t=e.invoice_id,i='<a href="'+baseUrl+'app/invoice/preview">#'+t+"</a>";return i}},{targets:3,render:function(a,n,e,s){var t=e.invoice_status,i=e.due_date,c=e.balance,o={Sent:'<span class="badge badge-center rounded-pill bg-label-secondary w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-circle-check ti-xs"></i></span>',Draft:'<span class="badge badge-center rounded-pill bg-label-primary w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-device-floppy ti-xs"></i></span>',"Past Due":'<span class="badge badge-center rounded-pill bg-label-danger w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-info-circle ti-xs"></i></span>',"Partial Payment":'<span class="badge badge-center rounded-pill bg-label-success w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-circle-half-2 ti-xs"></i></span>',Paid:'<span class="badge badge-center rounded-pill bg-label-warning w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-chart-pie ti-xs"></i></span>',Downloaded:'<span class="badge badge-center rounded-pill bg-label-info w-px-30 h-px-30 d-flex align-items-center justify-content-center"><i class="ti ti-arrow-down-circle ti-xs"></i></span>'};return"<span class='d-inline-block' data-bs-toggle='tooltip' data-bs-html='true' title='<span>"+t+'<br> <span class="fw-medium">Balance:</span> '+c+'<br> <span class="fw-medium">Due Date:</span> '+i+"</span>'>"+o[t]+"</span>"}},{targets:4,responsivePriority:4,render:function(a,n,e,s){var t=e.client_name,i=e.service,c=e.avatar_image,o=Math.floor(Math.random()*11)+1,m=o+".png";if(c===!0)var d='<img src="'+assetsPath+"img/avatars/"+m+'" alt="Avatar" class="rounded-circle">';else{var u=Math.floor(Math.random()*6),f=["success","danger","warning","info","primary","secondary"],b=f[u],t=e.client_name,r=t.match(/\b\w/g)||[];r=((r.shift()||"")+(r.pop()||"")).toUpperCase(),d='<span class="avatar-initial rounded-circle bg-label-'+b+'">'+r+"</span>"}var v='<div class="d-flex justify-content-start align-items-center"><div class="avatar-wrapper"><div class="avatar avatar-sm me-3">'+d+'</div></div><div class="d-flex flex-column"><a href="'+baseUrl+'pages/profile-user" class="text-heading text-truncate"><span class="fw-medium">'+t+'</span></a><small class="text-truncate">'+i+"</small></div></div>";return v}},{targets:5,render:function(a,n,e,s){var t=e.total;return'<span class="d-none">'+t+"</span>$"+t}},{targets:6,render:function(a,n,e,s){var t=new Date(e.due_date),i='<span class="d-none">'+moment(t).format("YYYYMMDD")+"</span>"+moment(t).format("DD MMM YYYY");return i}},{targets:7,orderable:!1,render:function(a,n,e,s){var t=e.balance;if(t===0){var i="bg-label-success";return'<span class="badge '+i+'" text-capitalized> Paid </span>'}else return'<span class="d-none">'+t+'</span><span class="text-heading">'+t+"</span>"}},{targets:8,visible:!1},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(a,n,e,s){return'<div class="d-flex align-items-center"><a href="javascript:;" data-bs-toggle="tooltip" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill" data-bs-placement="top" title="Delete"><i class="ti ti-trash mx-2 ti-md"></i></a><a href="'+baseUrl+'app/invoice/preview" data-bs-toggle="tooltip" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill" data-bs-placement="top" title="Preview Invoice"><i class="ti ti-eye mx-2 ti-md"></i></a><div class="dropdown"><a href="javascript:;" class="btn dropdown-toggle hide-arrow btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill p-0" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end"><a href="javascript:;" class="dropdown-item">Download</a><a href="'+baseUrl+'app/invoice/edit" class="dropdown-item">Edit</a><a href="javascript:;" class="dropdown-item">Duplicate</a></div></div>'}}],order:[[2,"desc"]],dom:'<"row mx-1"<"col-12 col-md-6 d-flex align-items-center justify-content-center justify-content-md-start gap-2"l<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start"B>><"col-12 col-md-6 d-flex align-items-center justify-content-end flex-column flex-md-row pe-5 gap-md-4 mt-n6 mt-md-0"f<"invoice_status mb-6 mb-md-0">>>t<"row mx-1"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',language:{sLengthMenu:"Show _MENU_",search:"",searchPlaceholder:"Search Software",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{text:'<i class="ti ti-plus ti-xs me-md-2"></i><span class="d-md-inline-block d-none">Create Software</span>',className:"btn btn-primary waves-effect waves-light",action:function(a,n,e,s){window.location=baseUrl+"software/create"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(a){var n=a.data();return"Details of "+n.full_name}}),type:"column",renderer:function(a,n,e){var s=$.map(e,function(t,i){return t.title!==""?'<tr data-dt-row="'+t.rowIndex+'" data-dt-column="'+t.columnIndex+'"><td>'+t.title+":</td> <td>"+t.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}},initComplete:function(){this.api().columns(8).every(function(){var a=this,n=$('<select id="UserRole" class="form-select"><option value=""> Invoice Status </option></select>').appendTo(".invoice_status").on("change",function(){var e=$.fn.dataTable.util.escapeRegex($(this).val());a.search(e?"^"+e+"$":"",!0,!1).draw()});a.data().unique().sort().each(function(e,s){n.append('<option value="'+e+'" class="text-capitalize">'+e+"</option>")})})}});l.on("draw.dt",function(){var a=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));a.map(function(n){return new bootstrap.Tooltip(n,{boundary:document.body})})}),$(".software-list-table tbody").on("click",".delete-record",function(){p.row($(this).parents("tr")).remove().draw()}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
