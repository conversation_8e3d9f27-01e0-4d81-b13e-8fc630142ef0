<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class CSSOptimizer
{
    /**
     * Get optimized CSS for specific page
     * 
     * @param string $page
     * @return array
     */
    public static function getOptimizedCSS($page = 'default')
    {
        $cacheKey = "optimized_css_{$page}";
        
        return Cache::remember($cacheKey, 3600, function () use ($page) {
            return self::optimizeCSS($page);
        });
    }
    
    /**
     * Optimize CSS based on page type
     * 
     * @param string $page
     * @return array
     */
    private static function optimizeCSS($page)
    {
        switch ($page) {
            case 'software':
            case 'home':
                return [
                    'critical' => self::getCriticalCSS($page),
                    'deferred' => self::getDeferredCSS($page),
                    'unused' => self::getUnusedCSS($page)
                ];
            default:
                return [
                    'critical' => self::getCriticalCSS('default'),
                    'deferred' => self::getDeferredCSS('default'),
                    'unused' => self::getUnusedCSS('default')
                ];
        }
    }
    
    /**
     * Get critical CSS that should load immediately
     * 
     * @param string $page
     * @return array
     */
    private static function getCriticalCSS($page)
    {
        $critical = [
            'core' => true, // Always load core CSS
            'theme' => true, // Always load theme CSS
        ];
        
        switch ($page) {
            case 'software':
            case 'home':
                $critical['tabler-icons'] = true; // Need icons for software cards
                break;
            default:
                $critical['tabler-icons'] = false;
        }
        
        return $critical;
    }
    
    /**
     * Get CSS that can be deferred
     * 
     * @param string $page
     * @return array
     */
    private static function getDeferredCSS($page)
    {
        $deferred = [
            'fontawesome' => true, // Can be deferred
            'flag-icons' => true, // Can be deferred
            'node-waves' => true, // Can be deferred
            'perfect-scrollbar' => true, // Can be deferred
            'typeahead-js' => true, // Can be deferred
        ];
        
        switch ($page) {
            case 'software':
                // Software pages might need some icons immediately
                $deferred['fontawesome'] = false; // Load immediately if used in cards
                break;
        }
        
        return $deferred;
    }
    
    /**
     * Get unused CSS that can be removed
     * 
     * @param string $page
     * @return array
     */
    private static function getUnusedCSS($page)
    {
        $unused = [];
        
        switch ($page) {
            case 'software':
            case 'home':
                // Homepage/software pages don't need these
                $unused = [
                    'datatables' => true,
                    'select2' => false, // Might be used in filters
                    'bootstrap-datepicker' => true,
                    'bootstrap-daterangepicker' => true,
                    'jquery-ui' => true,
                ];
                break;
            default:
                $unused = [
                    'datatables' => false,
                    'select2' => false,
                    'bootstrap-datepicker' => false,
                    'bootstrap-daterangepicker' => false,
                    'jquery-ui' => false,
                ];
        }
        
        return $unused;
    }
    
    /**
     * Generate CSS loading strategy
     * 
     * @param string $page
     * @return string
     */
    public static function generateCSSLoadingStrategy($page = 'default')
    {
        $optimization = self::getOptimizedCSS($page);
        $strategy = '';
        
        // Critical CSS - load immediately
        $strategy .= "<!-- Critical CSS - Load Immediately -->\n";
        if ($optimization['critical']['core']) {
            $strategy .= "@vite(['resources/assets/vendor/scss/core.scss'])\n";
        }
        if ($optimization['critical']['theme']) {
            $strategy .= "@vite(['resources/assets/vendor/scss/theme-default.scss'])\n";
        }
        if ($optimization['critical']['tabler-icons']) {
            $strategy .= "@vite(['resources/assets/vendor/fonts/tabler-icons.scss'])\n";
        }
        
        // Deferred CSS - load after critical content
        $strategy .= "\n<!-- Deferred CSS - Load After Critical Content -->\n";
        foreach ($optimization['deferred'] as $css => $shouldDefer) {
            if ($shouldDefer) {
                $strategy .= "<link rel=\"preload\" href=\"{{ Vite::asset('resources/assets/vendor/fonts/{$css}.scss') }}\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n";
            }
        }
        
        return $strategy;
    }
    
    /**
     * Get minimal CSS for above-the-fold content
     * 
     * @param string $page
     * @return string
     */
    public static function getMinimalCSS($page = 'default')
    {
        switch ($page) {
            case 'software':
            case 'home':
                return '
                /* Minimal CSS for Software/Home Pages */
                body { font-family: "Public Sans", sans-serif; margin: 0; background: #f5f5f9; }
                .container-xxl { max-width: 1440px; margin: 0 auto; padding: 0 1rem; }
                .row { display: flex; flex-wrap: wrap; margin: -0.75rem; }
                .col-md-6 { flex: 0 0 50%; max-width: 50%; padding: 0.75rem; }
                .col-xxl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
                @media (max-width: 768px) { .col-md-6, .col-xxl-4 { flex: 0 0 100%; max-width: 100%; } }
                .card { background: #fff; border-radius: 0.375rem; box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075); height: 100%; border: 1px solid #d9dee3; }
                .card-body { padding: 1rem; }
                .btn { padding: 0.4375rem 1.25rem; border-radius: 0.375rem; text-decoration: none; display: inline-block; border: 1px solid transparent; font-weight: 500; text-align: center; cursor: pointer; }
                .btn-primary { background-color: #696cff; border-color: #696cff; color: #fff; }
                .img-tool { width: 100%; height: 30vh; object-fit: cover; border-radius: 0.375rem; }
                .w-100 { width: 100%; }
                .mb-6 { margin-bottom: 3rem; }
                .text-center { text-align: center; }
                ';
            default:
                return '
                /* Minimal CSS for Default Pages */
                body { font-family: "Public Sans", sans-serif; margin: 0; background: #f5f5f9; }
                .container-xxl { max-width: 1440px; margin: 0 auto; padding: 0 1rem; }
                .card { background: #fff; border-radius: 0.375rem; box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075); }
                .btn { padding: 0.4375rem 1.25rem; border-radius: 0.375rem; text-decoration: none; display: inline-block; }
                .btn-primary { background-color: #696cff; border-color: #696cff; color: #fff; }
                ';
        }
    }
    
    /**
     * Clear CSS optimization cache
     * 
     * @return void
     */
    public static function clearCache()
    {
        $pages = ['default', 'software', 'home', 'hotmail'];
        
        foreach ($pages as $page) {
            Cache::forget("optimized_css_{$page}");
        }
    }
}
