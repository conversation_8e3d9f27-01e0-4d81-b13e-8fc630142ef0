<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SmsCountry extends Model
{
    use HasFactory;

    protected $table = 'sms_countries';

    protected $fillable = [
        'name',
        'code',
        'server_id',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the server that owns this country
     */
    public function server(): BelongsTo
    {
        return $this->belongsTo(SmsServer::class, 'server_id');
    }

    /**
     * Get all services for this country
     */
    public function services(): Has<PERSON>any
    {
        return $this->hasMany(SmsService::class, 'country_id');
    }

    /**
     * Get all sim rentals for this country
     */
    public function simRentals(): HasMany
    {
        return $this->hasMany(SmsSimRental::class, 'country_id');
    }

    /**
     * Scope for active countries
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered countries
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope for specific server
     */
    public function scopeForServer($query, $serverId)
    {
        return $query->where('server_id', $serverId);
    }


}
