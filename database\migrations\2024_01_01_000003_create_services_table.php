<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code'); // telegram, whatsapp, facebook, etc.
            $table->foreignId('country_id')->constrained('sms_countries')->onDelete('cascade');
            $table->decimal('price', 10, 2); // Giá cho service này
            $table->integer('stock')->default(0); // Số lượng sim có sẵn
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique(['country_id', 'code']); // Mỗi country có thể có cùng service code
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_services');
    }
};
