// Checkboxes and Radios
// *******************************************************************************
.form-check-input {
  cursor: $form-check-label-cursor;
  &:disabled {
    background-color: $form-check-input-disabled-bg;
    border-color: $form-check-input-disabled-bg;
  }
  &:checked {
    box-shadow: $box-shadow-xs;
  }
}

.form-check {
  position: relative;
}

// Only for checkbox and radio (not for bs default switch)
//? .dt-checkboxes-cell class is used for DataTables checkboxes
.form-check:not(.form-switch),
.dt-checkboxes-cell {
  .form-check-input[type='radio'] {
    background-size: 1.3125rem;
    &:not(:checked) {
      background-size: 0.75rem;
    }
  }
}

// RTL Style
@include rtl-only {
  .form-check {
    padding-left: 0;
    padding-right: $form-check-padding-start;
  }
  .form-check .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: $form-check-padding-start * -1;
  }
}

// Switches
// *******************************************************************************

.form-switch .form-check-input {
  background-color: $form-switch-bg;
  border: none;
  box-shadow: $form-switch-box-shadow;
  &:focus {
    box-shadow: $form-switch-box-shadow;
  }
}
// RTL Style
@include rtl-only {
  .form-switch {
    padding-left: 0;
    padding-right: $form-switch-padding-start;
    .form-check-input {
      margin-left: 0;
      margin-right: $form-switch-padding-start * -1;
      background-position: right center;
      &:checked {
        background-position: $form-switch-checked-bg-position-rtl;
      }
    }
  }
  .form-check-inline {
    margin-right: 0;
    margin-left: $form-check-inline-margin-end;
  }
}

// Contextual colors for form check
@each $color, $value in $theme-colors {
  @if $color != primary {
    @include template-form-check-variant('.form-check-#{$color}', $value);
  }
}
