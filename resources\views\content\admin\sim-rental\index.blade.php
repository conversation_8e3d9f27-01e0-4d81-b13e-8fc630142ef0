@extends('layouts/layoutMaster')

@section('title', 'Sim Rental Management - Admin')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'])
@endsection

@section('page-style')
    @vite(['resources/assets/vendor/scss/pages/cards-advance.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'])
@endsection

@section('page-script')
    @vite(['resources/assets/js/admin/sim-rental-admin.js'])
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">🛠️ Sim Rental Management</h4>
                            <p class="mb-0 text-muted">Manage servers, countries, and services for sim rental system</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="badge bg-label-info fs-6 px-3 py-2">
                                <i class="ti ti-settings me-1"></i>
                                Admin Panel
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 col-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="card-info">
                                    <p class="card-text">Total Servers</p>
                                    <div class="d-flex align-items-end mb-2">
                                        <h4 class="card-title mb-0 me-2">{{ $stats['total_servers'] }}</h4>
                                        <small class="text-success">({{ $stats['active_servers'] }} active)</small>
                                    </div>
                                </div>
                                <div class="card-icon">
                                    <span class="badge bg-label-primary rounded p-2">
                                        <i class="ti ti-server ti-sm"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="card-info">
                                    <p class="card-text">Total Countries</p>
                                    <div class="d-flex align-items-end mb-2">
                                        <h4 class="card-title mb-0 me-2">{{ $stats['total_countries'] }}</h4>
                                    </div>
                                </div>
                                <div class="card-icon">
                                    <span class="badge bg-label-success rounded p-2">
                                        <i class="ti ti-world ti-sm"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="card-info">
                                    <p class="card-text">Total Services</p>
                                    <div class="d-flex align-items-end mb-2">
                                        <h4 class="card-title mb-0 me-2">{{ $stats['total_services'] }}</h4>
                                    </div>
                                </div>
                                <div class="card-icon">
                                    <span class="badge bg-label-warning rounded p-2">
                                        <i class="ti ti-apps ti-sm"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-12 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="card-info">
                                    <p class="card-text">Quick Actions</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-primary" onclick="openAddModal('server')">
                                            <i class="ti ti-plus me-1"></i>Add Server
                                        </button>
                                    </div>
                                </div>
                                <div class="card-icon">
                                    <span class="badge bg-label-info rounded p-2">
                                        <i class="ti ti-plus ti-sm"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Management Tabs -->
            <div class="nav-align-top mb-4">
                <ul class="nav nav-pills mb-3" role="tablist">
                    <li class="nav-item">
                        <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab" data-bs-target="#servers-tab" aria-controls="servers-tab" aria-selected="true">
                            <i class="ti ti-server me-1"></i>Servers
                        </button>
                    </li>
                    <li class="nav-item">
                        <button type="button" class="nav-link" role="tab" data-bs-toggle="tab" data-bs-target="#countries-tab" aria-controls="countries-tab" aria-selected="false">
                            <i class="ti ti-world me-1"></i>Countries
                        </button>
                    </li>
                    <li class="nav-item">
                        <button type="button" class="nav-link" role="tab" data-bs-toggle="tab" data-bs-target="#services-tab" aria-controls="services-tab" aria-selected="false">
                            <i class="ti ti-apps me-1"></i>Services
                        </button>
                    </li>
                </ul>
                <div class="tab-content">
                    <!-- Servers Tab -->
                    <div class="tab-pane fade show active" id="servers-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Server Management</h5>
                                <button class="btn btn-primary" onclick="openAddModal('server')">
                                    <i class="ti ti-plus me-1"></i>Add Server
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="serversTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Description</th>
                                                <th>Countries</th>
                                                <th>Sort Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($servers as $server)
                                            <tr>
                                                <td>{{ $server->id }}</td>
                                                <td>{{ $server->name }}</td>
                                                <td>{{ $server->description }}</td>
                                                <td><span class="badge bg-label-info">{{ $server->countries_count }}</span></td>
                                                <td>{{ $server->sort_order }}</td>
                                                <td>
                                                    <span class="badge bg-label-{{ $server->is_active ? 'success' : 'secondary' }}">
                                                        {{ $server->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="ti ti-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" onclick="editServer({{ $server->id }})">
                                                                <i class="ti ti-edit me-2"></i>Edit</a></li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteServer({{ $server->id }})">
                                                                <i class="ti ti-trash me-2"></i>Delete</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Countries Tab -->
                    <div class="tab-pane fade" id="countries-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Country Management</h5>
                                <button class="btn btn-primary" onclick="openAddModal('country')">
                                    <i class="ti ti-plus me-1"></i>Add Country
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="countriesTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Code</th>
                                                <th>Server</th>
                                                <th>Services</th>
                                                <th>Sort Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($countries as $country)
                                            <tr>
                                                <td>{{ $country->id }}</td>
                                                <td>{{ $country->name }}</td>
                                                <td><span class="badge bg-label-primary">{{ $country->code }}</span></td>
                                                <td>{{ $country->server->name }}</td>
                                                <td><span class="badge bg-label-info">{{ $country->services_count }}</span></td>
                                                <td>{{ $country->sort_order }}</td>
                                                <td>
                                                    <span class="badge bg-label-{{ $country->is_active ? 'success' : 'secondary' }}">
                                                        {{ $country->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="ti ti-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" onclick="editCountry({{ $country->id }})">
                                                                <i class="ti ti-edit me-2"></i>Edit</a></li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteCountry({{ $country->id }})">
                                                                <i class="ti ti-trash me-2"></i>Delete</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Services Tab -->
                    <div class="tab-pane fade" id="services-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Service Management</h5>
                                <button class="btn btn-primary" onclick="openAddModal('service')">
                                    <i class="ti ti-plus me-1"></i>Add Service
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="servicesTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Code</th>
                                                <th>Country</th>
                                                <th>Server</th>
                                                <th>Price</th>
                                                <th>Stock</th>
                                                <th>Sort Order</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($services as $service)
                                            <tr>
                                                <td>{{ $service->id }}</td>
                                                <td>{{ $service->name }}</td>
                                                <td><span class="badge bg-label-primary">{{ $service->code }}</span></td>
                                                <td>{{ $service->country->name }}</td>
                                                <td>{{ $service->country->server->name }}</td>
                                                <td><span class="text-success fw-medium">{{ number_format($service->price) }}đ</span></td>
                                                <td>
                                                    <span class="badge bg-label-{{ $service->stock > 0 ? 'success' : 'danger' }}">
                                                        {{ $service->stock }}
                                                    </span>
                                                </td>
                                                <td>{{ $service->sort_order }}</td>
                                                <td>
                                                    <span class="badge bg-label-{{ $service->is_active ? 'success' : 'secondary' }}">
                                                        {{ $service->is_active ? 'Active' : 'Inactive' }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="ti ti-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" onclick="editService({{ $service->id }})">
                                                                <i class="ti ti-edit me-2"></i>Edit</a></li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteService({{ $service->id }})">
                                                                <i class="ti ti-trash me-2"></i>Delete</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    @include('content.admin.sim-rental.modals')

    <!-- Hidden data for JavaScript -->
    <script>
        window.adminData = {
            servers: @json($servers),
            countries: @json($countries),
            services: @json($services)
        };
    </script>
@endsection
