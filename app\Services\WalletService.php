<?php

namespace App\Services;

use App\Models\User;
use App\Models\TransactionLog;
use Illuminate\Support\Facades\DB;
use Exception;

class WalletService
{
    /**
     * Trừ tiền từ tài khoản người dùng, c<PERSON> lock để tr<PERSON>h race condition
     */
    public static function debit(int $userId, float $amount, string $description = ''): void
    {
        DB::transaction(function () use ($userId, $amount, $description) {
            // Lấy user và lock hàng DB
            $user = User::where('id', $userId)->lockForUpdate()->first();

            if (!$user) {
                throw new Exception("Không tìm thấy người dùng.");
            }

            if ($user->balance < $amount) {
                throw new Exception("Số dư không đủ.");
            }

            $user->balance -= $amount;
            $user->save();

            // Ghi log giao dịch
            TransactionLog::create([
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $amount,
                'description' => $description,
            ]);
        });
    }

    public function credit(int $userId, float $amount, string $description = ''): void
    {
        DB::transaction(function () use ($userId, $amount, $description) {
            $user = User::where('id', $userId)->lockForUpdate()->first();
            if (!$user) {
                throw new Exception("Không tìm thấy người dùng.");
            }
            $user->balance += $amount;
            $user->save();
            TransactionLog::create([
                'user_id'     => $user->id,
                'type'        => 'credit',
                'amount'      => $amount,
                'description' => $description,
            ]);
        });
    }
}
