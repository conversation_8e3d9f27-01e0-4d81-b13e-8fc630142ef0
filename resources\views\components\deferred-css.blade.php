@props(['files' => []])

{{-- Defer CSS loading for better performance --}}
@if(!empty($files))
<script>
(function() {
    'use strict';
    
    // CSS files to load
    var cssFiles = @json($files);
    
    // Function to load CSS asynchronously
    function loadCSS(href) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'print';
        link.onload = function() {
            this.media = 'all';
        };
        
        // Insert before first script
        var ref = document.getElementsByTagName('script')[0];
        ref.parentNode.insertBefore(link, ref);
        
        // Fallback for older browsers
        setTimeout(function() {
            link.media = 'all';
        }, 100);
    }
    
    // Load CSS files on user interaction or after delay
    var loaded = false;
    function loadDeferredCSS() {
        if (!loaded) {
            loaded = true;
            cssFiles.forEach(loadCSS);
            
            // Remove event listeners
            document.removeEventListener('scroll', loadDeferredCSS);
            document.removeEventListener('mousemove', loadDeferredCSS);
            document.removeEventListener('touchstart', loadDeferredCSS);
            document.removeEventListener('click', loadDeferredCSS);
        }
    }
    
    // Add event listeners
    document.addEventListener('scroll', loadDeferredCSS, { passive: true });
    document.addEventListener('mousemove', loadDeferredCSS, { passive: true });
    document.addEventListener('touchstart', loadDeferredCSS, { passive: true });
    document.addEventListener('click', loadDeferredCSS, { passive: true });
    
    // Fallback: load after 2 seconds
    setTimeout(loadDeferredCSS, 2000);
})();
</script>

{{-- Noscript fallback --}}
<noscript>
    @foreach($files as $file)
        <link rel="stylesheet" href="{{ $file }}">
    @endforeach
</noscript>
@endif
