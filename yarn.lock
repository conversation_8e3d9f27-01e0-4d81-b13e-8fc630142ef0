# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  "integrity" "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw=="
  "resolved" "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.7":
  "integrity" "sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/highlight" "^7.24.7"
    "picocolors" "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.23.3", "@babel/compat-data@^7.23.5", "@babel/compat-data@^7.24.7":
  "integrity" "sha512-qJzAIcv03PyaWqxRgO4mSU3lihncDT296vnyuE2O8uA4w3UHWI4S3hgeZd1L8W1Bft40w9JxJ2b412iDUFFRhw=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.7.tgz"
  "version" "7.24.7"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.0.0-0 || ^8.0.0-0 <8.0.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.4.0 || ^8.0.0-0 <8.0.0", "@babel/core@7.23.7":
  "integrity" "sha512-+UpDgowcmqe36d4NwqvKsyPMlOLNGMsfMmQ5WGCu+siCe3t3dfe9njrzGfdN4qq+bcNUt0+Vw6haRxBOycs4dw=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.23.7.tgz"
  "version" "7.23.7"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.23.5"
    "@babel/generator" "^7.23.6"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helpers" "^7.23.7"
    "@babel/parser" "^7.23.6"
    "@babel/template" "^7.22.15"
    "@babel/traverse" "^7.23.7"
    "@babel/types" "^7.23.6"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.23.6", "@babel/generator@^7.24.7":
  "integrity" "sha512-oipXieGC3i45Y1A41t4tAqpnEZWgB/lC6Ehh6+rOviR5XWpTtMmLN+fGjz9vOiNRt0p6RtO6DtD0pdU3vpqdSA=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "jsesc" "^2.5.1"

"@babel/helper-annotate-as-pure@^7.24.7":
  "integrity" "sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.24.7":
  "integrity" "sha512-xZeCVVdwb4MsDBkkyZ64tReWYrLRHlMN72vP7Bdm3OUOuyFZExhsHUUnuWnm2/XOlAJzR0LfPpB56WXZn0X/lA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-compilation-targets@^7.22.15", "@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.23.6", "@babel/helper-compilation-targets@^7.24.7":
  "integrity" "sha512-ctSdRHBi20qWOfy27RUb4Fhp07KSJ3sXcuSvTrXrc4aG8NSYDo1ici3Vhg9bg69y5bj0Mr1lh0aeEgTvc12rMg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/compat-data" "^7.24.7"
    "@babel/helper-validator-option" "^7.24.7"
    "browserslist" "^4.22.2"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.24.7":
  "integrity" "sha512-kTkaDl7c9vO80zeX1rJxnuRpEsD5tA81yh11X1gQo+PhSti3JS+7qeZo9U4RHobKRiFPKaGK3svUAeb8D0Q7eg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.7"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/helper-replace-supers" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "semver" "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.24.7":
  "integrity" "sha512-03TCmXy2FtXJEZfbXDTSqq1fRJArk7lX9DOFC/47VthYcxyIOx+eXQmdo6DOQvrbpIix+KfXwvuXdFDZHxt+rA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "regexpu-core" "^5.3.1"
    "semver" "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.4.4":
  "integrity" "sha512-QcJMILQCu2jm5TFPGA3lCpJJTeEP+mqeXooG/NZbg/h5FTFi6V0+99ahlRsW8/kRLyb24LZVCCiclDedhLKcBA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"

"@babel/helper-define-polyfill-provider@^0.5.0":
  "integrity" "sha512-NovQquuQLAQ5HuyjCz7WQP9MjRj7dx++yspwiyUiGl9ZyadHRSql1HZh5ogRd8W8w6YM6EQ/NTB8rgjLt5W65Q=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"

"@babel/helper-define-polyfill-provider@^0.6.2":
  "integrity" "sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"

"@babel/helper-environment-visitor@^7.24.7":
  "integrity" "sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-function-name@^7.24.7":
  "integrity" "sha512-FyoJTsj/PEUWu1/TYRiXTIHc8lbw+TDYkZuoE43opPS5TrI7MyONBE1oNvfguEXAD9yhQRrVBnXdXzSLQl9XnA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-hoist-variables@^7.24.7":
  "integrity" "sha512-MJJwhkoGy5c4ehfoRyrJ/owKeMl19U54h27YYftT0o2teQ3FJ3nQUf/I3LlJsX4l3qlw7WRXUmiyajvHXoTubQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-member-expression-to-functions@^7.24.7":
  "integrity" "sha512-LGeMaf5JN4hAT471eJdBs/GK1DoYIJ5GCtZN/EsL6KUiiDZOvO/eKE11AMZJa2zP4zk4qe9V2O/hxAmkRc8p6w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-imports@^7.22.15", "@babel/helper-module-imports@^7.24.7":
  "integrity" "sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-transforms@^7.23.3", "@babel/helper-module-transforms@^7.24.7":
  "integrity" "sha512-1fuJEwIrp+97rM4RWdO+qrRsZlAeL1lQJoPqtCYWv0NL115XM93hIH4CSRln2w52SqvmY5hqdtauB6QFCDiZNQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"

"@babel/helper-optimise-call-expression@^7.24.7":
  "integrity" "sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A=="
  "resolved" "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-Rq76wjt7yz9AAc1KnlRKNAi/dMSVWgDRx43FHoJEbcYU6xOWaE2dVPwcdTukJrjxS65GITyfbvEYHvkirZ6uEg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.7.tgz"
  "version" "7.24.7"

"@babel/helper-remap-async-to-generator@^7.24.7":
  "integrity" "sha512-9pKLcTlZ92hNZMQfGCHImUpDOlAgkkpqalWEeftW5FBya75k8Li2ilerxkM/uBEj01iBZXcCIB/bwvDYgWyibA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-wrap-function" "^7.24.7"

"@babel/helper-replace-supers@^7.24.7":
  "integrity" "sha512-qTAxxBM81VEyoAY0TtLrx1oAEJc09ZK67Q9ljQToqCnA+55eNwCORaxlKyu+rNfX86o8OXRUSNUnrtsAZXM9sg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.7"
    "@babel/helper-optimise-call-expression" "^7.24.7"

"@babel/helper-simple-access@^7.24.7":
  "integrity" "sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.24.7":
  "integrity" "sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-split-export-declaration@^7.24.7":
  "integrity" "sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.24.7":
  "integrity" "sha512-7MbVt6xrwFQbunH2DNQsAP5sTGxfqQtErvBIvIMi6EQnbgUOuVYanvREcmFrOPhoXBrTtjhhP+lW+o5UfK+tDg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.7.tgz"
  "version" "7.24.7"

"@babel/helper-validator-identifier@^7.24.7":
  "integrity" "sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz"
  "version" "7.24.7"

"@babel/helper-validator-option@^7.23.5", "@babel/helper-validator-option@^7.24.7":
  "integrity" "sha512-yy1/KvjhV/ZCL+SM7hBrvnZJ3ZuT9OuZgIJAGpPEToANvc3iM6iDvBnRjtElWibHU6n8/LPR/EjX9EtIEYO3pw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.7.tgz"
  "version" "7.24.7"

"@babel/helper-wrap-function@^7.24.7":
  "integrity" "sha512-N9JIYk3TD+1vq/wn77YnJOqMtfWhNewNE+DJV4puD2X7Ew9J4JvrzrFDfTfyv5EgEXVy9/Wt8QiOErzEmv5Ifw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-function-name" "^7.24.7"
    "@babel/template" "^7.24.7"
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helpers@^7.23.7":
  "integrity" "sha512-NlmJJtvcw72yRJRcnCmGvSi+3jDEg8qFu3z0AFoymmzLx5ERVWyzd9kVXr7Th9/8yIJi2Zc6av4Tqz3wFs8QWg=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/highlight@^7.24.7":
  "integrity" "sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw=="
  "resolved" "https://registry.npmjs.org/@babel/highlight/-/highlight-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.7"
    "chalk" "^2.4.2"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.0.0"

"@babel/parser@^7.23.6", "@babel/parser@^7.24.7":
  "integrity" "sha512-9uUYRm6OqQrCqQdG1iCBwBPZgN8ciDBro2nIOFaiRz1/BCxaI7CNvQbDHvsArAC7Tw9Hda/B3U+6ui9u4HWXPw=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.24.7.tgz"
  "version" "7.24.7"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.23.3":
  "integrity" "sha512-unaQgZ/iRu/By6tsjMZzpeBZjChYfLYry6HrEXPoz3KmfF0sVBQ1l8zKMQ4xRGLWVsjuvB8nQfjNP/DcfEOCsg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.23.3":
  "integrity" "sha512-+izXIbke1T33mY4MSNnrqhPXDz01WYhEf3yF5NbnUtkiNnm+XBZJl3kNfoK6NKmYlz/D07+l2GWVK/QfDkNCuQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/plugin-transform-optional-chaining" "^7.24.7"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.23.7":
  "integrity" "sha512-utA4HuR6F4Vvcr+o4DnjL8fCOlgRFGbeeBEGNg3ZTrLFw6VWG5XmUrvcQ0FjIYMU2ST4XcR2Wsp7t9qOAPnxMg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  "integrity" "sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  "version" "7.21.0-placeholder-for-preset-env.2"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3", "@babel/plugin-syntax-dynamic-import@7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.23.3":
  "integrity" "sha512-Ec3NRUMoi8gskrkBe3fNmEQfxDvY8bgfQpz6jlk/41kX9eUjvpyqWU7PBP/pLAvMaSQjbMNKJmvX57jP+M6bPg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-import-attributes@^7.23.3":
  "integrity" "sha512-hbX+lKKeUMGihnK8nvKqmXBInriT3GVjzXKFriV3YC6APGxMbP8RZNFwy91+hocLXq90Mta+HshoB31802bb8A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-import-meta@^7.10.4":
  "integrity" "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  "integrity" "sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.23.3":
  "integrity" "sha512-Dt9LQs6iEY++gXUwY03DNFat5C2NbO48jj+j/bSAz6b3HgPs39qcPiYt77fDObIcFwj3/C2ICX9YMwGflUoSHQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-async-generator-functions@^7.23.7":
  "integrity" "sha512-o+iF77e3u7ZS4AoAuJvapz9Fm001PuD2V3Lp6OSE4FYQke+cSewYtnek+THqGRWyQloRCyvWL1OkyfNEl9vr/g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-remap-async-to-generator" "^7.24.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-transform-async-to-generator@^7.23.3":
  "integrity" "sha512-SQY01PcJfmQ+4Ash7NE+rpbLFbmqA2GPIgqzxfFTL4t1FKRq4zTms/7htKpoCUI9OcFYgzqfmCdH53s6/jn5fA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-remap-async-to-generator" "^7.24.7"

"@babel/plugin-transform-block-scoped-functions@^7.23.3":
  "integrity" "sha512-yO7RAz6EsVQDaBH18IDJcMB1HnrUn2FJ/Jslc/WtPPWcjhpUJXU/rjbwmluzp7v/ZzWcEhTMXELnnsz8djWDwQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-block-scoping@^7.23.4":
  "integrity" "sha512-Nd5CvgMbWc+oWzBsuaMcbwjJWAcp5qzrbg69SZdHSP7AMY0AbWFqFO0WTFCA1jxhMCwodRwvRec8k0QUbZk7RQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-class-properties@^7.23.3":
  "integrity" "sha512-vKbfawVYayKcSeSR5YYzzyXvsDFWU2mD8U5TFeXtbCPLFUqe7GyCgvO6XDHzje862ODrOwy6WCPmKeWHbCFJ4w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-class-static-block@^7.23.4":
  "integrity" "sha512-HMXK3WbBPpZQufbMG4B46A90PkuuhN9vBCb5T8+VAHqvAqvcLi+2cKoukcpmUYkszLhScU3l1iudhrks3DggRQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-transform-classes@^7.23.8":
  "integrity" "sha512-CFbbBigp8ln4FU6Bpy6g7sE8B/WmCmzvivzUC6xDAdWVsjYTXijpuuGJmYkAaoWAzcItGKT3IOAbxRItZ5HTjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-compilation-targets" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-replace-supers" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.23.3":
  "integrity" "sha512-25cS7v+707Gu6Ds2oY6tCkUwsJ9YIDbggd9+cu9jzzDgiNq7hR/8dkzxWfKWnTic26vsI3EsCXNd4iEB6e8esQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/template" "^7.24.7"

"@babel/plugin-transform-destructuring@^7.23.3":
  "integrity" "sha512-19eJO/8kdCQ9zISOf+SEUJM/bAUIsvY3YDnXZTupUCQ8LgrWnsG/gFB9dvXqdXnRXMAM8fvt7b0CBKQHNGy1mw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-dotall-regex@^7.23.3":
  "integrity" "sha512-ZOA3W+1RRTSWvyqcMJDLqbchh7U4NRGqwRfFSVbOLS/ePIP4vHB5e8T8eXcuqyN1QkgKyj5wuW0lcS85v4CrSw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-duplicate-keys@^7.23.3":
  "integrity" "sha512-JdYfXyCRihAe46jUIliuL2/s0x0wObgwwiGxw/UbgJBr20gQBThrokO4nYKgWkD7uBaqM7+9x5TU7NkExZJyzw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-dynamic-import@^7.23.4":
  "integrity" "sha512-sc3X26PhZQDb3JhORmakcbvkeInvxz+A8oda99lj7J60QRuPZvNAk9wQlTBS1ZynelDrDmTU4pw1tyc5d5ZMUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.23.3":
  "integrity" "sha512-Rqe/vSc9OYgDajNIK35u7ot+KeCoetqQYFXM4Epf7M7ez3lWlOjrDjrwMei6caCVhfdw+mIKD4cgdGNy5JQotQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-export-namespace-from@^7.23.4":
  "integrity" "sha512-v0K9uNYsPL3oXZ/7F9NNIbAj2jv1whUEtyA6aujhekLs56R++JDQuzRcP2/z4WX5Vg/c5lE9uWZA0/iUoFhLTA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-transform-for-of@^7.23.6":
  "integrity" "sha512-wo9ogrDG1ITTTBsy46oGiN1dS9A7MROBTcYsfS8DtsImMkHk9JXJ3EWQM6X2SUw4x80uGPlwj0o00Uoc6nEE3g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"

"@babel/plugin-transform-function-name@^7.23.3":
  "integrity" "sha512-U9FcnA821YoILngSmYkW6FjyQe2TyZD5pHt4EVIhmcTkrJw/3KqcrRSxuOo5tFZJi7TE19iDyI1u+weTI7bn2w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-compilation-targets" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-json-strings@^7.23.4":
  "integrity" "sha512-2yFnBGDvRuxAaE/f0vfBKvtnvvqU8tGpMHqMNpTN2oWMKIR3NqFkjaAgGwawhqK/pIN2T3XdjGPdaG0vDhOBGw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-transform-literals@^7.23.3":
  "integrity" "sha512-vcwCbb4HDH+hWi8Pqenwnjy+UiklO4Kt1vfspcQYFhJdpthSnW8XvWGyDZWKNVrVbVViI/S7K9PDJZiUmP2fYQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-logical-assignment-operators@^7.23.4":
  "integrity" "sha512-4D2tpwlQ1odXmTEIFWy9ELJcZHqrStlzK/dAOWYyxX3zT0iXQB6banjgeOJQXzEc4S0E0a5A+hahxPaEFYftsw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.23.3":
  "integrity" "sha512-T/hRC1uqrzXMKLQ6UCwMT85S3EvqaBXDGf0FaMf4446Qx9vKwlghvee0+uuZcDUCZU5RuNi4781UQ7R308zzBw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-modules-amd@^7.23.3":
  "integrity" "sha512-9+pB1qxV3vs/8Hdmz/CulFB8w2tuu6EB94JZFsjdqxQokwGa9Unap7Bo2gGBGIvPmDIVvQrom7r5m/TCDMURhg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-modules-commonjs@^7.23.3":
  "integrity" "sha512-iFI8GDxtevHJ/Z22J5xQpVqFLlMNstcLXh994xifFwxxGslr2ZXXLWgtBeLctOD63UFDArdvN6Tg8RFw+aEmjQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"

"@babel/plugin-transform-modules-systemjs@^7.23.3":
  "integrity" "sha512-GYQE0tW7YoaN13qFh3O1NCY4MPkUiAH3fiF7UcV/I3ajmDKEdG3l+UOcbAm4zUE3gnvUU+Eni7XrVKo9eO9auw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-hoist-variables" "^7.24.7"
    "@babel/helper-module-transforms" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"

"@babel/plugin-transform-modules-umd@^7.23.3":
  "integrity" "sha512-3aytQvqJ/h9z4g8AsKPLvD4Zqi2qT+L3j7XoFFu1XBlZWEl2/1kWnhmAbxpLgPrHSY0M6UA02jyTiwUVtiKR6A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-module-transforms" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.22.5":
  "integrity" "sha512-/jr7h/EWeJtk1U/uz2jlsCioHkZk1JJZVcc8oQsJ1dUlaJD83f4/6Zeh2aHt9BIFokHIsSeDfhUmju0+1GPd6g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-new-target@^7.23.3":
  "integrity" "sha512-RNKwfRIXg4Ls/8mMTza5oPF5RkOW8Wy/WgMAp1/F1yZ8mMbtwXW+HDoJiOsagWrAhI5f57Vncrmr9XeT4CVapA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-nullish-coalescing-operator@^7.23.4":
  "integrity" "sha512-Ts7xQVk1OEocqzm8rHMXHlxvsfZ0cEF2yomUqpKENHWMF4zKk175Y4q8H5knJes6PgYad50uuRmt3UJuhBw8pQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-transform-numeric-separator@^7.23.4":
  "integrity" "sha512-e6q1TiVUzvH9KRvicuxdBTUj4AdKSRwzIyFFnfnezpCfP2/7Qmbb8qbU2j7GODbl4JMkblitCQjKYUaX/qkkwA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-transform-object-rest-spread@^7.23.4", "@babel/plugin-transform-object-rest-spread@7.23.4":
  "integrity" "sha512-9x9K1YyeQVw0iOXJlIzwm8ltobIIv7j2iLyP2jIhEbqPRQ7ScNgwQufU2I0Gq11VjyG4gI4yMXt2VFags+1N3g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.23.4.tgz"
  "version" "7.23.4"
  dependencies:
    "@babel/compat-data" "^7.23.3"
    "@babel/helper-compilation-targets" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.23.3"

"@babel/plugin-transform-object-super@^7.23.3":
  "integrity" "sha512-A/vVLwN6lBrMFmMDmPPz0jnE6ZGx7Jq7d6sT/Ev4H65RER6pZ+kczlf1DthF5N0qaPHBsI7UXiE8Zy66nmAovg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-replace-supers" "^7.24.7"

"@babel/plugin-transform-optional-catch-binding@^7.23.4":
  "integrity" "sha512-uLEndKqP5BfBbC/5jTwPxLh9kqPWWgzN/f8w6UwAIirAEqiIVJWWY312X72Eub09g5KF9+Zn7+hT7sDxmhRuKA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-transform-optional-chaining@^7.23.4", "@babel/plugin-transform-optional-chaining@^7.24.7":
  "integrity" "sha512-tK+0N9yd4j+x/4hxF3F0e0fu/VdcxU18y5SevtyM/PCFlQvXbR0Zmlo2eBrKtVipGNFzpq56o8WsIIKcJFUCRQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-transform-parameters@^7.23.3":
  "integrity" "sha512-yGWW5Rr+sQOhK0Ot8hjDJuxU3XLRQGflvT4lhlSY0DFvdb3TwKaY26CJzHtYllU0vT9j58hc37ndFPsqT1SrzA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-private-methods@^7.23.3":
  "integrity" "sha512-COTCOkG2hn4JKGEKBADkA8WNb35TGkkRbI5iT845dB+NyqgO8Hn+ajPbSnIQznneJTa3d30scb6iz/DhH8GsJQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-private-property-in-object@^7.23.4":
  "integrity" "sha512-9z76mxwnwFxMyxZWEgdgECQglF2Q7cFLm0kMf8pGwt+GSJsY0cONKj/UuO4bOH0w/uAel3ekS4ra5CEAyJRmDA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.23.3":
  "integrity" "sha512-EMi4MLQSHfd2nrCqQEWxFdha2gBCqU4ZcCng4WBGZ5CJL4bBRW0ptdqqDdeirGZcpALazVVNJqRmsO8/+oNCBA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-regenerator@^7.23.3":
  "integrity" "sha512-lq3fvXPdimDrlg6LWBoqj+r/DEWgONuwjuOuQCSYgRroXDH/IdM1C0IZf59fL5cHLpjEH/O6opIRBbqv7ELnuA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "regenerator-transform" "^0.15.2"

"@babel/plugin-transform-reserved-words@^7.23.3":
  "integrity" "sha512-0DUq0pHcPKbjFZCfTss/pGkYMfy3vFWydkUBd9r0GHpIyfs2eCDENvqadMycRS9wZCXR41wucAfJHJmwA0UmoQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-runtime@7.23.7":
  "integrity" "sha512-fa0hnfmiXc9fq/weK34MUV0drz2pOL/vfKWvN7Qw127hiUPabFCUMgAbYWcchRzMJit4o5ARsK/s+5h0249pLw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.23.7.tgz"
  "version" "7.23.7"
  dependencies:
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "babel-plugin-polyfill-corejs2" "^0.4.7"
    "babel-plugin-polyfill-corejs3" "^0.8.7"
    "babel-plugin-polyfill-regenerator" "^0.5.4"
    "semver" "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.23.3":
  "integrity" "sha512-KsDsevZMDsigzbA09+vacnLpmPH4aWjcZjXdyFKGzpplxhbeB4wYtury3vglQkg6KM/xEPKt73eCjPPf1PgXBA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-spread@^7.23.3":
  "integrity" "sha512-x96oO0I09dgMDxJaANcRyD4ellXFLLiWhuwDxKZX5g2rWP1bTPkBSwCYv96VDXVT1bD9aPj8tppr5ITIh8hBng=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"

"@babel/plugin-transform-sticky-regex@^7.23.3":
  "integrity" "sha512-kHPSIJc9v24zEml5geKg9Mjx5ULpfncj0wRpYtxbvKyTtHCYDkVE3aHQ03FrpEo4gEe2vrJJS1Y9CJTaThA52g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-template-literals@^7.23.3":
  "integrity" "sha512-AfDTQmClklHCOLxtGoP7HkeMw56k1/bTQjwsfhL6pppo/M4TOBSq+jjBUBLmV/4oeFg4GWMavIl44ZeCtmmZTw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-typeof-symbol@^7.23.3":
  "integrity" "sha512-VtR8hDy7YLB7+Pet9IarXjg/zgCMSF+1mNS/EQEiEaUPoFXCVsHG64SIxcaaI2zJgRiv+YmgaQESUfWAdbjzgg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-unicode-escapes@^7.23.3":
  "integrity" "sha512-U3ap1gm5+4edc2Q/P+9VrBNhGkfnf+8ZqppY71Bo/pzZmXhhLdqgaUl6cuB07O1+AQJtCLfaOmswiNbSQ9ivhw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-unicode-property-regex@^7.23.3":
  "integrity" "sha512-uH2O4OV5M9FZYQrwc7NdVmMxQJOCCzFeYudlZSzUAHRFeOujQefa92E74TQDVskNHCzOXoigEuoyzHDhaEaK5w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-unicode-regex@^7.23.3":
  "integrity" "sha512-hlQ96MBZSAXUq7ltkjtu3FJCCSMx/j629ns3hA3pXnBXjanNP0LHi+JpPeA81zaWgVK1VGH95Xuy7u0RyQ8kMg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-unicode-sets-regex@^7.23.3":
  "integrity" "sha512-2G8aAvF4wy1w/AGZkemprdGMRg5o6zPNhbHVImRz3lss55TYCBd6xStN19rt8XJHq20sqV0JbyWjOWwQRwV/wg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/preset-env@7.23.8":
  "integrity" "sha512-lFlpmkApLkEP6woIKprO6DO60RImpatTQKtz4sUcDjVcK8M8mQ4sZsuxaTMNOZf0sqAq/ReYW1ZBHnOQwKpLWA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.23.8.tgz"
  "version" "7.23.8"
  dependencies:
    "@babel/compat-data" "^7.23.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.23.3"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.23.3"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.23.7"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.23.3"
    "@babel/plugin-syntax-import-attributes" "^7.23.3"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.23.3"
    "@babel/plugin-transform-async-generator-functions" "^7.23.7"
    "@babel/plugin-transform-async-to-generator" "^7.23.3"
    "@babel/plugin-transform-block-scoped-functions" "^7.23.3"
    "@babel/plugin-transform-block-scoping" "^7.23.4"
    "@babel/plugin-transform-class-properties" "^7.23.3"
    "@babel/plugin-transform-class-static-block" "^7.23.4"
    "@babel/plugin-transform-classes" "^7.23.8"
    "@babel/plugin-transform-computed-properties" "^7.23.3"
    "@babel/plugin-transform-destructuring" "^7.23.3"
    "@babel/plugin-transform-dotall-regex" "^7.23.3"
    "@babel/plugin-transform-duplicate-keys" "^7.23.3"
    "@babel/plugin-transform-dynamic-import" "^7.23.4"
    "@babel/plugin-transform-exponentiation-operator" "^7.23.3"
    "@babel/plugin-transform-export-namespace-from" "^7.23.4"
    "@babel/plugin-transform-for-of" "^7.23.6"
    "@babel/plugin-transform-function-name" "^7.23.3"
    "@babel/plugin-transform-json-strings" "^7.23.4"
    "@babel/plugin-transform-literals" "^7.23.3"
    "@babel/plugin-transform-logical-assignment-operators" "^7.23.4"
    "@babel/plugin-transform-member-expression-literals" "^7.23.3"
    "@babel/plugin-transform-modules-amd" "^7.23.3"
    "@babel/plugin-transform-modules-commonjs" "^7.23.3"
    "@babel/plugin-transform-modules-systemjs" "^7.23.3"
    "@babel/plugin-transform-modules-umd" "^7.23.3"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.22.5"
    "@babel/plugin-transform-new-target" "^7.23.3"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.23.4"
    "@babel/plugin-transform-numeric-separator" "^7.23.4"
    "@babel/plugin-transform-object-rest-spread" "^7.23.4"
    "@babel/plugin-transform-object-super" "^7.23.3"
    "@babel/plugin-transform-optional-catch-binding" "^7.23.4"
    "@babel/plugin-transform-optional-chaining" "^7.23.4"
    "@babel/plugin-transform-parameters" "^7.23.3"
    "@babel/plugin-transform-private-methods" "^7.23.3"
    "@babel/plugin-transform-private-property-in-object" "^7.23.4"
    "@babel/plugin-transform-property-literals" "^7.23.3"
    "@babel/plugin-transform-regenerator" "^7.23.3"
    "@babel/plugin-transform-reserved-words" "^7.23.3"
    "@babel/plugin-transform-shorthand-properties" "^7.23.3"
    "@babel/plugin-transform-spread" "^7.23.3"
    "@babel/plugin-transform-sticky-regex" "^7.23.3"
    "@babel/plugin-transform-template-literals" "^7.23.3"
    "@babel/plugin-transform-typeof-symbol" "^7.23.3"
    "@babel/plugin-transform-unicode-escapes" "^7.23.3"
    "@babel/plugin-transform-unicode-property-regex" "^7.23.3"
    "@babel/plugin-transform-unicode-regex" "^7.23.3"
    "@babel/plugin-transform-unicode-sets-regex" "^7.23.3"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    "babel-plugin-polyfill-corejs2" "^0.4.7"
    "babel-plugin-polyfill-corejs3" "^0.8.7"
    "babel-plugin-polyfill-regenerator" "^0.5.4"
    "core-js-compat" "^3.31.0"
    "semver" "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  "integrity" "sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  "version" "0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/regjsgen@^0.8.0":
  "integrity" "sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA=="
  "resolved" "https://registry.npmjs.org/@babel/regjsgen/-/regjsgen-0.8.0.tgz"
  "version" "0.8.0"

"@babel/runtime@^7.8.4":
  "integrity" "sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@babel/template@^7.22.15", "@babel/template@^7.24.7":
  "integrity" "sha512-jYqfPrU9JTF0PmPy1tLYHW4Mp4KlgxJD9l2nP9fD6yT/ICi554DmrWBAEYpIelzjHf1msDP3PxJIRt/nFNfBig=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/traverse@^7.23.7", "@babel/traverse@^7.24.7":
  "integrity" "sha512-yb65Ed5S/QAcewNPh0nZczy9JdYXkkAbIsEo+P7BE7yO3txAY30Y/oPa3QkQ5It3xVG2kpKMg9MsdxZaO31uKA=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-hoist-variables" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"
    "debug" "^4.3.1"
    "globals" "^11.1.0"

"@babel/types@^7.23.6", "@babel/types@^7.24.7", "@babel/types@^7.4.4":
  "integrity" "sha512-XEFXSlxiG5td2EJRe8vOmRbaXVgfcBlszKujvVmWIK/UpywWljQCfzAv3RQCGujWQ1RD4YYWEAqDXfuJiy8f5Q=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.24.7.tgz"
  "version" "7.24.7"
  dependencies:
    "@babel/helper-string-parser" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"
    "to-fast-properties" "^2.0.0"

"@esbuild/win32-x64@0.20.2":
  "integrity" "sha512-N49X4lJX27+l9jbLKSqZ6bKNjzQvHaT8IIFUy+YIqmXQdjYCToGWwOItDrfby14c78aDd5NHQl29xingXfCdLQ=="
  "resolved" "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.20.2.tgz"
  "version" "0.20.2"

"@eslint-community/eslint-utils@^4.2.0":
  "integrity" "sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA=="
  "resolved" "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "eslint-visitor-keys" "^3.3.0"

"@eslint-community/regexpp@^4.6.1":
  "integrity" "sha512-Zm2NGpWELsQAD1xsJzGQpYfvICSsFkEpU0jxBjfdC6uNEWXcHnfs9hScFWtXVDVl+rBQJGrl4g1vcKIejpH9dA=="
  "resolved" "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.1.tgz"
  "version" "4.10.1"

"@eslint/eslintrc@^2.1.4":
  "integrity" "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^9.6.0"
    "globals" "^13.19.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@8.57.0":
  "integrity" "sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g=="
  "resolved" "https://registry.npmjs.org/@eslint/js/-/js-8.57.0.tgz"
  "version" "8.57.0"

"@floating-ui/core@^1.0.0":
  "integrity" "sha512-+2XpQV9LLZeanU4ZevzRnGFg2neDeKHgFLjP6YLW+tly0IvrhqT4u8enLGjLH3qeh85g19xY5rsAusfwTdn5lg=="
  "resolved" "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/dom@^1.5.1":
  "integrity" "sha512-Nsdud2X65Dz+1RHjAIP0t8z5e2ff/IRbei6BqFrl1urT8sDVzM1HMQ+R0XcU5ceRfyO3I6ayeqIfh+6Wb8LGTw=="
  "resolved" "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.5.tgz"
  "version" "1.6.5"
  dependencies:
    "@floating-ui/core" "^1.0.0"
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/utils@^0.2.0":
  "integrity" "sha512-J4yDIIthosAsRZ5CPYP/jQvUAQtlZTTD/4suA08/FEnlxqW3sKS9iAhgsa9VYLZ6vDHn/ixJgIqRQPotoBjxIw=="
  "resolved" "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.2.tgz"
  "version" "0.2.2"

"@foliojs-fork/fontkit@^1.9.1":
  "integrity" "sha512-IfB5EiIb+GZk+77TRB86AHroVaqfq8JRFlUbz0WEwsInyCG0epX2tCPOy+UfaWPju30DeVoUAXfzWXmhn753KA=="
  "resolved" "https://registry.npmjs.org/@foliojs-fork/fontkit/-/fontkit-1.9.2.tgz"
  "version" "1.9.2"
  dependencies:
    "@foliojs-fork/restructure" "^2.0.2"
    "brotli" "^1.2.0"
    "clone" "^1.0.4"
    "deep-equal" "^1.0.0"
    "dfa" "^1.2.0"
    "tiny-inflate" "^1.0.2"
    "unicode-properties" "^1.2.2"
    "unicode-trie" "^2.0.0"

"@foliojs-fork/linebreak@^1.1.1":
  "integrity" "sha512-ZPohpxxbuKNE0l/5iBJnOAfUaMACwvUIKCvqtWGKIMv1lPYoNjYXRfhi9FeeV9McBkBLxsMFWTVVhHJA8cyzvg=="
  "resolved" "https://registry.npmjs.org/@foliojs-fork/linebreak/-/linebreak-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "base64-js" "1.3.1"
    "unicode-trie" "^2.0.0"

"@foliojs-fork/pdfkit@^0.14.0":
  "integrity" "sha512-nMOiQAv6id89MT3tVTCgc7HxD5ZMANwio2o5yvs5sexQkC0KI3BLaLakpsrHmFfeGFAhqPmZATZGbJGXTUebpg=="
  "resolved" "https://registry.npmjs.org/@foliojs-fork/pdfkit/-/pdfkit-0.14.0.tgz"
  "version" "0.14.0"
  dependencies:
    "@foliojs-fork/fontkit" "^1.9.1"
    "@foliojs-fork/linebreak" "^1.1.1"
    "crypto-js" "^4.2.0"
    "png-js" "^1.0.0"

"@foliojs-fork/restructure@^2.0.2":
  "integrity" "sha512-59SgoZ3EXbkfSX7b63tsou/SDGzwUEK6MuB5sKqgVK1/XE0fxmpsOb9DQI8LXW3KfGnAjImCGhhEb7uPPAUVNA=="
  "resolved" "https://registry.npmjs.org/@foliojs-fork/restructure/-/restructure-2.0.2.tgz"
  "version" "2.0.2"

"@form-validation/bundle@2.4.0":
  "integrity" "sha512-f2A4MlUo9UySgQg27TOVLEV2kKSQOhBuB/2Pc6VyxNtFz5JPEXPSuQS9tIaSwX89MYaQsW3ps1V6Q8ByqJfd6A=="
  "resolved" "https://registry.npmjs.org/@form-validation/bundle/-/bundle-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-alias" "2.4.0"
    "@form-validation/plugin-aria" "2.4.0"
    "@form-validation/plugin-declarative" "2.4.0"
    "@form-validation/plugin-default-submit" "2.4.0"
    "@form-validation/plugin-dependency" "2.4.0"
    "@form-validation/plugin-excluded" "2.4.0"
    "@form-validation/plugin-field-status" "2.4.0"
    "@form-validation/plugin-framework" "2.4.0"
    "@form-validation/plugin-icon" "2.4.0"
    "@form-validation/plugin-message" "2.4.0"
    "@form-validation/plugin-sequence" "2.4.0"
    "@form-validation/plugin-submit-button" "2.4.0"
    "@form-validation/plugin-tooltip" "2.4.0"
    "@form-validation/plugin-trigger" "2.4.0"
    "@form-validation/validator-base64" "2.4.0"
    "@form-validation/validator-between" "2.4.0"
    "@form-validation/validator-bic" "2.4.0"
    "@form-validation/validator-blank" "2.4.0"
    "@form-validation/validator-callback" "2.4.0"
    "@form-validation/validator-choice" "2.4.0"
    "@form-validation/validator-color" "2.4.0"
    "@form-validation/validator-credit-card" "2.4.0"
    "@form-validation/validator-cusip" "2.4.0"
    "@form-validation/validator-date" "2.4.0"
    "@form-validation/validator-different" "2.4.0"
    "@form-validation/validator-digits" "2.4.0"
    "@form-validation/validator-ean" "2.4.0"
    "@form-validation/validator-ein" "2.4.0"
    "@form-validation/validator-email-address" "2.4.0"
    "@form-validation/validator-file" "2.4.0"
    "@form-validation/validator-greater-than" "2.4.0"
    "@form-validation/validator-grid" "2.4.0"
    "@form-validation/validator-hex" "2.4.0"
    "@form-validation/validator-iban" "2.4.0"
    "@form-validation/validator-id" "2.4.0"
    "@form-validation/validator-identical" "2.4.0"
    "@form-validation/validator-imei" "2.4.0"
    "@form-validation/validator-imo" "2.4.0"
    "@form-validation/validator-integer" "2.4.0"
    "@form-validation/validator-ip" "2.4.0"
    "@form-validation/validator-isbn" "2.4.0"
    "@form-validation/validator-isin" "2.4.0"
    "@form-validation/validator-ismn" "2.4.0"
    "@form-validation/validator-issn" "2.4.0"
    "@form-validation/validator-less-than" "2.4.0"
    "@form-validation/validator-mac" "2.4.0"
    "@form-validation/validator-meid" "2.4.0"
    "@form-validation/validator-not-empty" "2.4.0"
    "@form-validation/validator-numeric" "2.4.0"
    "@form-validation/validator-phone" "2.4.0"
    "@form-validation/validator-promise" "2.4.0"
    "@form-validation/validator-regexp" "2.4.0"
    "@form-validation/validator-remote" "2.4.0"
    "@form-validation/validator-rtn" "2.4.0"
    "@form-validation/validator-sedol" "2.4.0"
    "@form-validation/validator-siren" "2.4.0"
    "@form-validation/validator-siret" "2.4.0"
    "@form-validation/validator-step" "2.4.0"
    "@form-validation/validator-string-case" "2.4.0"
    "@form-validation/validator-string-length" "2.4.0"
    "@form-validation/validator-uri" "2.4.0"
    "@form-validation/validator-uuid" "2.4.0"
    "@form-validation/validator-vat" "2.4.0"
    "@form-validation/validator-vin" "2.4.0"
    "@form-validation/validator-zip-code" "2.4.0"

"@form-validation/core@2.4.0":
  "integrity" "sha512-uZxBCtgJMt6iiW9T/tWtlBQbbbvysdHayzZxD424NFqozMOXqPYeA3xh8TH7bXh0vvpYc0+YGHl2LwA68tFd1w=="
  "resolved" "https://registry.npmjs.org/@form-validation/core/-/core-2.4.0.tgz"
  "version" "2.4.0"

"@form-validation/plugin-alias@2.4.0":
  "integrity" "sha512-9xVOtjRVp0qy0IwPUFgTJeSOa5iexN4Q0WAREZIYmYZ4NP31fOfMntIaDICRif2BjWLRuCd9OUp3x+WIETQ/7Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-alias/-/plugin-alias-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-aria@2.4.0":
  "integrity" "sha512-5sCyH65WAvoixgyvtYxMwuRz/s4Rl1slDvAOd1t14/CSMhBv+DmnapLI77w1Mw+K2kJZgFiXJ+X8K6NL0/sigA=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-aria/-/plugin-aria-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-message" "2.4.0"

"@form-validation/plugin-auto-focus@2.4.0":
  "integrity" "sha512-v5ExZFacTU2Ob+7V3FNiZErzbxH9i8I0dmWoPqdfOJRQtcvBs3JD6gtmOa52uMeOW6Ldz4YkVvTS0ub9m/RgjQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-auto-focus/-/plugin-auto-focus-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-field-status" "2.4.0"

"@form-validation/plugin-bootstrap5@2.4.0":
  "integrity" "sha512-l/SM3J8478OaR9xSRRrNi4r+K3lJwEw1yNCKczIvTMgxCfVNpndsAH81jMkZLRDbcN8o152s7Q2Ey9vb4Tqxzw=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-bootstrap5/-/plugin-bootstrap5-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-framework" "2.4.0"
    "@form-validation/plugin-icon" "2.4.0"
    "@form-validation/plugin-message" "2.4.0"

"@form-validation/plugin-declarative@2.4.0":
  "integrity" "sha512-qASwD731hbFv10hVse2wUkbSaamJKvEbzCZfscRGbnfwGeCuNLO9NHgn26+uOgtsC3ooDvW/1KP2xPYlhvfhTQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-declarative/-/plugin-declarative-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-default-submit@2.4.0":
  "integrity" "sha512-EX4Mv3KDLISW7GeUpsHon40mvERm7E7BOtikQqvAQH2byBJlPsQ7pkUGcY5fNGNrpLmvRD9q8tS1QFdL/gD0uA=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-default-submit/-/plugin-default-submit-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-dependency@2.4.0":
  "integrity" "sha512-L7MVuQHOLs/K5TN6NFLmfdGFfUXlvMDMukpi0Cl8UD1XA+0pC0h7hM87WiJiGTzmW6Bc3fdPVK08iwtLdpq3EA=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-dependency/-/plugin-dependency-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-trigger" "2.4.0"

"@form-validation/plugin-excluded@2.4.0":
  "integrity" "sha512-QBZt7Xy2IWEKH+8pZN1RRHB+UWj3UHwM1ojnKenrz5ZF6/8kyw1+cVaOpiAGYpg2bS2qHgmHfZXHiF/ZzZYRig=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-excluded/-/plugin-excluded-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-field-status@2.4.0":
  "integrity" "sha512-h+Mn4fLbZ6hYxkZiZOpR8HL8OgIM9vxAkRhTv49M97BPevArSrBUfZehaoCHIi0JYM8gI1zc/rWKACIo3WipzA=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-field-status/-/plugin-field-status-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-framework@2.4.0":
  "integrity" "sha512-h6ABODOQCaFu/snhBHyJSD1Z8jvkpF68mCFJjyT9nEqGrZQyUCI5C0oskXVAa2xL/FJ2dVEzWwYfBZ8TsZuh4w=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-framework/-/plugin-framework-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-icon" "2.4.0"
    "@form-validation/plugin-message" "2.4.0"

"@form-validation/plugin-icon@2.4.0":
  "integrity" "sha512-Et5y/9c2heg7RtwchdVvWw2b+ylkCl+vUlsrK02R5Fr3blVqgWWeQbyZVNf7G/dabG1PbYfTr/db3DO+ebafQw=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-icon/-/plugin-icon-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-message@2.4.0":
  "integrity" "sha512-lZyEjlYFT3Ec9IuRCoXzPWeljdYy6AVNr2xCHRIjZMlhVrCfinlLFAOjUniJC92eWCRaLCVtSoMMEMuRBOfS2A=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-message/-/plugin-message-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-sequence@2.4.0":
  "integrity" "sha512-SfTS4+cEkWstGE8yCBkT0AKm4FqmXO73B76L1FIq7UUAWe3y91Qbnf8ukieNpvV1l9uvldgr++YpXN4Ce72hIw=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-sequence/-/plugin-sequence-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-submit-button@2.4.0":
  "integrity" "sha512-czOxp8mB1aZ8xo9Bsh8GMIgXtdREeGuOfhgZ4fQZJ06z1Wbm5VUi4SJkeHqOEaGkl4hZUx15h9Im3O947HzDIA=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-submit-button/-/plugin-submit-button-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/plugin-tooltip@2.4.0":
  "integrity" "sha512-HFrx0MpJhPNP8YSvbap8sP2Bmj7V6hotNBg9BTcUoHt6fixiHpRJFVfthddmTsHOooJrTKikUD93lN2nNtvS3A=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-tooltip/-/plugin-tooltip-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"
    "@form-validation/plugin-icon" "2.4.0"

"@form-validation/plugin-trigger@2.4.0":
  "integrity" "sha512-hIaZpj3cSF6+bYxzLW3N7ePkGf52D4QnqgiBd9Q0MMr9JD2qrYWpxFXUzTRuM/qRCrqxclWbZqlIK+bc4ruQew=="
  "resolved" "https://registry.npmjs.org/@form-validation/plugin-trigger/-/plugin-trigger-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-base64@2.4.0":
  "integrity" "sha512-tnwr97xmP0IKmsCvyjBFO+rtJin+Pswr99Jura6WBz8xfM22hiiSS/3VhWA3UKNl+oVABq/GQGyMkBoaxyyjtA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-base64/-/validator-base64-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-between@2.4.0":
  "integrity" "sha512-YO0ZaXBi3qEcPPbBvGbpTxELg2y4FlK3a1mi0at/9fgOK1MgpBr70UEIOkdacO8+TbRELyz6GSsQPf1XFupV2g=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-between/-/validator-between-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-bic@2.4.0":
  "integrity" "sha512-3c12u4ooFASe9Vu+ArWFz1n5d3I6OugKknGzNPQrPaGwy3wcl/ZL2upqHk9Y83YCAfnvTgf1mosGJR5Od7qb8Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-bic/-/validator-bic-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-blank@2.4.0":
  "integrity" "sha512-hQDGnlLcL8byX1RMPoL2pYkwUgUDk2SBKb/vGBnHYnHdYQzcCQCOvkHu8rlsbtTPzO0MjukmrjADhkWd+vhAgw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-blank/-/validator-blank-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-callback@2.4.0":
  "integrity" "sha512-yI4WUVbVPzVOA+Nmr65G7FJ+sEI6vyDaEKg37DE8oBl8sF2m1na2591MJfZ0ysZZ5B9bZ81pZftIsu7WKWdvdw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-callback/-/validator-callback-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-choice@2.4.0":
  "integrity" "sha512-lLE7Wcz3hHaCYgGZf7hHXD+lWDChozSUmDv/TfclTC+nfY7BMGgD7mCa9jXM1+OUmkGeikcbDQgTTTEygB82gQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-choice/-/validator-choice-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-color@2.4.0":
  "integrity" "sha512-VY2l7Pjxfr4B6g9MIjJMp2iqe0jlPhvBqK3ZDb0ltPCH1cJ4uNtCHSNSyllkCLP1NTf/tLPh5Bf9awOLWGNAMg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-color/-/validator-color-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-credit-card@2.4.0":
  "integrity" "sha512-nmcxe5WysG0V1fFWju9UEmcVQS+lqaUOID/Qf+C5Lp1BJr6gdBk3eQ3F18wioZqtDiw+bCyfJPJv8gXcfYJn1Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-credit-card/-/validator-credit-card-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-cusip@2.4.0":
  "integrity" "sha512-C81z/SmX0be5NmYE/jLpSKAJkAWjk4wXAZlKmeFMMmzXR0rdbcZPN/9K3kEp9dxuSdbq189sexdsJewb6MYrSg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-cusip/-/validator-cusip-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-date@2.4.0":
  "integrity" "sha512-s5X2vvGrZL1X5Nfb7CfvR9RUfNo0Q2gGkiJLMg786MFxjgSbAzpRU7NqaiCYKpq0GqXwWA+d/k2zBJQe4LKlOA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-date/-/validator-date-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-different@2.4.0":
  "integrity" "sha512-wzk3YCEsTNITa7wIZjOCM41eUDcBNYHb2TcryjrcPr+8MsPwgfiKWVQz1tPGLEyZpVkhC86RAmGgaTPH4k3EBg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-different/-/validator-different-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-digits@2.4.0":
  "integrity" "sha512-2GmA6yxFZhAtXrg7bw6OoABtSqgZWVDTxflBmpIjkuaW4g5LaWCVHgmzRQ6/e7KzmBnUZ+JDTKw+HMME03yllQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-digits/-/validator-digits-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-ean@2.4.0":
  "integrity" "sha512-6KxcTrqo4s+gxUnDwe3WAgzVYzVj4efXjphrm55mGCnQTH6TA+Ua9Xlo+d9sF3z2r7+c1qGyGgPudNES7K7Y7g=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-ean/-/validator-ean-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-ein@2.4.0":
  "integrity" "sha512-+OkUwqHXyRC4JS73RYzE/BAP63/iYMgmDOgSfGMxY7BSkw53dBe1n+wF9mkhnOvgzRTjdgL/w7zN7bZOQ1cW0Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-ein/-/validator-ein-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-email-address@2.4.0":
  "integrity" "sha512-dqIonCT0dvAqhxoHOwYTYwHigtkTcrKWhEKmNFBjKmtjeZVS++xOugAcTD6D4wM3pzl7MufN0DAr6XqYfDGj/Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-email-address/-/validator-email-address-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-file@2.4.0":
  "integrity" "sha512-37QZEkxhzcj04kzk71QsSzwGfXZyfJXB4eNWZOLVoRX/+MVw0Vvjkc9iHieZZpO1VwbotcnmQqHNTy2Cl9mzZg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-file/-/validator-file-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-greater-than@2.4.0":
  "integrity" "sha512-9fjb9cqE15HFKnTidc2vBUMsfaKQg502qvP2RQjQ+NOM7j3OIN8lZABvUTnwvTXjegu3N12g0cfSvrcxWK9vMA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-greater-than/-/validator-greater-than-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-grid@2.4.0":
  "integrity" "sha512-eboq7B3VD7xbTTtq9g+tBKiy8VkqwFSgOmlO6LuWT0Iind947hugzb+CACuh6fxY2eDAEXwWbza+j1u8fvx1Vw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-grid/-/validator-grid-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-hex@2.4.0":
  "integrity" "sha512-JAWs+so0KBC3yHOaP1VrZby8j+auyXpuMVUiIe/h+1nwFbVjMgFXbdXw2/jryloVwzEfiplpFCRFC0o0A0d6qw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-hex/-/validator-hex-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-iban@2.4.0":
  "integrity" "sha512-Hmuaz7V9yGflhntdUoXnRWf1qkFTYomM/54BdvBP1kNzWZemIwaAUQZuYGP/8pwJw2kpaziPvHMH9VBcH2ld7Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-iban/-/validator-iban-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-id@2.4.0":
  "integrity" "sha512-V/qN006ZwEOddxKH+Xqz9VFCTdIJKF01uccZBSjAUba4rTK8ZNWPituQTto0FGHo9RyyyUhQt5gXmszXjQ7lHQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-id/-/validator-id-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-identical@2.4.0":
  "integrity" "sha512-qaxil+Kk8096PbxTJGMgqzgWnrQI3NnIu1C14/8+lQdhlGGr0VFboAkB2RN5gN1QPUlTuqE3OBaAxadI5ylYpQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-identical/-/validator-identical-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-imei@2.4.0":
  "integrity" "sha512-EYI7A/eQEtgkoB03XqZdwnJYT6VbHFXaf6JCmCDbDFpn+NVDu6zy0dzbyEgH9RB+VVqJ8wmsb11Ixx/lRAt22Q=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-imei/-/validator-imei-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-imo@2.4.0":
  "integrity" "sha512-r+JBdOTMGM7VnLuN2dz/Nnh1UH/g4ikt1eJtWgQ3iaCZ8naMltgiEE3DLI0CHKg6iKPjzVxNJRBv4a3ljTw57g=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-imo/-/validator-imo-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-integer@2.4.0":
  "integrity" "sha512-DajeIf6M1tAc/GEVxqV6iCXN5TqS2rZQnJUpZhfXb7aD9WrrWATtPSoWRyaa9AUqIvKdEqb2l8zySZz+dperNg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-integer/-/validator-integer-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-ip@2.4.0":
  "integrity" "sha512-FlE9sEoeT0Xm2JNy93GgykMb69985uZRvh/swmnEJds2kbZOnt970g/+J23y4b6zbvRUpr+OeM/zOsELi6M5YQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-ip/-/validator-ip-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-isbn@2.4.0":
  "integrity" "sha512-sPnM2vmTen10t/84GOuhmY2TNvX818hOuFdhWjn+CU8oVqJHM3u5vwA2OoLFV7d/qD+O1nIhp4WLIrXo+bTY9w=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-isbn/-/validator-isbn-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-isin@2.4.0":
  "integrity" "sha512-9/IyO9SMuSlQURARdOLvVakJVfTUshGgYBG9R8UlqzWuaiFkQcj4Y3EFKprdRdDRp9uCuFORPKAG1mmzrbR7jQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-isin/-/validator-isin-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-ismn@2.4.0":
  "integrity" "sha512-BR+vQ60qZryjli1L54uauuALg8i8bZ+BUu1rxxEWWfgD0jas0HPlxF/WucRpAlNrVFseYESKOTczH3wOQnrglQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-ismn/-/validator-ismn-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-issn@2.4.0":
  "integrity" "sha512-kSjT1IsO22ALrKLDlvzfHkQpZjf84brZhJ//rQnUiRW4P2EUZPZhLxM3cSWyhswV8IjZ6wAMN0SezVzgHsOGpA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-issn/-/validator-issn-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-less-than@2.4.0":
  "integrity" "sha512-sXiTNBTEGxFBU22uA4zabq7icr0CxOznEiPtAWwN9xhdO2E5kVZdCH1JLhS98iby4muNr0mZ5IfU0ZNK1jBxkA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-less-than/-/validator-less-than-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-mac@2.4.0":
  "integrity" "sha512-qIHmraPAhZX59+1SzC2KD7+ZTbf8NhY6rlOKoJtcQf/0gP4S9VIUdAVQjHHu2i/xd6/V2wuNISp2rvDd/3ipKw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-mac/-/validator-mac-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-meid@2.4.0":
  "integrity" "sha512-0Cko6R4znDba334rEsz51JuhTs7MmkDBBwYXSzx5DYeSS/WV2gv8FEeuBERC4d0H3JUrzaqattFPcFE2KWEn2A=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-meid/-/validator-meid-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-not-empty@2.4.0":
  "integrity" "sha512-gTysbflHrO01Wx1q0IjCnXLS/LZFP4KvSDOnZrI2Yq1H93vt9VFzME9Wcq0W/u9AmnYlKuLbHm4/LieIeW3oJw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-not-empty/-/validator-not-empty-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-numeric@2.4.0":
  "integrity" "sha512-VlIAfspBGMxfHpaI16W/Y6QE8+PJfAdUeoAODfNaspWMd+w/ty7YDOqqumpm2C1ASnZdDXhl4lCApf43H78wuQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-numeric/-/validator-numeric-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-phone@2.4.0":
  "integrity" "sha512-2SdDbvJyRtvKnkrx8JwIvWr162EEXkCKr0FfBabrkeeq6Th0PoiyzbteL1CNWx+zcmxH3Q9/vTy9BD/j4UeqDA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-phone/-/validator-phone-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-promise@2.4.0":
  "integrity" "sha512-w7x1OtGq77PJCtrWlFF2tyngHqiL2JFvs3DKSlUxAoq2cFFV9jQaxdN2bbJFmZbxN0U6Dxm1V4Gd7qGoYTeUZA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-promise/-/validator-promise-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-regexp@2.4.0":
  "integrity" "sha512-cQD991hQwPMAM84iwAr54A6AUfZgsX4F/HGpai2ZNu/srrmQcYlN/ogd43bCSNbJG8Ifa5P39Gh1Df8OttaY+g=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-regexp/-/validator-regexp-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-remote@2.4.0":
  "integrity" "sha512-Z83ilqm12IDFioviCBu3VMh6QvZX6Sw1/wcI5W8kiCRLjnmvkWE5f2F4MTxvGV14qM32xTvJon2jHcx3dNHQMg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-remote/-/validator-remote-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-rtn@2.4.0":
  "integrity" "sha512-Q5/jlCkFwvfRrxngPI4+tZq4ZhwB6k0gsx25vW8w9FqNpcPlgFnj41Pugq00rVrIUuOvQkwjO3ZKGAvsS9qELA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-rtn/-/validator-rtn-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-sedol@2.4.0":
  "integrity" "sha512-ZzB1qS9HwzVWc7EYZzmmn8h2xh74S84oKP+ylTEXXroeh/4uAFQ/9EISSHAFPBBjDZRw54f9wR5FaxbF/7cO7w=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-sedol/-/validator-sedol-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-siren@2.4.0":
  "integrity" "sha512-QX6QAgz1fSMLmgHj/UGVjQnRcMGmwygn4TSN2n+J1la7R1KLVL4/X5uD7zV5oHOAEA4xFy78LWNkq239Jjfu3A=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-siren/-/validator-siren-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-siret@2.4.0":
  "integrity" "sha512-YQNOQWKIUB92M9CFMEM/eaaoGI//g03bBxmDreSqoMpog795ufAnxNh/0nNkJ5itncJLeIRCQEGGiCBSlaIUlA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-siret/-/validator-siret-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-step@2.4.0":
  "integrity" "sha512-KyJCub99m62VPSj2G8OF5ZvNsz4juVlGOPgcYPH8b67eHnizH+yCoTKILfHnzU1SObkW1NH9fOMwekwS1Ln0Ug=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-step/-/validator-step-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-string-case@2.4.0":
  "integrity" "sha512-4256rG3Kvk/zg3IeIknqfGBQawM/t1/WCCBYsyrQZKleREcMq53ay8x8DNBeRyINzwjMQoAhAtBhIfCwrcWOrA=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-string-case/-/validator-string-case-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-string-length@2.4.0":
  "integrity" "sha512-zKQZzCz9wA6RxpiTdPpBkqITscYkGcsaTxDx/yV6phxFp6qGzPvPAszuNycOkYhgnzh70jOUPmTzyLSzai08fw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-string-length/-/validator-string-length-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-uri@2.4.0":
  "integrity" "sha512-xBfCdXg6X3YVNNvd+VINkb/IDch7nsaQHZfEnkt2nuxQa0Dy66mvvcNpcnpGBl+89/4eQy34+uk/cSctjhZe6w=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-uri/-/validator-uri-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-uuid@2.4.0":
  "integrity" "sha512-2uCt9Nds65AE21cWq8j5yc36/6COBEk3LkK9WpOYN3TVL4TbfeWfR5/ITzoAdxoEFXKvvYy9S4i7Wgq0b8hTOw=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-uuid/-/validator-uuid-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-vat@2.4.0":
  "integrity" "sha512-YV7v00kt2tK7EwcqS+0jWZha2s0CpL0MS+fVnMdqacmPTyvFY3gpea8ImeZVLY9w6WHf0k2NXN6QMGTJhhALRg=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-vat/-/validator-vat-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-vin@2.4.0":
  "integrity" "sha512-YLys8PjftSMH3G2iGmH+HA40wIkBPnDwzmpoa1CKUf4YIVyjwy7q1q/ezajd+odmm5qvws8r9zIs77/GQvCXug=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-vin/-/validator-vin-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@form-validation/validator-zip-code@2.4.0":
  "integrity" "sha512-4wz7cMLCGZpguixtoWdsIr94s1y7obk2f9Yn5UhJ4ag4gl7ZaEI5Z159QoCnbZyTviqxsM79TZ049be1PtbSjQ=="
  "resolved" "https://registry.npmjs.org/@form-validation/validator-zip-code/-/validator-zip-code-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@form-validation/core" "2.4.0"

"@fortawesome/fontawesome-free@6.5.2":
  "integrity" "sha512-hRILoInAx8GNT5IMkrtIt9blOdrqHOnPBH+k70aWUAqPZPgopb9G5EQJFpaBx/S8zp2fC+mPW349Bziuk1o28Q=="
  "resolved" "https://registry.npmjs.org/@fortawesome/fontawesome-free/-/fontawesome-free-6.5.2.tgz"
  "version" "6.5.2"

"@fullcalendar/core@~6.1.14", "@fullcalendar/core@6.1.14":
  "integrity" "sha512-hIPRBevm0aMc2aHy1hRIJgXmI1QTvQM1neQa9oxtuqUmF1+ApYC3oAdwcQMTuI7lHHw3pKJDyJFkKLPPnL6HXA=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/core/-/core-6.1.14.tgz"
  "version" "6.1.14"
  dependencies:
    "preact" "~10.12.1"

"@fullcalendar/daygrid@~6.1.14", "@fullcalendar/daygrid@6.1.14":
  "integrity" "sha512-DSyjiA1dEM8k3bOCrZpZOmAOZu71KGtH02ze+4QKuhxkmn/zQghmmLRdfzpOrcyJg6xGKkoB4pBcO+2lXar8XQ=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/daygrid/-/daygrid-6.1.14.tgz"
  "version" "6.1.14"

"@fullcalendar/interaction@6.1.14":
  "integrity" "sha512-rXum5XCjq+WEPNctFeYL/JKZGeU2rlxrElygocdMegcrIBJQW5hnWWVE+i4/1dOmUKF80CbGVlXUyYXoqK2eFg=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/interaction/-/interaction-6.1.14.tgz"
  "version" "6.1.14"

"@fullcalendar/list@6.1.14":
  "integrity" "sha512-eV0/6iCumYfvlPzIUTAONWH17/JlQCyCChUz8m06L4E/sOiNjkHGz8vlVTmZKqXzx9oWOOyV/Nm3pCtHmVZh+Q=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/list/-/list-6.1.14.tgz"
  "version" "6.1.14"

"@fullcalendar/premium-common@~6.1.14":
  "integrity" "sha512-Fw8GZ6mjZtv6toliSr3iHwIqLIjx3+7fdd828OO4LGzX1wcnCd74CfWqR1tvg+9YLUKmRXNEGgQaN4y5j/XpKg=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/premium-common/-/premium-common-6.1.14.tgz"
  "version" "6.1.14"

"@fullcalendar/scrollgrid@~6.1.14":
  "integrity" "sha512-LVLdjMgzf0uDNWzB7GWOrZAGF61pa/J0ipJHfG3BTO5Ri5qZbOHpVcPuW94LOFnaGZpbaOG2YQlKygrujAAbvA=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/scrollgrid/-/scrollgrid-6.1.14.tgz"
  "version" "6.1.14"
  dependencies:
    "@fullcalendar/premium-common" "~6.1.14"

"@fullcalendar/timegrid@6.1.14":
  "integrity" "sha512-ZByc3BVAtxWSVfyaNedROLlg/Tb2NQ43+MZZAfBSrVwVm2xyfQ+Bsx3pJyCXsRsUh2TFFTO07q7nMWe0jet3KQ=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/timegrid/-/timegrid-6.1.14.tgz"
  "version" "6.1.14"
  dependencies:
    "@fullcalendar/daygrid" "~6.1.14"

"@fullcalendar/timeline@6.1.14":
  "integrity" "sha512-5UvugmoJsXeinrHwH57hMgFWh77KagWtxOkrSL5DwaRj4DTb2loV/pkYD5GJaMjpsZqZcUW0V8kICUzNbkZXag=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/timeline/-/timeline-6.1.14.tgz"
  "version" "6.1.14"
  dependencies:
    "@fullcalendar/premium-common" "~6.1.14"
    "@fullcalendar/scrollgrid" "~6.1.14"

"@humanwhocodes/config-array@^0.11.14":
  "integrity" "sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.14.tgz"
  "version" "0.11.14"
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    "debug" "^4.3.1"
    "minimatch" "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/object-schema@^2.0.2":
  "integrity" "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
  "version" "2.0.3"

"@isaacs/cliui@^8.0.2":
  "integrity" "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA=="
  "resolved" "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "string-width" "^5.1.2"
    "string-width-cjs" "npm:string-width@^4.2.0"
    "strip-ansi" "^7.0.1"
    "strip-ansi-cjs" "npm:strip-ansi@^6.0.1"
    "wrap-ansi" "^8.1.0"
    "wrap-ansi-cjs" "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/set-array@^1.2.1":
  "integrity" "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="
  "resolved" "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/source-map@^0.3.3":
  "integrity" "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  "integrity" "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  "integrity" "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@kurkle/color@^0.3.0":
  "integrity" "sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw=="
  "resolved" "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz"
  "version" "0.3.2"

"@mapbox/geojson-rewind@^0.5.2":
  "integrity" "sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA=="
  "resolved" "https://registry.npmjs.org/@mapbox/geojson-rewind/-/geojson-rewind-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "get-stream" "^6.0.1"
    "minimist" "^1.2.6"

"@mapbox/jsonlint-lines-primitives@^2.0.2":
  "integrity" "sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ=="
  "resolved" "https://registry.npmjs.org/@mapbox/jsonlint-lines-primitives/-/jsonlint-lines-primitives-2.0.2.tgz"
  "version" "2.0.2"

"@mapbox/mapbox-gl-supported@^2.0.1":
  "integrity" "sha512-HP6XvfNIzfoMVfyGjBckjiAOQK9WfX0ywdLubuPMPv+Vqf5fj0uCbgBQYpiqcWZT6cbyyRnTSXDheT1ugvF6UQ=="
  "resolved" "https://registry.npmjs.org/@mapbox/mapbox-gl-supported/-/mapbox-gl-supported-2.0.1.tgz"
  "version" "2.0.1"

"@mapbox/point-geometry@^0.1.0", "@mapbox/point-geometry@~0.1.0", "@mapbox/point-geometry@0.1.0":
  "integrity" "sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ=="
  "resolved" "https://registry.npmjs.org/@mapbox/point-geometry/-/point-geometry-0.1.0.tgz"
  "version" "0.1.0"

"@mapbox/tiny-sdf@^2.0.6":
  "integrity" "sha512-qMqa27TLw+ZQz5Jk+RcwZGH7BQf5G/TrutJhspsca/3SHwmgKQ1iq+d3Jxz5oysPVYTGP6aXxCo5Lk9Er6YBAA=="
  "resolved" "https://registry.npmjs.org/@mapbox/tiny-sdf/-/tiny-sdf-2.0.6.tgz"
  "version" "2.0.6"

"@mapbox/unitbezier@^0.0.1":
  "integrity" "sha512-nMkuDXFv60aBr9soUG5q+GvZYL+2KZHVvsqFCzqnkGEf46U2fvmytHaEVc1/YZbiLn8X+eR3QzX1+dwDO1lxlw=="
  "resolved" "https://registry.npmjs.org/@mapbox/unitbezier/-/unitbezier-0.0.1.tgz"
  "version" "0.0.1"

"@mapbox/vector-tile@^1.3.1":
  "integrity" "sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw=="
  "resolved" "https://registry.npmjs.org/@mapbox/vector-tile/-/vector-tile-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@mapbox/point-geometry" "~0.1.0"

"@mapbox/whoots-js@^3.1.0":
  "integrity" "sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q=="
  "resolved" "https://registry.npmjs.org/@mapbox/whoots-js/-/whoots-js-3.1.0.tgz"
  "version" "3.1.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.8":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  "integrity" "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg=="
  "resolved" "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  "version" "0.11.0"

"@pkgr/core@^0.1.0":
  "integrity" "sha512-cq8o4cWH0ibXh9VGi5P20Tu9XF/0fFXl9EUinr9QfTM7a7p0oTA4iJRCQWppXR1Pg8dSM0UCItCkPwsk9qWWYA=="
  "resolved" "https://registry.npmjs.org/@pkgr/core/-/core-0.1.1.tgz"
  "version" "0.1.1"

"@popperjs/core@^2.11.8", "@popperjs/core@2.11.8":
  "integrity" "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A=="
  "resolved" "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  "version" "2.11.8"

"@prettier/plugin-php@0.22.1":
  "integrity" "sha512-TN7tzC2/jCM1/H/mlUjqPos8lIV+vm8Qwp83KofuZclGlG9PoUWHU7m0yqskjAoCy+R4ZCV0hxdBLPBkU69S2Q=="
  "resolved" "https://registry.npmjs.org/@prettier/plugin-php/-/plugin-php-0.22.1.tgz"
  "version" "0.22.1"
  dependencies:
    "linguist-languages" "^7.27.0"
    "mem" "^9.0.2"
    "php-parser" "^3.1.5"

"@rollup/plugin-html@1.0.3":
  "integrity" "sha512-bbjQciNXitHX+Bgk0xsW3/0wFWih/356/r7/kvmdz4wzWhAU/a0zYBWTczihrlzz/6Qpw/kZ0yXqOJwsETgg7A=="
  "resolved" "https://registry.npmjs.org/@rollup/plugin-html/-/plugin-html-1.0.3.tgz"
  "version" "1.0.3"

"@rollup/rollup-win32-x64-msvc@4.18.0":
  "integrity" "sha512-UOo5FdvOL0+eIVTgS4tIdbW+TtnBLWg1YBCcU2KWM7nuNwRz9bksDX1bekJJCpu25N1DVWaCwnT39dVQxzqS8g=="
  "resolved" "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.18.0.tgz"
  "version" "4.18.0"

"@simonwep/pickr@1.9.1":
  "integrity" "sha512-fR3qmfAcPf/HSFS7GEnTmZLM3+xERv1+jyMBbzT63ilRRM8veYjI7ELvkHHKk0/du3lHp7uh/FqatjM3646X1g=="
  "resolved" "https://registry.npmjs.org/@simonwep/pickr/-/pickr-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "core-js" "3.37.0"
    "nanopop" "2.4.2"

"@socket.io/component-emitter@~3.1.0":
  "integrity" "sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA=="
  "resolved" "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  "version" "3.1.2"

"@types/cookie@^0.4.1":
  "integrity" "sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q=="
  "resolved" "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz"
  "version" "0.4.1"

"@types/cors@^2.8.12":
  "integrity" "sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA=="
  "resolved" "https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz"
  "version" "2.8.17"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.7":
  "integrity" "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg=="
  "resolved" "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  "version" "3.7.7"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@>=8.0.0":
  "integrity" "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag=="
  "resolved" "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@1.0.5":
  "integrity" "sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz"
  "version" "1.0.5"

"@types/estree@^1.0.6":
  "integrity" "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz"
  "version" "1.0.7"

"@types/jquery@*":
  "integrity" "sha512-nbWKkkyb919DOUxjmRVk8vwtDb0/k8FKncmUKFi+NY+QXqWltooxTrswvz4LspQwxvLdvzBN1TImr6cw3aQx2A=="
  "resolved" "https://registry.npmjs.org/@types/jquery/-/jquery-3.5.30.tgz"
  "version" "3.5.30"
  dependencies:
    "@types/sizzle" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.15", "@types/json-schema@^7.0.9":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/json5@^0.0.29":
  "integrity" "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ=="
  "resolved" "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/node@*", "@types/node@^18.0.0 || >=20.0.0", "@types/node@>=10.0.0":
  "integrity" "sha512-xyu6WAMVwv6AKFLB+e/7ySZVr/0zLCzOa7rSpq6jNwpqOrUbcACDWC+53d4n2QHOnDou0fbIsg8wZu/sxrnI4Q=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-20.14.2.tgz"
  "version" "20.14.2"
  dependencies:
    "undici-types" "~5.26.4"

"@types/sizzle@*":
  "integrity" "sha512-0vWLNK2D5MT9dg0iOo8GlKguPAU02QjmZitPEsXRuJXU/OGIOt9vT9Fc26wtYuavLxtO45v9PGleoL9Z0k1LHg=="
  "resolved" "https://registry.npmjs.org/@types/sizzle/-/sizzle-2.3.8.tgz"
  "version" "2.3.8"

"@types/typeahead@0.11.32":
  "integrity" "sha512-5NkqKPwkBh2dGxFEJjc4Vt9/XsaGmx+tfXMfcJxWjPvCvvCFEKRCz+bz7ZfRAiO0e0Hh1foBol3anLzMY1Ea8A=="
  "resolved" "https://registry.npmjs.org/@types/typeahead/-/typeahead-0.11.32.tgz"
  "version" "0.11.32"
  dependencies:
    "@types/jquery" "*"

"@ungap/structured-clone@^1.2.0":
  "integrity" "sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ=="
  "resolved" "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  "version" "1.2.0"

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  "integrity" "sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  "integrity" "sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-api-error@1.13.2":
  "integrity" "sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-buffer@1.14.1":
  "integrity" "sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  "version" "1.14.1"

"@webassemblyjs/helper-numbers@1.13.2":
  "integrity" "sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  "integrity" "sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-wasm-section@1.14.1":
  "integrity" "sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  "integrity" "sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  "integrity" "sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  "integrity" "sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/wasm-edit@^1.14.1":
  "integrity" "sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  "integrity" "sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  "integrity" "sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  "integrity" "sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  "integrity" "sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"@yaireo/tagify@4.18.3":
  "integrity" "sha512-K6ksuPKZvVXQMyJXfh1k9TvX3FupqBLY4RUg+153TZQxtENsPxrRSu165va2uYtj2F76KdoY0laOlE5YO0Kqrg=="
  "resolved" "https://registry.npmjs.org/@yaireo/tagify/-/tagify-4.18.3.tgz"
  "version" "4.18.3"

"accepts@~1.3.4":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.14.0", "acorn@^8.8.2", "acorn@^8.9.0":
  "integrity" "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  "version" "8.14.1"

"adjust-sourcemap-loader@^4.0.0":
  "integrity" "sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A=="
  "resolved" "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "regex-parser" "^2.2.11"

"ajv-formats@^2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^5.1.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0", "ajv@^8.8.2", "ajv@^8.9.0", "ajv@8.16.0":
  "integrity" "sha512-F0twR8U1ZU67JIEtekUcLkXkoO5mMMmgGD8sK/xUFzJ805jxHQl92hImFAqqXMyMYjSPOyUPAwHYhB72g5sTXw=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.16.0.tgz"
  "version" "8.16.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.4.1"

"animate.css@4.1.1":
  "integrity" "sha512-+mRmCTv6SbCmtYJCN4faJMNFVNN5EuCTTprDTAo7YzIGji2KADmakjVA3+8mVDkZ2Bf09vayB35lSQIex2+QaQ=="
  "resolved" "https://registry.npmjs.org/animate.css/-/animate.css-4.1.1.tgz"
  "version" "4.1.1"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "integrity" "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
  "version" "6.0.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^6.1.0":
  "integrity" "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  "version" "6.2.1"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aos@2.3.4":
  "integrity" "sha512-zh/ahtR2yME4I51z8IttIt4lC1Nw0ktsFtmeDzID1m9naJnWXhCoARaCgNOGXb5CLy3zm+wqmRAEgMYB5E2HUw=="
  "resolved" "https://registry.npmjs.org/aos/-/aos-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "classlist-polyfill" "^1.0.3"
    "lodash.debounce" "^4.0.6"
    "lodash.throttle" "^4.0.1"

"apexcharts-clevision@3.28.5":
  "integrity" "sha512-FaxvmpCGxUyusUGzS1iAt3yL00UG5Bus73fhEhnK1QuH+expzz53f4lJFxwP1kY31vpR9WTzZx52CZzDEk4UOg=="
  "resolved" "https://registry.npmjs.org/apexcharts-clevision/-/apexcharts-clevision-3.28.5.tgz"
  "version" "3.28.5"
  dependencies:
    "svg.draggable.js" "^2.2.2"
    "svg.easing.js" "^2.0.0"
    "svg.filter.js" "^2.0.2"
    "svg.pathmorphing.js" "^0.1.3"
    "svg.resize.js" "^1.4.3"
    "svg.select.js" "^3.0.1"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"array-buffer-byte-length@^1.0.1":
  "integrity" "sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg=="
  "resolved" "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.5"
    "is-array-buffer" "^3.0.4"

"array-includes@^3.1.7":
  "integrity" "sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ=="
  "resolved" "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz"
  "version" "3.1.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-object-atoms" "^1.0.0"
    "get-intrinsic" "^1.2.4"
    "is-string" "^1.0.7"

"array.prototype.findlastindex@^1.2.3":
  "integrity" "sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ=="
  "resolved" "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"
    "es-shim-unscopables" "^1.0.2"

"array.prototype.flat@^1.3.2":
  "integrity" "sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA=="
  "resolved" "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "es-shim-unscopables" "^1.0.0"

"array.prototype.flatmap@^1.3.2":
  "integrity" "sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ=="
  "resolved" "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "es-shim-unscopables" "^1.0.0"

"arraybuffer.prototype.slice@^1.0.3":
  "integrity" "sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A=="
  "resolved" "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "array-buffer-byte-length" "^1.0.1"
    "call-bind" "^1.0.5"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.22.3"
    "es-errors" "^1.2.1"
    "get-intrinsic" "^1.2.3"
    "is-array-buffer" "^3.0.4"
    "is-shared-array-buffer" "^1.0.2"

"async-each-series@0.1.1":
  "integrity" "sha512-p4jj6Fws4Iy2m0iCmI2am2ZNZCgbdgE+P8F/8csmn2vx7ixXrO2zGcuNsD46X5uZSVecmkEy/M06X2vG8KD6dQ=="
  "resolved" "https://registry.npmjs.org/async-each-series/-/async-each-series-0.1.1.tgz"
  "version" "0.1.1"

"async@^2.6.0":
  "integrity" "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA=="
  "resolved" "https://registry.npmjs.org/async/-/async-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "lodash" "^4.17.14"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atoa@1.0.0":
  "integrity" "sha512-VVE1H6cc4ai+ZXo/CRWoJiHXrA1qfA31DPnx6D20+kSI547hQN5Greh51LQ1baMRMfxO5K5M4ImMtZbZt2DODQ=="
  "resolved" "https://registry.npmjs.org/atoa/-/atoa-1.0.0.tgz"
  "version" "1.0.0"

"autoprefixer@10.4.19":
  "integrity" "sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.19.tgz"
  "version" "10.4.19"
  dependencies:
    "browserslist" "^4.23.0"
    "caniuse-lite" "^1.0.30001599"
    "fraction.js" "^4.3.7"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.0.0"
    "postcss-value-parser" "^4.2.0"

"autosize@6.0.1":
  "integrity" "sha512-f86EjiUKE6Xvczc4ioP1JBlWG7FKrE13qe/DxBCpe8GCipCq2nFw73aO8QEBKHfSbYGDN5eB9jXWKen7tspDqQ=="
  "resolved" "https://registry.npmjs.org/autosize/-/autosize-6.0.1.tgz"
  "version" "6.0.1"

"available-typed-arrays@^1.0.7":
  "integrity" "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ=="
  "resolved" "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "possible-typed-array-names" "^1.0.0"

"axios@0.21.4":
  "integrity" "sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-0.21.4.tgz"
  "version" "0.21.4"
  dependencies:
    "follow-redirects" "^1.14.0"

"axios@1.7.2":
  "integrity" "sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"babel-loader@9.1.3":
  "integrity" "sha512-xG3ST4DglodGf8qSwv0MdeWLhrDsw/32QMdTO5T1ZIp9gQur0HkCyFs7Awskr10JKXFXwpAhiCuYX5oGXnRGbw=="
  "resolved" "https://registry.npmjs.org/babel-loader/-/babel-loader-9.1.3.tgz"
  "version" "9.1.3"
  dependencies:
    "find-cache-dir" "^4.0.0"
    "schema-utils" "^4.0.0"

"babel-plugin-polyfill-corejs2@^0.4.7":
  "integrity" "sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.11.tgz"
  "version" "0.4.11"
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    "semver" "^6.3.1"

"babel-plugin-polyfill-corejs3@^0.8.7":
  "integrity" "sha512-KyDvZYxAzkC0Aj2dAPyDzi2Ym15e5JKZSK+maI7NAwSqofvuFglbSsxE7wUOvTg9oFVnHMzVzBKcqEb4PJgtOA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.7.tgz"
  "version" "0.8.7"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.4.4"
    "core-js-compat" "^3.33.1"

"babel-plugin-polyfill-regenerator@^0.5.4":
  "integrity" "sha512-OJGYZlhLqBh2DDHeqAxWB1XIvr49CxiJ2gIt61/PU55CQK4Z58OzMqjDe1zwQdQk+rBYsRc+1rJmdajM3gimHg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.5.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.1.2", "base64-js@^1.3.0", "base64-js@1.3.1":
  "integrity" "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz"
  "version" "1.3.1"

"base64id@~2.0.0", "base64id@2.0.0":
  "integrity" "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog=="
  "resolved" "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz"
  "version" "2.0.0"

"batch@0.6.1":
  "integrity" "sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw=="
  "resolved" "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  "version" "2.3.0"

"block-ui@2.70.1":
  "integrity" "sha512-Hx69Bw368lQ9+zOcisvWpoHBsZi9owHQ1YBwLZXFhOMXv59VjBSHHCDWfTaByYSUoNZgTt/sjR1juEbyqwWpwQ=="
  "resolved" "https://registry.npmjs.org/block-ui/-/block-ui-2.70.1.tgz"
  "version" "2.70.1"
  dependencies:
    "jquery" ">=1.7.x"

"bloodhound-js@1.2.3":
  "integrity" "sha512-gpcHaO8SqK/bLwWgobLGQvIVpSdTX4fKZFjfh6ltori5aULXJxC/ndrNRf3PYGOdyD4mFIqjySXLWX4pE88VmA=="
  "resolved" "https://registry.npmjs.org/bloodhound-js/-/bloodhound-js-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "es6-promise" "^3.0.2"
    "object-assign" "^4.0.1"
    "storage2" "^0.1.0"
    "superagent" "^3.8.3"

"bootstrap-datepicker@1.10.0":
  "integrity" "sha512-lWxtSYddAQOpbAO8UhYhHLcK6425eWoSjb5JDvZU3ePHEPF6A3eUr51WKaFy4PccU19JRxUG6wEU3KdhtKfvpg=="
  "resolved" "https://registry.npmjs.org/bootstrap-datepicker/-/bootstrap-datepicker-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "jquery" ">=3.4.0 <4.0.0"

"bootstrap-daterangepicker@3.1.0":
  "integrity" "sha512-oaQZx6ZBDo/dZNyXGVi2rx5GmFXThyQLAxdtIqjtLlYVaQUfQALl5JZMJJZzyDIX7blfy4ppZPAJ10g8Ma4d/g=="
  "resolved" "https://registry.npmjs.org/bootstrap-daterangepicker/-/bootstrap-daterangepicker-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "jquery" ">=1.10"
    "moment" "^2.9.0"

"bootstrap-maxlength@1.10.1":
  "integrity" "sha512-VYQosg0ojUNq05PlZcTwETm0E0Aoe/cclRmCC27QrHk/sY0Q75PUvgHYujN0gb2CD3n2olJfPeqx3EGAqpKjww=="
  "resolved" "https://registry.npmjs.org/bootstrap-maxlength/-/bootstrap-maxlength-1.10.1.tgz"
  "version" "1.10.1"
  dependencies:
    "bootstrap" "^4.4.1"
    "jquery" "^3.5.1"
    "qunit" "^2.10.0"

"bootstrap-select@1.14.0-beta3":
  "integrity" "sha512-wYUDY4NAYBcNydXybE7wh3+ucyf+AcUOhZ+e0TFIoZ38A+k/3BVT1RPl5f0CiPxAexP1IQuqALKMqI8wtZS71A=="
  "resolved" "https://registry.npmjs.org/bootstrap-select/-/bootstrap-select-1.14.0-beta3.tgz"
  "version" "1.14.0-beta3"

"bootstrap@^4.4.1":
  "integrity" "sha512-51Bbp/Uxr9aTuy6ca/8FbFloBUJZLHwnhTcnjIeRn2suQWsWzcuJhGjKDB5eppVte/8oCdOL3VuwxvZDUggwGQ=="
  "resolved" "https://registry.npmjs.org/bootstrap/-/bootstrap-4.6.2.tgz"
  "version" "4.6.2"

"bootstrap@>=3.0.0", "bootstrap@5.3.3":
  "integrity" "sha512-8HLCdWgyoMguSO9o+aH+iuZ+aht+mzW0u3HIMzVu7Srrpv7EBBxTnrFlSCskwdY1+EOFQSm7uMJhNQHkdPcmjg=="
  "resolved" "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.3.tgz"
  "version" "5.3.3"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^3.0.3", "braces@~3.0.2":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"brotli@^1.2.0":
  "integrity" "sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg=="
  "resolved" "https://registry.npmjs.org/brotli/-/brotli-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "base64-js" "^1.1.2"

"browser-sync-client@^2.29.3":
  "integrity" "sha512-4tK5JKCl7v/3aLbmCBMzpufiYLsB1+UI+7tUXCCp5qF0AllHy/jAqYu6k7hUF3hYtlClKpxExWaR+rH+ny07wQ=="
  "resolved" "https://registry.npmjs.org/browser-sync-client/-/browser-sync-client-2.29.3.tgz"
  "version" "2.29.3"
  dependencies:
    "etag" "1.8.1"
    "fresh" "0.5.2"
    "mitt" "^1.1.3"

"browser-sync-ui@^2.29.3":
  "integrity" "sha512-kBYOIQjU/D/3kYtUIJtj82e797Egk1FB2broqItkr3i4eF1qiHbFCG6srksu9gWhfmuM/TNG76jMfzAdxEPakg=="
  "resolved" "https://registry.npmjs.org/browser-sync-ui/-/browser-sync-ui-2.29.3.tgz"
  "version" "2.29.3"
  dependencies:
    "async-each-series" "0.1.1"
    "chalk" "4.1.2"
    "connect-history-api-fallback" "^1"
    "immutable" "^3"
    "server-destroy" "1.0.1"
    "socket.io-client" "^4.4.1"
    "stream-throttle" "^0.1.3"

"browser-sync@2.29.3":
  "integrity" "sha512-NiM38O6XU84+MN+gzspVmXV2fTOoe+jBqIBx3IBdhZrdeURr6ZgznJr/p+hQ+KzkKEiGH/GcC4SQFSL0jV49bg=="
  "resolved" "https://registry.npmjs.org/browser-sync/-/browser-sync-2.29.3.tgz"
  "version" "2.29.3"
  dependencies:
    "browser-sync-client" "^2.29.3"
    "browser-sync-ui" "^2.29.3"
    "bs-recipes" "1.3.4"
    "chalk" "4.1.2"
    "chokidar" "^3.5.1"
    "connect" "3.6.6"
    "connect-history-api-fallback" "^1"
    "dev-ip" "^1.0.1"
    "easy-extender" "^2.3.4"
    "eazy-logger" "^4.0.1"
    "etag" "^1.8.1"
    "fresh" "^0.5.2"
    "fs-extra" "3.0.1"
    "http-proxy" "^1.18.1"
    "immutable" "^3"
    "localtunnel" "^2.0.1"
    "micromatch" "^4.0.2"
    "opn" "5.3.0"
    "portscanner" "2.2.0"
    "raw-body" "^2.3.2"
    "resp-modifier" "6.0.2"
    "rx" "4.1.0"
    "send" "0.16.2"
    "serve-index" "1.9.1"
    "serve-static" "1.13.2"
    "server-destroy" "1.0.1"
    "socket.io" "^4.4.1"
    "ua-parser-js" "^1.0.33"
    "yargs" "^17.3.1"

"browserslist@^4.22.2", "browserslist@^4.23.0", "browserslist@^4.24.0", "browserslist@>= 4.21.0":
  "integrity" "sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.24.5.tgz"
  "version" "4.24.5"
  dependencies:
    "caniuse-lite" "^1.0.30001716"
    "electron-to-chromium" "^1.5.149"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.3"

"bs-recipes@1.3.4":
  "integrity" "sha512-BXvDkqhDNxXEjeGM8LFkSbR+jzmP/CYpCiVKYn+soB1dDldeU15EBNDkwVXndKuX35wnNUaPd0qSoQEAkmQtMw=="
  "resolved" "https://registry.npmjs.org/bs-recipes/-/bs-recipes-1.3.4.tgz"
  "version" "1.3.4"

"bs-stepper@1.7.0":
  "integrity" "sha512-+DX7UKKgw2GI6ucsSCRd19VHYrxf/8znRCLs1lQVVLxz+h7EqgIOxoHcJ0/QTaaNoR9Cwg78ydo6hXIasyd3LA=="
  "resolved" "https://registry.npmjs.org/bs-stepper/-/bs-stepper-1.7.0.tgz"
  "version" "1.7.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"call-bind@^1.0.2", "call-bind@^1.0.5", "call-bind@^1.0.6", "call-bind@^1.0.7":
  "integrity" "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.1"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"caniuse-lite@^1.0.30001599", "caniuse-lite@^1.0.30001716":
  "integrity" "sha512-49/c1+x3Kwz7ZIWt+4DvK3aMJy9oYXXG6/97JKsnjdCk/6n9vVyWL8NAwVt95Lwt9eigI10Hl782kDfZUUlRXw=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001716.tgz"
  "version" "1.0.30001716"

"chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@4.1.2":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chart.js@4.4.3":
  "integrity" "sha512-qK1gkGSRYcJzqrrzdR6a+I0vQ4/R+SoODXyAjscQ/4mzuNzySaMCd+hyVxitSY1+L2fjPD1Gbn+ibNqRmwQeLw=="
  "resolved" "https://registry.npmjs.org/chart.js/-/chart.js-4.4.3.tgz"
  "version" "4.4.3"
  dependencies:
    "@kurkle/color" "^0.3.0"

"cheap-ruler@^3.0.1":
  "integrity" "sha512-02T332h1/HTN6cDSufLP8x4JzDs2+VC+8qZ/N0kWIVPyc2xUkWwWh3B2fJxR7raXkL4Mq7k554mfuM9ofv/vGg=="
  "resolved" "https://registry.npmjs.org/cheap-ruler/-/cheap-ruler-3.0.2.tgz"
  "version" "3.0.2"

"chokidar@^3.5.1", "chokidar@^3.5.2", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  "version" "1.0.4"

"classlist-polyfill@^1.0.3":
  "integrity" "sha512-GzIjNdcEtH4ieA2S8NmrSxv7DfEV5fmixQeyTmqmRmRJPGpRBaSnA2a0VrCjyT8iW8JjEdMbKzDotAJf+ajgaQ=="
  "resolved" "https://registry.npmjs.org/classlist-polyfill/-/classlist-polyfill-1.2.0.tgz"
  "version" "1.2.0"

"cleave.js@1.6.0":
  "integrity" "sha512-ivqesy3j5hQVG3gywPfwKPbi/7ZSftY/UNp5uphnqjr25yI2CP8FS2ODQPzuLXXnNLi29e2+PgPkkiKUXLs/Nw=="
  "resolved" "https://registry.npmjs.org/cleave.js/-/cleave.js-1.6.0.tgz"
  "version" "1.6.0"

"clipboard@2.0.11":
  "integrity" "sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw=="
  "resolved" "https://registry.npmjs.org/clipboard/-/clipboard-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "good-listener" "^1.2.2"
    "select" "^1.1.2"
    "tiny-emitter" "^2.0.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"cliui@^8.0.1":
  "integrity" "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.1"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.4":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w=="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"combined-stream@^1.0.6", "combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.2.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^8.3.0":
  "integrity" "sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz"
  "version" "8.3.0"

"commander@7.2.0":
  "integrity" "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz"
  "version" "7.2.0"

"common-path-prefix@^3.0.0":
  "integrity" "sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w=="
  "resolved" "https://registry.npmjs.org/common-path-prefix/-/common-path-prefix-3.0.0.tgz"
  "version" "3.0.0"

"component-emitter@^1.2.0":
  "integrity" "sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ=="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz"
  "version" "1.3.1"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"confusing-browser-globals@^1.0.10":
  "integrity" "sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA=="
  "resolved" "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
  "version" "1.0.11"

"connect-history-api-fallback@^1":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"connect@3.6.6":
  "integrity" "sha512-OO7axMmPpu/2XuX1+2Yrg0ddju31B6xLZMWkJ5rYBu4YRmRVlOjvlY6kw2FJKiAzyxGwnrDUAG4s1Pf0sbBMCQ=="
  "resolved" "https://registry.npmjs.org/connect/-/connect-3.6.6.tgz"
  "version" "3.6.6"
  dependencies:
    "debug" "2.6.9"
    "finalhandler" "1.1.0"
    "parseurl" "~1.3.2"
    "utils-merge" "1.0.1"

"contra@1.9.4":
  "integrity" "sha512-N9ArHAqwR/lhPq4OdIAwH4e1btn6EIZMAz4TazjnzCiVECcWUPTma+dRAM38ERImEJBh8NiCCpjoQruSZ+agYg=="
  "resolved" "https://registry.npmjs.org/contra/-/contra-1.9.4.tgz"
  "version" "1.9.4"
  dependencies:
    "atoa" "1.0.0"
    "ticky" "1.0.1"

"convert-source-map@^1.7.0":
  "integrity" "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  "version" "1.9.0"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"cookie@~0.4.1":
  "integrity" "sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz"
  "version" "0.4.2"

"cookiejar@^2.1.0":
  "integrity" "sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw=="
  "resolved" "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.4.tgz"
  "version" "2.1.4"

"core-js-compat@^3.31.0", "core-js-compat@^3.33.1":
  "integrity" "sha512-9TNiImhKvQqSUkOvk/mMRZzOANTiEVC7WaBNhHcKM7x+/5E1l5NvsysR19zuDQScE8k+kfQXWRN3AtS/eOSHpg=="
  "resolved" "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.37.1.tgz"
  "version" "3.37.1"
  dependencies:
    "browserslist" "^4.23.0"

"core-js@^3.26.1", "core-js@3.37.0":
  "integrity" "sha512-fu5vHevQ8ZG4og+LXug8ulUtVxjOcEYvifJr7L5Bfq9GOztVqsKd9/59hUk2ZSbCrS3BqUr3EpaYGIYzq7g3Ug=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.37.0.tgz"
  "version" "3.37.0"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cors@~2.8.5":
  "integrity" "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="
  "resolved" "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  "version" "2.8.5"
  dependencies:
    "object-assign" "^4"
    "vary" "^1"

"cross-env@7.0.3":
  "integrity" "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw=="
  "resolved" "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "cross-spawn" "^7.0.1"

"cross-spawn@^7.0.0", "cross-spawn@^7.0.1", "cross-spawn@^7.0.2":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crossvent@1.5.5":
  "integrity" "sha512-MY4xhBYEnVi+pmTpHCOCsCLYczc0PVtGdPBz6NXNXxikLaUZo4HdAeUb1UqAo3t3yXAloSelTmfxJ+/oUqkW5w=="
  "resolved" "https://registry.npmjs.org/crossvent/-/crossvent-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "custom-event" "^1.0.0"

"crypto-js@^4.2.0":
  "integrity" "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q=="
  "resolved" "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  "version" "4.2.0"

"csscolorparser@~1.0.3":
  "integrity" "sha512-umPSgYwZkdFoUrH5hIq5kf0wPSXiro51nPw0j2K/c83KflkPSTBGMz6NJvMB+07VlL0y7VPo6QJcDjcgKTTm3w=="
  "resolved" "https://registry.npmjs.org/csscolorparser/-/csscolorparser-1.0.3.tgz"
  "version" "1.0.3"

"custom-event-polyfill@^1.0.7":
  "integrity" "sha512-TDDkd5DkaZxZFM8p+1I3yAlvM3rSr1wbrOliG4yJiwinMZN8z/iGL7BTlDkrJcYTmgUSb4ywVCc3ZaUtOtC76w=="
  "resolved" "https://registry.npmjs.org/custom-event-polyfill/-/custom-event-polyfill-1.0.7.tgz"
  "version" "1.0.7"

"custom-event@^1.0.0":
  "integrity" "sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg=="
  "resolved" "https://registry.npmjs.org/custom-event/-/custom-event-1.0.1.tgz"
  "version" "1.0.1"

"data-view-buffer@^1.0.1":
  "integrity" "sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA=="
  "resolved" "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"data-view-byte-length@^1.0.1":
  "integrity" "sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ=="
  "resolved" "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"data-view-byte-offset@^1.0.0":
  "integrity" "sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA=="
  "resolved" "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"datatables.net-bs5@1.13.11":
  "integrity" "sha512-NQO15TjXo4xvZ0jReC4Uf86ezbQzPQPdaN2YncrCPhzQ6fx+2WD7DUq4ur0HbAcYHo94/M+MtD5Ch29To7Rj3Q=="
  "resolved" "https://registry.npmjs.org/datatables.net-bs5/-/datatables.net-bs5-1.13.11.tgz"
  "version" "1.13.11"
  dependencies:
    "datatables.net" "1.13.11"
    "jquery" "1.8 - 4"

"datatables.net-buttons-bs5@2.4.3":
  "integrity" "sha512-osgIyqTTXRWOF+vh19GM0ixB4kqmtQTiYmcO3v2Ca+bwUpfasI+ylEyMrHqqiON77zA+FmPTuUVHUZTJIIJWUg=="
  "resolved" "https://registry.npmjs.org/datatables.net-buttons-bs5/-/datatables.net-buttons-bs5-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "datatables.net-bs5" "^1.13.0"
    "datatables.net-buttons" "2.4.3"
    "jquery" ">=1.7"

"datatables.net-buttons@2.4.3":
  "integrity" "sha512-xoHD6I2kxnU/CEp97Ar0lSnAL1siucQ/5Q/otGWWfWE2VN0o4n5C2h2Ot/ZCS8kxbEHBGd873Bc2xPdJH87yOw=="
  "resolved" "https://registry.npmjs.org/datatables.net-buttons/-/datatables.net-buttons-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "datatables.net" "^1.13.0"
    "jquery" ">=1.7"

"datatables.net-fixedcolumns-bs5@4.3.1":
  "integrity" "sha512-cHh+ydEmtnD7O5JHsob32Xi0iS+CTvJSSJEEuasK7JmOG6Ct2OjwSX2Z6MHQyjoaSWxqjnT4OVf1v01j8abXTA=="
  "resolved" "https://registry.npmjs.org/datatables.net-fixedcolumns-bs5/-/datatables.net-fixedcolumns-bs5-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "datatables.net-bs5" "^1.13.0"
    "datatables.net-fixedcolumns" "4.3.1"
    "jquery" ">=1.7"

"datatables.net-fixedcolumns@4.3.1":
  "integrity" "sha512-K5hEr5PIIHFMLd2sR9CBw3RRhf0nJbbsc5NHWnfjUUtnr9d808xbifuej3TpdKOtGeRJgAnRktiL9f30sM32CQ=="
  "resolved" "https://registry.npmjs.org/datatables.net-fixedcolumns/-/datatables.net-fixedcolumns-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "datatables.net" "^1.13.0"
    "jquery" ">=1.7"

"datatables.net-fixedheader-bs5@3.4.1":
  "integrity" "sha512-A2oDu1dcDzM6tF1IJNFtcnPQ5Vl3wJewyEs9I9/oI/a5P93nAZGbG6F5ZZedE9UDSMfNfCU3MbXYaUYmZEAqrA=="
  "resolved" "https://registry.npmjs.org/datatables.net-fixedheader-bs5/-/datatables.net-fixedheader-bs5-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "datatables.net-bs5" "^1.13.0"
    "datatables.net-fixedheader" "3.4.1"
    "jquery" ">=1.7"

"datatables.net-fixedheader@3.4.1":
  "integrity" "sha512-c9FJAShG5r8RJDIszWQvMFe6Ie+njfbHB9GhzOPjEF7zhbsMUQEkoYq1qW3ppOxY5psadDrT+D3f4iGM589u6w=="
  "resolved" "https://registry.npmjs.org/datatables.net-fixedheader/-/datatables.net-fixedheader-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "datatables.net" "^1.13.0"
    "jquery" ">=1.7"

"datatables.net-responsive-bs5@2.5.1":
  "integrity" "sha512-jNaIO67eM+TivHSm8mnMp2LrP/Fo+zMCoBUUj7oA8Xt49J/CMvdHCzh0szpLxj/++xmTZzCkuslyu6iM85VO8g=="
  "resolved" "https://registry.npmjs.org/datatables.net-responsive-bs5/-/datatables.net-responsive-bs5-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "datatables.net-bs5" "^1.13.0"
    "datatables.net-responsive" "2.5.1"
    "jquery" ">=1.7"

"datatables.net-responsive@2.5.1":
  "integrity" "sha512-hyJb2faIzEWUX5Yn4HOSq/6NNB9SXDVbI4OU9ny+XU/2ghZEz4676spOgzpDHTdWvCfM+t1mbUsT70fDiTTr9w=="
  "resolved" "https://registry.npmjs.org/datatables.net-responsive/-/datatables.net-responsive-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "datatables.net" "^1.13.0"
    "jquery" ">=1.7"

"datatables.net-rowgroup-bs5@1.4.1":
  "integrity" "sha512-rY34tetrYXq5Aa5ijHeq/6KriyjC24cocumxBzrqBafnGKmh0YGTx6zMzWjDV5f1RS2/NDRlgn7BaLJ9gJa24Q=="
  "resolved" "https://registry.npmjs.org/datatables.net-rowgroup-bs5/-/datatables.net-rowgroup-bs5-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "datatables.net-bs5" ">=1.13.4"
    "datatables.net-rowgroup" ">=1.3.1"
    "jquery" ">=1.7"

"datatables.net-rowgroup@1.4.1":
  "integrity" "sha512-nwcQbh1IttKyEedmObOKnskvriis3DBKsBRpiSoZO3EvRlZ69wnW9WQhw9/WxPnOUjATO3gJ/n7DMrLoGsKdDA=="
  "resolved" "https://registry.npmjs.org/datatables.net-rowgroup/-/datatables.net-rowgroup-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "datatables.net" ">=1.13.4"
    "jquery" ">=1.7"

"datatables.net-select-bs5@1.7.1":
  "integrity" "sha512-445VC5BtBZLKGYoxW85a0XFIpGkPpw4O/7fav2c989e27NEuO++Ztya1I3dFcCW+lB5CjV7U/oerm6bYkNJ+hQ=="
  "resolved" "https://registry.npmjs.org/datatables.net-select-bs5/-/datatables.net-select-bs5-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "datatables.net-bs5" "^1.13.0"
    "datatables.net-select" "1.7.1"
    "jquery" ">=1.7"

"datatables.net-select@1.7.1":
  "integrity" "sha512-yC+GoBDVsnbaFTYKmZ2v5Bftc66zSZCYHbPYZb/ccdvxytEEudjc9R3wn6fgkOrVx3C2X/8keQc4a7EJvdOErg=="
  "resolved" "https://registry.npmjs.org/datatables.net-select/-/datatables.net-select-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "datatables.net" "^1.13.0"
    "jquery" ">=1.7"

"datatables.net@1.13.11":
  "integrity" "sha512-AE6RkMXziRaqzPcu/pl3SJXeRa6fmXQG/fVjuRESujvkzqDCYEeKTTpPMuVJSGYJpPi32WGSphVNNY1G4nSN/g=="
  "resolved" "https://registry.npmjs.org/datatables.net/-/datatables.net-1.13.11.tgz"
  "version" "1.13.11"
  dependencies:
    "jquery" "1.8 - 4"

"debug@^2.2.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.1", "debug@^4.3.2", "debug@~4.3.1", "debug@~4.3.2", "debug@~4.3.4":
  "integrity" "sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.5.tgz"
  "version" "4.3.5"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@4.3.2":
  "integrity" "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "ms" "2.1.2"

"deep-equal@^1.0.0", "deep-equal@^1.0.1":
  "integrity" "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-arguments" "^1.1.1"
    "is-date-object" "^1.0.5"
    "is-regex" "^1.1.4"
    "object-is" "^1.1.5"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.5.1"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^4.3.1":
  "integrity" "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"define-data-property@^1.0.1", "define-data-property@^1.1.4":
  "integrity" "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A=="
  "resolved" "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-properties@^1.2.0", "define-properties@^1.2.1":
  "integrity" "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegate@^3.1.2":
  "integrity" "sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw=="
  "resolved" "https://registry.npmjs.org/delegate/-/delegate-3.2.0.tgz"
  "version" "3.2.0"

"depd@~1.1.2":
  "integrity" "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"desandro-matches-selector@^2.0.0":
  "integrity" "sha512-+1q0nXhdzg1IpIJdMKalUwvvskeKnYyEe3shPRwedNcWtnhEKT3ZxvFjzywHDeGcKViIxTCAoOYQWP1qD7VNyg=="
  "resolved" "https://registry.npmjs.org/desandro-matches-selector/-/desandro-matches-selector-2.0.2.tgz"
  "version" "2.0.2"

"destroy@~1.0.4":
  "integrity" "sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg=="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"dev-ip@^1.0.1":
  "integrity" "sha512-LmVkry/oDShEgSZPNgqCIp2/TlqtExeGmymru3uCELnfyjY11IzpAproLYs+1X88fXO6DBoYP3ul2Xo2yz2j6A=="
  "resolved" "https://registry.npmjs.org/dev-ip/-/dev-ip-1.0.1.tgz"
  "version" "1.0.1"

"dfa@^1.2.0":
  "integrity" "sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q=="
  "resolved" "https://registry.npmjs.org/dfa/-/dfa-1.2.0.tgz"
  "version" "1.2.0"

"doctrine@^2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dragula@^3.7.3":
  "integrity" "sha512-/rRg4zRhcpf81TyDhaHLtXt6sEywdfpv1cRUMeFFy7DuypH2U0WUL0GTdyAQvXegviT4PJK4KuMmOaIDpICseQ=="
  "resolved" "https://registry.npmjs.org/dragula/-/dragula-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "contra" "1.9.4"
    "crossvent" "1.5.5"

"dropzone@5.9.3":
  "integrity" "sha512-Azk8kD/2/nJIuVPK+zQ9sjKMRIpRvNyqn9XwbBHNq+iNuSccbJS6hwm1Woy0pMST0erSo0u4j+KJaodndDk4vA=="
  "resolved" "https://registry.npmjs.org/dropzone/-/dropzone-5.9.3.tgz"
  "version" "5.9.3"

"earcut@^2.2.4":
  "integrity" "sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ=="
  "resolved" "https://registry.npmjs.org/earcut/-/earcut-2.2.4.tgz"
  "version" "2.2.4"

"eastasianwidth@^0.2.0":
  "integrity" "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA=="
  "resolved" "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  "version" "0.2.0"

"easy-extender@^2.3.4":
  "integrity" "sha512-8cAwm6md1YTiPpOvDULYJL4ZS6WfM5/cTeVVh4JsvyYZAoqlRVUpHL9Gr5Fy7HA6xcSZicUia3DeAgO3Us8E+Q=="
  "resolved" "https://registry.npmjs.org/easy-extender/-/easy-extender-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "lodash" "^4.17.10"

"eazy-logger@^4.0.1":
  "integrity" "sha512-2GSFtnnC6U4IEKhEI7+PvdxrmjJ04mdsj3wHZTFiw0tUtG4HCWzTr13ZYTk8XOGnA1xQMaDljoBOYlk3D/MMSw=="
  "resolved" "https://registry.npmjs.org/eazy-logger/-/eazy-logger-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "chalk" "4.1.2"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.5.149":
  "integrity" "sha512-UyiO82eb9dVOx8YO3ajDf9jz2kKyt98DEITRdeLPstOEuTlLzDA4Gyq5K9he71TQziU5jUVu2OAu5N48HmQiyQ=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.149.tgz"
  "version" "1.5.149"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.2.2":
  "integrity" "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  "version" "9.2.2"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.1", "encodeurl@~1.0.2":
  "integrity" "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"engine.io-client@~6.5.2":
  "integrity" "sha512-9Z0qLB0NIisTRt1DZ/8U2k12RJn8yls/nXMZLn+/N8hANT3TcYjKFKcwbw5zFQiN4NTde3TSY9zb79e1ij6j9Q=="
  "resolved" "https://registry.npmjs.org/engine.io-client/-/engine.io-client-6.5.3.tgz"
  "version" "6.5.3"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.11.0"
    "xmlhttprequest-ssl" "~2.0.0"

"engine.io-parser@~5.2.1":
  "integrity" "sha512-RcyUFKA93/CXH20l4SoVvzZfrSDMOTUS3bWVpTt2FuFP+XYrL8i8oonHP7WInRyVHXh0n/ORtoeiE1os+8qkSw=="
  "resolved" "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.2.tgz"
  "version" "5.2.2"

"engine.io@~6.5.2":
  "integrity" "sha512-KdVSDKhVKyOi+r5uEabrDLZw2qXStVvCsEB/LN3mw4WFi6Gx50jTyuxYVCwAAC0U46FdnzP/ScKRBTXb/NiEOg=="
  "resolved" "https://registry.npmjs.org/engine.io/-/engine.io-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "@types/cookie" "^0.4.1"
    "@types/cors" "^2.8.12"
    "@types/node" ">=10.0.0"
    "accepts" "~1.3.4"
    "base64id" "2.0.0"
    "cookie" "~0.4.1"
    "cors" "~2.8.5"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.11.0"

"enhanced-resolve@^5.17.1":
  "integrity" "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz"
  "version" "5.18.1"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"es-abstract@^1.22.1", "es-abstract@^1.22.3", "es-abstract@^1.23.0", "es-abstract@^1.23.2":
  "integrity" "sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.3.tgz"
  "version" "1.23.3"
  dependencies:
    "array-buffer-byte-length" "^1.0.1"
    "arraybuffer.prototype.slice" "^1.0.3"
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "data-view-buffer" "^1.0.1"
    "data-view-byte-length" "^1.0.1"
    "data-view-byte-offset" "^1.0.0"
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"
    "es-set-tostringtag" "^2.0.3"
    "es-to-primitive" "^1.2.1"
    "function.prototype.name" "^1.1.6"
    "get-intrinsic" "^1.2.4"
    "get-symbol-description" "^1.0.2"
    "globalthis" "^1.0.3"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"
    "has-proto" "^1.0.3"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.2"
    "internal-slot" "^1.0.7"
    "is-array-buffer" "^3.0.4"
    "is-callable" "^1.2.7"
    "is-data-view" "^1.0.1"
    "is-negative-zero" "^2.0.3"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.3"
    "is-string" "^1.0.7"
    "is-typed-array" "^1.1.13"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.13.1"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.5"
    "regexp.prototype.flags" "^1.5.2"
    "safe-array-concat" "^1.1.2"
    "safe-regex-test" "^1.0.3"
    "string.prototype.trim" "^1.2.9"
    "string.prototype.trimend" "^1.0.8"
    "string.prototype.trimstart" "^1.0.8"
    "typed-array-buffer" "^1.0.2"
    "typed-array-byte-length" "^1.0.1"
    "typed-array-byte-offset" "^1.0.2"
    "typed-array-length" "^1.0.6"
    "unbox-primitive" "^1.0.2"
    "which-typed-array" "^1.1.15"

"es-define-property@^1.0.0":
  "integrity" "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ=="
  "resolved" "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.2.4"

"es-errors@^1.2.1", "es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-module-lexer@^1.2.1":
  "integrity" "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz"
  "version" "1.7.0"

"es-object-atoms@^1.0.0":
  "integrity" "sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw=="
  "resolved" "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.0.3":
  "integrity" "sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ=="
  "resolved" "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "get-intrinsic" "^1.2.4"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.1"

"es-shim-unscopables@^1.0.0", "es-shim-unscopables@^1.0.2":
  "integrity" "sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw=="
  "resolved" "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "hasown" "^2.0.0"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"es6-promise@^3.0.2":
  "integrity" "sha512-SOp9Phqvqn7jtEUxPWdWfWoLmyt2VaJ6MpvP9Comy1MceMXqE6bxvaTu4iaxpYYPzhny28Lc+M87/c2cPK6lDg=="
  "resolved" "https://registry.npmjs.org/es6-promise/-/es6-promise-3.3.1.tgz"
  "version" "3.3.1"

"esbuild@^0.20.1":
  "integrity" "sha512-WdOOppmUNU+IbZ0PaDiTst80zjnrOkyJNHoKupIcVyU8Lvla3Ugx94VzkQ32Ijqd7UhHJy75gNWDMUekcrSJ6g=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.20.2.tgz"
  "version" "0.20.2"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.20.2"
    "@esbuild/android-arm" "0.20.2"
    "@esbuild/android-arm64" "0.20.2"
    "@esbuild/android-x64" "0.20.2"
    "@esbuild/darwin-arm64" "0.20.2"
    "@esbuild/darwin-x64" "0.20.2"
    "@esbuild/freebsd-arm64" "0.20.2"
    "@esbuild/freebsd-x64" "0.20.2"
    "@esbuild/linux-arm" "0.20.2"
    "@esbuild/linux-arm64" "0.20.2"
    "@esbuild/linux-ia32" "0.20.2"
    "@esbuild/linux-loong64" "0.20.2"
    "@esbuild/linux-mips64el" "0.20.2"
    "@esbuild/linux-ppc64" "0.20.2"
    "@esbuild/linux-riscv64" "0.20.2"
    "@esbuild/linux-s390x" "0.20.2"
    "@esbuild/linux-x64" "0.20.2"
    "@esbuild/netbsd-x64" "0.20.2"
    "@esbuild/openbsd-x64" "0.20.2"
    "@esbuild/sunos-x64" "0.20.2"
    "@esbuild/win32-arm64" "0.20.2"
    "@esbuild/win32-ia32" "0.20.2"
    "@esbuild/win32-x64" "0.20.2"

"escalade@^3.1.1", "escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-html@~1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-airbnb-base@15.0.0":
  "integrity" "sha512-xaX3z4ZZIcFLvh2oUNvcX5oEofXda7giYmuplVxoOg5A7EXJMrUyqRgR+mhDhPK8LZ4PttFOBvCYDbX3sUoUig=="
  "resolved" "https://registry.npmjs.org/eslint-config-airbnb-base/-/eslint-config-airbnb-base-15.0.0.tgz"
  "version" "15.0.0"
  dependencies:
    "confusing-browser-globals" "^1.0.10"
    "object.assign" "^4.1.2"
    "object.entries" "^1.1.5"
    "semver" "^6.3.0"

"eslint-config-prettier@*", "eslint-config-prettier@9.1.0":
  "integrity" "sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw=="
  "resolved" "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-9.1.0.tgz"
  "version" "9.1.0"

"eslint-import-resolver-node@^0.3.9":
  "integrity" "sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g=="
  "resolved" "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  "version" "0.3.9"
  dependencies:
    "debug" "^3.2.7"
    "is-core-module" "^2.13.0"
    "resolve" "^1.22.4"

"eslint-module-utils@^2.8.0":
  "integrity" "sha512-rXDXR3h7cs7dy9RNpUlQf80nX31XWJEyGq1tRMo+6GsO5VmTe4UTwtmonAD4ZkAsrfMVDA2wlGJ3790Ys+D49Q=="
  "resolved" "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.8.1.tgz"
  "version" "2.8.1"
  dependencies:
    "debug" "^3.2.7"

"eslint-plugin-import@^2.25.2", "eslint-plugin-import@2.29.1":
  "integrity" "sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.29.1.tgz"
  "version" "2.29.1"
  dependencies:
    "array-includes" "^3.1.7"
    "array.prototype.findlastindex" "^1.2.3"
    "array.prototype.flat" "^1.3.2"
    "array.prototype.flatmap" "^1.3.2"
    "debug" "^3.2.7"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.9"
    "eslint-module-utils" "^2.8.0"
    "hasown" "^2.0.0"
    "is-core-module" "^2.13.1"
    "is-glob" "^4.0.3"
    "minimatch" "^3.1.2"
    "object.fromentries" "^2.0.7"
    "object.groupby" "^1.0.1"
    "object.values" "^1.1.7"
    "semver" "^6.3.1"
    "tsconfig-paths" "^3.15.0"

"eslint-plugin-prettier@5.1.3":
  "integrity" "sha512-C9GCVAs4Eq7ZC/XFQHITLiHJxQngdtraXaM+LoUFoFp/lHNl2Zn8f3WQbe9HvTBBQ9YnKFB0/2Ajdqwo5D1EAw=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"
    "synckit" "^0.8.6"

"eslint-scope@^7.2.2":
  "integrity" "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-visitor-keys@^3.3.0", "eslint-visitor-keys@^3.4.1", "eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.32.0 || ^8.2.0", "eslint@>=7.0.0", "eslint@>=8.0.0", "eslint@8.57.0":
  "integrity" "sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-8.57.0.tgz"
  "version" "8.57.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.0"
    "@humanwhocodes/config-array" "^0.11.14"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.3.2"
    "doctrine" "^3.0.0"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^7.2.2"
    "eslint-visitor-keys" "^3.4.3"
    "espree" "^9.6.1"
    "esquery" "^1.4.2"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "globals" "^13.19.0"
    "graphemer" "^1.4.0"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "is-path-inside" "^3.0.3"
    "js-yaml" "^4.1.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"
    "strip-ansi" "^6.0.1"
    "text-table" "^0.2.0"

"espree@^9.6.0", "espree@^9.6.1":
  "integrity" "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "acorn" "^8.9.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^3.4.1"

"esquery@^1.4.2":
  "integrity" "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@^1.8.1", "etag@~1.8.1", "etag@1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"ev-emitter@^1.0.0":
  "integrity" "sha512-ipiDYhdQSCZ4hSbX4rMW+XzNKMD1prg/sTvoVmSLkuQ1MVlwjJQQA+sW8tMYR3BLUr9KjodFV4pvzunvRhd33Q=="
  "resolved" "https://registry.npmjs.org/ev-emitter/-/ev-emitter-1.1.1.tgz"
  "version" "1.1.1"

"eventemitter3@^2.0.3":
  "integrity" "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz"
  "version" "2.0.3"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"extend@^3.0.0", "extend@^3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz"
  "version" "1.3.0"

"fast-diff@1.1.2":
  "integrity" "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz"
  "version" "1.1.2"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
  "version" "1.17.1"
  dependencies:
    "reusify" "^1.0.4"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.1.0":
  "integrity" "sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.1"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "statuses" "~1.3.1"
    "unpipe" "~1.0.0"

"find-cache-dir@^4.0.0":
  "integrity" "sha512-9ZonPT4ZAK4a+1pUPVPZJapbi7O5qbbJPdYw/NOQWZZbVLdDTYM3A4R9z/DpAM08IDaFGsvPgiGZ82WEwUDWjg=="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "common-path-prefix" "^3.0.0"
    "pkg-dir" "^7.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"find-up@^6.3.0":
  "integrity" "sha512-v2ZsoEuVHYy8ZIlYqwPe/39Cy+cFDzp4dXPaxNvkEuouymu+2Jbz0PxpKarJHYJTmv2HWT3O382qY8l4jMWthw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-6.3.0.tgz"
  "version" "6.3.0"
  dependencies:
    "locate-path" "^7.1.0"
    "path-exists" "^5.0.0"

"fizzy-ui-utils@^2.0.0":
  "integrity" "sha512-CZXDVXQ1If3/r8s0T+v+qVeMshhfcuq0rqIFgJnrtd+Bu8GmDmqMjntjUePypVtjHXKJ6V4sw9zeyox34n9aCg=="
  "resolved" "https://registry.npmjs.org/fizzy-ui-utils/-/fizzy-ui-utils-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "desandro-matches-selector" "^2.0.0"

"flag-icons@7.2.3":
  "integrity" "sha512-X2gUdteNuqdNqob2KKTJTS+ZCvyWeLCtDz9Ty8uJP17Y4o82Y+U/Vd4JNrdwTAjagYsRznOn9DZ+E/Q52qbmqg=="
  "resolved" "https://registry.npmjs.org/flag-icons/-/flag-icons-7.2.3.tgz"
  "version" "7.2.3"

"flat-cache@^3.0.4":
  "integrity" "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.3"
    "rimraf" "^3.0.2"

"flatpickr@4.6.13":
  "integrity" "sha512-97PMG/aywoYpB4IvbvUJi0RQi8vearvU0oov1WW3k0WZPBMrTQVqekSX5CjSG/M4Q3i6A/0FKXC7RyAoAUUSPw=="
  "resolved" "https://registry.npmjs.org/flatpickr/-/flatpickr-4.6.13.tgz"
  "version" "4.6.13"

"flatted@^3.2.9":
  "integrity" "sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.3.1.tgz"
  "version" "3.3.1"

"follow-redirects@^1.0.0", "follow-redirects@^1.14.0", "follow-redirects@^1.15.6":
  "integrity" "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz"
  "version" "1.15.6"

"for-each@^0.3.3":
  "integrity" "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw=="
  "resolved" "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "is-callable" "^1.1.3"

"foreground-child@^3.1.0":
  "integrity" "sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg=="
  "resolved" "https://registry.npmjs.org/foreground-child/-/foreground-child-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "cross-spawn" "^7.0.0"
    "signal-exit" "^4.0.1"

"form-data@^2.3.1":
  "integrity" "sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"formidable@^1.2.0":
  "integrity" "sha512-KcpbcpuLNOwrEjnbpMC0gS+X8ciDoZE1kkqzat4a8vrprf+s9pKNQ/QIwWfbfs4ltgmFl3MD177SNTkve3BwGQ=="
  "resolved" "https://registry.npmjs.org/formidable/-/formidable-1.2.6.tgz"
  "version" "1.2.6"

"fraction.js@^4.3.7":
  "integrity" "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew=="
  "resolved" "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  "version" "4.3.7"

"fresh@^0.5.2", "fresh@0.5.2":
  "integrity" "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q=="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"fs-extra@3.0.1":
  "integrity" "sha512-V3Z3WZWVUYd8hoCL5xfXJCaHWYzmtwW5XWYSlLgERi8PWd8bx1kUHUk8L1BT57e49oKnDDD180mjfrHc1yA9rg=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^3.0.0"
    "universalify" "^0.1.0"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"function.prototype.name@^1.1.6":
  "integrity" "sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg=="
  "resolved" "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "functions-have-names" "^1.2.3"

"functions-have-names@^1.2.3":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"geojson-vt@^3.2.1":
  "integrity" "sha512-EvGQQi/zPrDA6zr6BnJD/YhwAkBP8nnJ9emh3EnHQKVMfg/MRVtPbMYdgVy/IaEmn4UfagD2a6fafPDL5hbtwg=="
  "resolved" "https://registry.npmjs.org/geojson-vt/-/geojson-vt-3.2.1.tgz"
  "version" "3.2.1"

"get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.1.3", "get-intrinsic@^1.2.1", "get-intrinsic@^1.2.3", "get-intrinsic@^1.2.4":
  "integrity" "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-size@^2.0.2":
  "integrity" "sha512-lXNzT/h/dTjTxRbm9BXb+SGxxzkm97h/PCIKtlN/CBCxxmkkIVV21udumMS93MuVTDX583gqc94v3RjuHmI+2Q=="
  "resolved" "https://registry.npmjs.org/get-size/-/get-size-2.0.3.tgz"
  "version" "2.0.3"

"get-stream@^6.0.1":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-symbol-description@^1.0.2":
  "integrity" "sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg=="
  "resolved" "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.5"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.4"

"gl-matrix@^3.4.3":
  "integrity" "sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA=="
  "resolved" "https://registry.npmjs.org/gl-matrix/-/gl-matrix-3.4.3.tgz"
  "version" "3.4.3"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^7.1.3":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@10.4.1":
  "integrity" "sha512-2jelhlq3E4ho74ZyVLN03oKdAZVUa6UDZzFLVH1H7dnoax+y9qyaq8zBkfDIggjniU19z0wU18y16jMB2eyVIw=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-10.4.1.tgz"
  "version" "10.4.1"
  dependencies:
    "foreground-child" "^3.1.0"
    "jackspeak" "^3.1.2"
    "minimatch" "^9.0.4"
    "minipass" "^7.1.2"
    "path-scurry" "^1.11.1"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.19.0":
  "integrity" "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
  "version" "13.24.0"
  dependencies:
    "type-fest" "^0.20.2"

"globalthis@^1.0.3":
  "integrity" "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ=="
  "resolved" "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "define-properties" "^1.2.1"
    "gopd" "^1.0.1"

"globalyzer@0.1.0":
  "integrity" "sha512-40oNTM9UfG6aBmuKxk/giHn5nQ8RVz/SS4Ir6zgzOv9/qC3kKZ9v4etGTcJbEl/NyVQH7FGU7d+X1egr57Md2Q=="
  "resolved" "https://registry.npmjs.org/globalyzer/-/globalyzer-0.1.0.tgz"
  "version" "0.1.0"

"globrex@^0.1.2":
  "integrity" "sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg=="
  "resolved" "https://registry.npmjs.org/globrex/-/globrex-0.1.2.tgz"
  "version" "0.1.2"

"good-listener@^1.2.2":
  "integrity" "sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw=="
  "resolved" "https://registry.npmjs.org/good-listener/-/good-listener-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "delegate" "^3.1.2"

"gopd@^1.0.1":
  "integrity" "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.11", "graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"graphemer@^1.4.0":
  "integrity" "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="
  "resolved" "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  "version" "1.4.0"

"grid-index@^1.1.0":
  "integrity" "sha512-HZRwumpOGUrHyxO5bqKZL0B0GlUpwtCAzZ42sgxUPniu33R1LSFH5yrIcBCHjkctCAh3mtWKcKd9J4vDDdeVHA=="
  "resolved" "https://registry.npmjs.org/grid-index/-/grid-index-1.1.0.tgz"
  "version" "1.1.0"

"hammerjs@2.0.8":
  "integrity" "sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ=="
  "resolved" "https://registry.npmjs.org/hammerjs/-/hammerjs-2.0.8.tgz"
  "version" "2.0.8"

"has-bigints@^1.0.1", "has-bigints@^1.0.2":
  "integrity" "sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ=="
  "resolved" "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
  "version" "1.0.2"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0", "has-property-descriptors@^1.0.2":
  "integrity" "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg=="
  "resolved" "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-proto@^1.0.1", "has-proto@^1.0.3":
  "integrity" "sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q=="
  "resolved" "https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz"
  "version" "1.0.3"

"has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0", "has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"hasown@^2.0.0", "hasown@^2.0.1", "hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"highlight.js@11.9.0":
  "integrity" "sha512-fJ7cW7fQGCYAkgv4CPfwFHrfd/cLS4Hau96JuJ+ZTOWhjnhoeN1ub1tFmALm/+lW5z4WCAuAV9bm05AP0mS6Gw=="
  "resolved" "https://registry.npmjs.org/highlight.js/-/highlight.js-11.9.0.tgz"
  "version" "11.9.0"

"http-errors@~1.6.2":
  "integrity" "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"iconv-lite@^0.6.3":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ieee754@^1.1.12":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore-by-default@^1.0.1":
  "integrity" "sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA=="
  "resolved" "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
  "version" "1.0.1"

"ignore@^5.2.0":
  "integrity" "sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.3.1.tgz"
  "version" "5.3.1"

"immediate@~3.0.5":
  "integrity" "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="
  "resolved" "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"immutable@^3":
  "integrity" "sha512-15gZoQ38eYjEjxkorfbcgBKBL6R7T459OuK+CpcWt7O3KF4uPCx2tD0uFETlUDIyo+1789crbMhTvQBSR5yBMg=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-3.8.2.tgz"
  "version" "3.8.2"

"immutable@^4.0.0":
  "integrity" "sha512-Ju0+lEMyzMVZarkTn/gqRpdqd5dOPaz1mCZ0SH3JV6iFw81PldE/PEB1hWVEA288HPt4WXW8O7AWxB10M+03QQ=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-4.3.6.tgz"
  "version" "4.3.6"

"import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.3":
  "integrity" "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"internal-slot@^1.0.7":
  "integrity" "sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g=="
  "resolved" "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "es-errors" "^1.3.0"
    "hasown" "^2.0.0"
    "side-channel" "^1.0.4"

"is-arguments@^1.1.1":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-array-buffer@^3.0.4":
  "integrity" "sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw=="
  "resolved" "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.2.1"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-callable@^1.1.3", "is-callable@^1.1.4", "is-callable@^1.2.7":
  "integrity" "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  "version" "1.2.7"

"is-core-module@^2.13.0", "is-core-module@^2.13.1":
  "integrity" "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw=="
  "resolved" "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "hasown" "^2.0.0"

"is-data-view@^1.0.1":
  "integrity" "sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w=="
  "resolved" "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-typed-array" "^1.1.13"

"is-date-object@^1.0.1", "is-date-object@^1.0.5":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.3":
  "integrity" "sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw=="
  "resolved" "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
  "version" "2.0.3"

"is-number-like@^1.0.3":
  "integrity" "sha512-6rZi3ezCyFcn5L71ywzz2bS5b2Igl1En3eTlZlvKjpz1n3IZLAYMbKYAIQgFmEu0GENg92ziU/faEOA/aixjbA=="
  "resolved" "https://registry.npmjs.org/is-number-like/-/is-number-like-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "lodash.isfinite" "^3.3.2"

"is-number-object@^1.0.4":
  "integrity" "sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ=="
  "resolved" "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-path-inside@^3.0.3":
  "integrity" "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ=="
  "resolved" "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-shared-array-buffer@^1.0.2", "is-shared-array-buffer@^1.0.3":
  "integrity" "sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg=="
  "resolved" "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.7"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typed-array@^1.1.13":
  "integrity" "sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw=="
  "resolved" "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.13.tgz"
  "version" "1.1.13"
  dependencies:
    "which-typed-array" "^1.1.14"

"is-weakref@^1.0.2":
  "integrity" "sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ=="
  "resolved" "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw=="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@^2.0.5":
  "integrity" "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  "version" "2.0.5"

"isarray@~1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"jackspeak@^3.1.2":
  "integrity" "sha512-JVYhQnN59LVPFCEcVa2C3CrEKYacvjRfqIQl+h8oi91aLYQVWRYbxjPcv1bUiUy/kLmQaANrYfNMCO3kuEDHfw=="
  "resolved" "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

"jest-worker@^27.4.5":
  "integrity" "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jkanban@1.3.1":
  "integrity" "sha512-5M2nQuLnYTW8ZWAj0Gzes0BVYKE2BmpvJ+wc4Kv5/WZ4A+NYH/Njw3UJbW8hnClgrRVyHbeVNe3Q4gvZzoNjaw=="
  "resolved" "https://registry.npmjs.org/jkanban/-/jkanban-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "dragula" "^3.7.3"
    "npm-watch" "^0.7.0"

"jquery-datatables-checkboxes@1.2.14":
  "integrity" "sha512-99B6PmS3ZdinDyqF5vBiykd8B+NohHRBBs/a5/hrYRlsBxbWqQ2KtigSvAc2qFdxPOyrcCV75nWBYnlrtdCGgg=="
  "resolved" "https://registry.npmjs.org/jquery-datatables-checkboxes/-/jquery-datatables-checkboxes-1.2.14.tgz"
  "version" "1.2.14"
  dependencies:
    "datatables.net" ">=1.10.8"
    "jquery" ">=1.7"

"jquery-sticky@1.0.4":
  "integrity" "sha512-AdeFsrufKRBf1yPVK2O7FRvbVCJnaiq4X100afPXOr5WoBICU8ENhsuB+gHYCh0w4GVGvSZcssWB0ehpq0u6aw=="
  "resolved" "https://registry.npmjs.org/jquery-sticky/-/jquery-sticky-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "jquery" "*"

"jquery.repeater@1.2.1":
  "integrity" "sha512-OltR1Z1AwaOsCQFBbfe1h+RnxjAwLrBa9uYkOjuj6DvyEJx0alr0ToCvfbCX1CJqq4vOMfoZUSjjKXBVog0srw=="
  "resolved" "https://registry.npmjs.org/jquery.repeater/-/jquery.repeater-1.2.1.tgz"
  "version" "1.2.1"

"jquery@*", "jquery@^3.5.0", "jquery@^3.5.1", "jquery@>=1.10", "jquery@>=1.12.0", "jquery@>=1.7", "jquery@>=1.7.x", "jquery@>=3.4.0 <4.0.0", "jquery@1.8 - 4", "jquery@1.9.1 - 3", "jquery@3.7.1":
  "integrity" "sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg=="
  "resolved" "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"
  "version" "3.7.1"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-parse-even-better-errors@^2.3.1":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^1.0.2":
  "integrity" "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2", "json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonfile@^3.0.0":
  "integrity" "sha512-oBko6ZHlubVB5mRFkur5vgYR1UyqX+S6Y/oCfLhqNdcc2fYFlDpIoNc7AfKS1KOGcnNAkvsr0grLck9ANM815w=="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-3.0.1.tgz"
  "version" "3.0.1"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jstree@3.3.16":
  "integrity" "sha512-yeeIJffi2WAqyMeHufXj/Ozy7GqgKdDkxfN8L8lwbG0h1cw/TgDafWmyhroH4AKgDSk9yW1W6jiJZu4zXAqzXw=="
  "resolved" "https://registry.npmjs.org/jstree/-/jstree-3.3.16.tgz"
  "version" "3.3.16"
  dependencies:
    "jquery" "^3.5.0"

"jszip@3.10.1":
  "integrity" "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g=="
  "resolved" "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "setimmediate" "^1.0.5"

"katex@0.16.10":
  "integrity" "sha512-ZiqaC04tp2O5utMsl2TEZTXxa6WSC4yo0fv5ML++D3QZv/vx2Mct0mTlRx3O+uUkjfuAgOkzsCmq5MiUEsDDdA=="
  "resolved" "https://registry.npmjs.org/katex/-/katex-0.16.10.tgz"
  "version" "0.16.10"
  dependencies:
    "commander" "^8.3.0"

"kdbush@^4.0.1", "kdbush@^4.0.2":
  "integrity" "sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA=="
  "resolved" "https://registry.npmjs.org/kdbush/-/kdbush-4.0.2.tgz"
  "version" "4.0.2"

"keyv@^4.5.3":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"laravel-vite-plugin@1.0.1":
  "integrity" "sha512-laLEZUnSskIDZtLb2FNRdcjsRUhh1VOVvapbVGVTeaBPJTCF/b6gbPiX2dZdcH1RKoOE0an7L+2gnInk6K33Zw=="
  "resolved" "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "picocolors" "^1.0.0"
    "vite-plugin-full-reload" "^1.1.0"

"leaflet@1.9.4":
  "integrity" "sha512-nxS1ynzJOmOlHp+iL3FyWqK89GtNL8U8rvlMOsQdTTssxZwCXh8N2NB3GDQOL+YR3XnWyZAxwQixURb+FA74PA=="
  "resolved" "https://registry.npmjs.org/leaflet/-/leaflet-1.9.4.tgz"
  "version" "1.9.4"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"limiter@^1.0.5":
  "integrity" "sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA=="
  "resolved" "https://registry.npmjs.org/limiter/-/limiter-1.1.5.tgz"
  "version" "1.1.5"

"linguist-languages@^7.27.0":
  "integrity" "sha512-Wzx/22c5Jsv2ag+uKy+ITanGA5hzvBZngrNGDXLTC7ZjGM6FLCYGgomauTkxNJeP9of353OM0pWqngYA180xgw=="
  "resolved" "https://registry.npmjs.org/linguist-languages/-/linguist-languages-7.27.0.tgz"
  "version" "7.27.0"

"loader-runner@^4.2.0":
  "integrity" "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  "version" "4.3.0"

"loader-utils@^2.0.0":
  "integrity" "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"loadjs@^4.2.0":
  "integrity" "sha512-vNX4ZZLJBeDEOBvdr2v/F+0aN5oMuPu7JTqrMwp+DtgK+AryOlpy6Xtm2/HpNr+azEa828oQjOtWsB6iDtSfSQ=="
  "resolved" "https://registry.npmjs.org/loadjs/-/loadjs-4.3.0.tgz"
  "version" "4.3.0"

"localtunnel@^2.0.1":
  "integrity" "sha512-n418Cn5ynvJd7m/N1d9WVJISLJF/ellZnfsLnx8WBWGzxv/ntNcFkJ1o6se5quUhCplfLGBNL5tYHiq5WF3Nug=="
  "resolved" "https://registry.npmjs.org/localtunnel/-/localtunnel-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "axios" "0.21.4"
    "debug" "4.3.2"
    "openurl" "1.1.1"
    "yargs" "17.1.1"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"locate-path@^7.1.0":
  "integrity" "sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "p-locate" "^6.0.0"

"lodash.debounce@^4.0.6", "lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.isfinite@^3.3.2":
  "integrity" "sha512-7FGG40uhC8Mm633uKW1r58aElFlBlxCrg9JfSi3P6aYiWmfiWF0PgMd86ZUsxE5GwWPdHoS2+48bwTh2VPkIQA=="
  "resolved" "https://registry.npmjs.org/lodash.isfinite/-/lodash.isfinite-3.3.2.tgz"
  "version" "3.3.2"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.throttle@^4.0.1":
  "integrity" "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="
  "resolved" "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash@^4.17.10", "lodash@^4.17.14", "lodash@4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lru-cache@^10.2.0":
  "integrity" "sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.2.tgz"
  "version" "10.2.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"map-age-cleaner@^0.1.3":
  "integrity" "sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w=="
  "resolved" "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "p-defer" "^1.0.0"

"mapbox-gl@3.0.1":
  "integrity" "sha512-o7C6sAlj6Hkdd4xQVEgQflgJYNYyZOAtYahhIOb9m8chI8umtWcCp8Ie0iGLYJvce1WHRMa3WGzs69ggwuWlDA=="
  "resolved" "https://registry.npmjs.org/mapbox-gl/-/mapbox-gl-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@mapbox/geojson-rewind" "^0.5.2"
    "@mapbox/jsonlint-lines-primitives" "^2.0.2"
    "@mapbox/mapbox-gl-supported" "^2.0.1"
    "@mapbox/point-geometry" "^0.1.0"
    "@mapbox/tiny-sdf" "^2.0.6"
    "@mapbox/unitbezier" "^0.0.1"
    "@mapbox/vector-tile" "^1.3.1"
    "@mapbox/whoots-js" "^3.1.0"
    "cheap-ruler" "^3.0.1"
    "csscolorparser" "~1.0.3"
    "earcut" "^2.2.4"
    "geojson-vt" "^3.2.1"
    "gl-matrix" "^3.4.3"
    "grid-index" "^1.1.0"
    "kdbush" "^4.0.1"
    "murmurhash-js" "^1.0.0"
    "pbf" "^3.2.1"
    "potpack" "^2.0.0"
    "quickselect" "^2.0.0"
    "rw" "^1.3.3"
    "supercluster" "^8.0.0"
    "tinyqueue" "^2.0.3"
    "vt-pbf" "^3.1.3"

"masonry-layout@4.2.2":
  "integrity" "sha512-iGtAlrpHNyxaR19CvKC3npnEcAwszXoyJiI8ARV2ePi7fmYhIud25MHK8Zx4P0LCC4d3TNO9+rFa1KoK1OEOaA=="
  "resolved" "https://registry.npmjs.org/masonry-layout/-/masonry-layout-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "get-size" "^2.0.2"
    "outlayer" "^2.1.0"

"mem@^9.0.2":
  "integrity" "sha512-F2t4YIv9XQUBHt6AOJ0y7lSmP1+cY7Fm1DRh9GClTGzKST7UWLMx6ly9WZdLH/G/ppM5RL4MlQfRT71ri9t19A=="
  "resolved" "https://registry.npmjs.org/mem/-/mem-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "map-age-cleaner" "^0.1.3"
    "mimic-fn" "^4.0.0"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"methods@^1.1.1":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^4.0.2":
  "integrity" "sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.7.tgz"
  "version" "4.0.7"
  dependencies:
    "braces" "^3.0.3"
    "picomatch" "^2.3.1"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@~2.1.17", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1", "mime@1.4.1":
  "integrity" "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz"
  "version" "1.4.1"

"mimic-fn@^4.0.0":
  "integrity" "sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz"
  "version" "4.0.0"

"minimatch@^3.0.2", "minimatch@^3.0.5", "minimatch@^3.1.1", "minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^9.0.4":
  "integrity" "sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-9.0.4.tgz"
  "version" "9.0.4"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist@^1.2.0", "minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", "minipass@^7.1.2":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"mitt@^1.1.3":
  "integrity" "sha512-r6lj77KlwqLhIUku9UWYes7KJtsczvolZkzp8hbaDPPaE24OmWl5s539Mytlj22siEQKosZ26qCBgda2PKwoJw=="
  "resolved" "https://registry.npmjs.org/mitt/-/mitt-1.2.0.tgz"
  "version" "1.2.0"

"moment@^2.9.0", "moment@2.30.1":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"murmurhash-js@^1.0.0":
  "integrity" "sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw=="
  "resolved" "https://registry.npmjs.org/murmurhash-js/-/murmurhash-js-1.0.0.tgz"
  "version" "1.0.0"

"nanoid@^3.3.7":
  "integrity" "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  "version" "3.3.7"

"nanopop@2.4.2":
  "integrity" "sha512-NzOgmMQ+elxxHeIha+OG/Pv3Oc3p4RU2aBhwWwAqDpXrdTbtRylbRLQztLy8dMMwfl6pclznBdfUhccEn9ZIzw=="
  "resolved" "https://registry.npmjs.org/nanopop/-/nanopop-2.4.2.tgz"
  "version" "2.4.2"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"node-releases@^2.0.19":
  "integrity" "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  "version" "2.0.19"

"node-watch@0.7.3":
  "integrity" "sha512-3l4E8uMPY1HdMMryPRUAl+oIHtXtyiTlIiESNSVSNxcPfzAFzeTbXFQkZfAwBbo0B1qMSG8nUABx+Gd+YrbKrQ=="
  "resolved" "https://registry.npmjs.org/node-watch/-/node-watch-0.7.3.tgz"
  "version" "0.7.3"

"node-waves@0.7.6":
  "integrity" "sha512-tVUIzekLZb7xMTxE4llCivOCE3ECg/Rlf4KfUD2FS3PI4Awk1I8j8xmPaFUAIEZVI6Io3132JQRYxLetR2NYUQ=="
  "resolved" "https://registry.npmjs.org/node-waves/-/node-waves-0.7.6.tgz"
  "version" "0.7.6"

"nodemon@^2.0.3":
  "integrity" "sha512-B8YqaKMmyuCO7BowF1Z1/mkPqLk6cs/l63Ojtd6otKjMx47Dq1utxfRxcavH1I7VSaL8n5BUaoutadnsX3AAVQ=="
  "resolved" "https://registry.npmjs.org/nodemon/-/nodemon-2.0.22.tgz"
  "version" "2.0.22"
  dependencies:
    "chokidar" "^3.5.2"
    "debug" "^3.2.7"
    "ignore-by-default" "^1.0.1"
    "minimatch" "^3.1.2"
    "pstree.remy" "^1.1.8"
    "semver" "^5.7.1"
    "simple-update-notifier" "^1.0.7"
    "supports-color" "^5.5.0"
    "touch" "^3.1.0"
    "undefsafe" "^2.0.5"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"nouislider@15.7.2":
  "integrity" "sha512-Jn1xuJZk0cs7XdzFD2tKucedlvi5/eEAsyewPS70Qqba3a1vcyEey2VZFmp7JiS06QfgkD0kEFk8oMnJrLDWXQ=="
  "resolved" "https://registry.npmjs.org/nouislider/-/nouislider-15.7.2.tgz"
  "version" "15.7.2"

"npm-watch@^0.7.0":
  "integrity" "sha512-AN2scNyMljMGkn0mIkaRRk19I7Vx0qTK6GmsIcDblX5YRbSsoJORTAtrceICSx7Om9q48NWcwm/R0t6E7F4Ocg=="
  "resolved" "https://registry.npmjs.org/npm-watch/-/npm-watch-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "nodemon" "^2.0.3"
    "through2" "^2.0.0"

"numeral@2.0.6":
  "integrity" "sha512-qaKRmtYPZ5qdw4jWJD6bxEf1FJEqllJrwxCLIm0sQU/A7v2/czigzOb+C2uSiFsa9lBUzeH7M1oK+Q+OLxL3kA=="
  "resolved" "https://registry.npmjs.org/numeral/-/numeral-2.0.6.tgz"
  "version" "2.0.6"

"object-assign@^4", "object-assign@^4.0.1", "object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-inspect@^1.13.1":
  "integrity" "sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz"
  "version" "1.13.1"

"object-is@^1.1.5":
  "integrity" "sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object.assign@^4.1.2", "object.assign@^4.1.5":
  "integrity" "sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "call-bind" "^1.0.5"
    "define-properties" "^1.2.1"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"object.entries@^1.1.5":
  "integrity" "sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ=="
  "resolved" "https://registry.npmjs.org/object.entries/-/object.entries-1.1.8.tgz"
  "version" "1.1.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"object.fromentries@^2.0.7":
  "integrity" "sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ=="
  "resolved" "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz"
  "version" "2.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-object-atoms" "^1.0.0"

"object.groupby@^1.0.1":
  "integrity" "sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ=="
  "resolved" "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"

"object.values@^1.1.7":
  "integrity" "sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ=="
  "resolved" "https://registry.npmjs.org/object.values/-/object.values-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"on-finished@~2.3.0":
  "integrity" "sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww=="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"once@^1.3.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"openurl@1.1.1":
  "integrity" "sha512-d/gTkTb1i1GKz5k3XE3XFV/PxQ1k45zDqGP2OA7YhgsaLoqm6qRvARAZOFer1fcXritWlGBRCu/UgeS4HAnXAA=="
  "resolved" "https://registry.npmjs.org/openurl/-/openurl-1.1.1.tgz"
  "version" "1.1.1"

"opn@5.3.0":
  "integrity" "sha512-bYJHo/LOmoTd+pfiYhfZDnf9zekVJrY+cnS2a5F2x+w5ppvTqObojTP7WiFG+kVZs9Inw+qQ/lw7TroWwhdd2g=="
  "resolved" "https://registry.npmjs.org/opn/-/opn-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.9.3":
  "integrity" "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  "version" "0.9.4"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.5"

"outlayer@^2.1.0":
  "integrity" "sha512-+GplXsCQ3VrbGujAeHEzP9SXsBmJxzn/YdDSQZL0xqBmAWBmortu2Y9Gwdp9J0bgDQ8/YNIPMoBM13nTwZfAhw=="
  "resolved" "https://registry.npmjs.org/outlayer/-/outlayer-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ev-emitter" "^1.0.0"
    "fizzy-ui-utils" "^2.0.0"
    "get-size" "^2.0.2"

"p-defer@^1.0.0":
  "integrity" "sha512-wB3wfAxZpk2AzOfUMJNL+d36xothRSyj8EXOa4f6GMqYDN9BJaaSISbsk+wS9abmnebVw95C2Kb5t85UmpCxuw=="
  "resolved" "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-limit@^4.0.0":
  "integrity" "sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "yocto-queue" "^1.0.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-locate@^6.0.0":
  "integrity" "sha512-wPrq66Llhl7/4AGC6I+cqxT07LhXvWL08LNXz1fENOw0Ap4sRZZ/gZpTTJ5jpurzzzfS2W/Ge9BY3LgLjCShcw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-limit" "^4.0.0"

"pako@^0.2.5":
  "integrity" "sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz"
  "version" "0.2.9"

"pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parchment@^1.1.4":
  "integrity" "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg=="
  "resolved" "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz"
  "version" "1.1.4"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parseurl@~1.3.2":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-exists@^5.0.0":
  "integrity" "sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-5.0.0.tgz"
  "version" "5.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-scurry@^1.11.1":
  "integrity" "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA=="
  "resolved" "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "lru-cache" "^10.2.0"
    "minipass" "^5.0.0 || ^6.0.2 || ^7.0.0"

"pbf@^3.2.1":
  "integrity" "sha512-ClrV7pNOn7rtmoQVF4TS1vyU0WhYRnP92fzbfF75jAIwpnzdJXf8iTd4CMEqO4yUenH6NDqLiwjqlh6QgZzgLQ=="
  "resolved" "https://registry.npmjs.org/pbf/-/pbf-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "ieee754" "^1.1.12"
    "resolve-protobuf-schema" "^2.1.0"

"pdfmake@0.2.10":
  "integrity" "sha512-doipFnmE1UHSk+Z3wfQuVweVQqx2pE/Ns2G5gCqZmWwqjDj+mZHnZYH/ryXWoIfD+iVdZUAutgI/VHkTCN+Xrw=="
  "resolved" "https://registry.npmjs.org/pdfmake/-/pdfmake-0.2.10.tgz"
  "version" "0.2.10"
  dependencies:
    "@foliojs-fork/linebreak" "^1.1.1"
    "@foliojs-fork/pdfkit" "^0.14.0"
    "iconv-lite" "^0.6.3"
    "xmldoc" "^1.1.2"

"perfect-scrollbar@1.5.5":
  "integrity" "sha512-dzalfutyP3e/FOpdlhVryN4AJ5XDVauVWxybSkLZmakFE2sS3y3pc4JnSprw8tGmHvkaG5Edr5T7LBTZ+WWU2g=="
  "resolved" "https://registry.npmjs.org/perfect-scrollbar/-/perfect-scrollbar-1.5.5.tgz"
  "version" "1.5.5"

"php-parser@^3.1.5":
  "integrity" "sha512-jEY2DcbgCm5aclzBdfW86GM6VEIWcSlhTBSHN1qhJguVePlYe28GhwS0yoeLYXpM2K8y6wzLwrbq814n2PHSoQ=="
  "resolved" "https://registry.npmjs.org/php-parser/-/php-parser-3.1.5.tgz"
  "version" "3.1.5"

"picocolors@^1.0.0", "picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pkg-dir@^7.0.0":
  "integrity" "sha512-Ie9z/WINcxxLp27BKOCHGde4ITq9UklYKDzVo1nhk5sqGEXU3FpkwP5GM2voTGJkGd9B3Otl+Q4uwSOeSUtOBA=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "find-up" "^6.3.0"

"plyr@3.7.8":
  "integrity" "sha512-yG/EHDobwbB/uP+4Bm6eUpJ93f8xxHjjk2dYcD1Oqpe1EcuQl5tzzw9Oq+uVAzd2lkM11qZfydSiyIpiB8pgdA=="
  "resolved" "https://registry.npmjs.org/plyr/-/plyr-3.7.8.tgz"
  "version" "3.7.8"
  dependencies:
    "core-js" "^3.26.1"
    "custom-event-polyfill" "^1.0.7"
    "loadjs" "^4.2.0"
    "rangetouch" "^2.0.1"
    "url-polyfill" "^1.1.12"

"png-js@^1.0.0":
  "integrity" "sha512-k+YsbhpA9e+EFfKjTCH3VW6aoKlyNYI6NYdTfDL4CIvFnvsuO84ttonmZE7rc+v23SLTH8XX+5w/Ak9v0xGY4g=="
  "resolved" "https://registry.npmjs.org/png-js/-/png-js-1.0.0.tgz"
  "version" "1.0.0"

"popper.js@^1.16.1":
  "integrity" "sha512-Wb4p1J4zyFTbM+u6WuO4XstYx4Ky9Cewe4DWrel7B0w6VVICvPwdOpotjzcf6eD8TsckVnIMNONQyPIUFOUbCQ=="
  "resolved" "https://registry.npmjs.org/popper.js/-/popper.js-1.16.1.tgz"
  "version" "1.16.1"

"portscanner@2.2.0":
  "integrity" "sha512-IFroCz/59Lqa2uBvzK3bKDbDDIEaAY8XJ1jFxcLWTqosrsc32//P4VuSB2vZXoHiHqOmx8B5L5hnKOxL/7FlPw=="
  "resolved" "https://registry.npmjs.org/portscanner/-/portscanner-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "async" "^2.6.0"
    "is-number-like" "^1.0.3"

"possible-typed-array-names@^1.0.0":
  "integrity" "sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q=="
  "resolved" "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
  "version" "1.0.0"

"postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^8.1.0", "postcss@^8.2.14", "postcss@^8.4.38", "postcss@8.4.38":
  "integrity" "sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.4.38.tgz"
  "version" "8.4.38"
  dependencies:
    "nanoid" "^3.3.7"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.2.0"

"potpack@^2.0.0":
  "integrity" "sha512-Q+/tYsFU9r7xoOJ+y/ZTtdVQwTWfzjbiXBDMM/JKUux3+QPP02iUuIoeBQ+Ot6oEDlC+/PGjB/5A3K7KKb7hcw=="
  "resolved" "https://registry.npmjs.org/potpack/-/potpack-2.0.0.tgz"
  "version" "2.0.0"

"preact@~10.12.1":
  "integrity" "sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg=="
  "resolved" "https://registry.npmjs.org/preact/-/preact-10.12.1.tgz"
  "version" "10.12.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^3.0.0", "prettier@>=3.0.0", "prettier@3.2.2":
  "integrity" "sha512-HTByuKZzw7utPiDO523Tt2pLtEyK7OibUD9suEJQrPUCYQqrHr74GGX6VidMrovbf/I50mPqr8j/II6oBAuc5A=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-3.2.2.tgz"
  "version" "3.2.2"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"prop-types@15.8.1":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"protocol-buffers-schema@^3.3.1":
  "integrity" "sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw=="
  "resolved" "https://registry.npmjs.org/protocol-buffers-schema/-/protocol-buffers-schema-3.6.0.tgz"
  "version" "3.6.0"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"pstree.remy@^1.1.8":
  "integrity" "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w=="
  "resolved" "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz"
  "version" "1.1.8"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"qs@^6.5.1":
  "integrity" "sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.12.1.tgz"
  "version" "6.12.1"
  dependencies:
    "side-channel" "^1.0.6"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quickselect@^2.0.0":
  "integrity" "sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw=="
  "resolved" "https://registry.npmjs.org/quickselect/-/quickselect-2.0.0.tgz"
  "version" "2.0.0"

"quill-delta@^3.6.2":
  "integrity" "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg=="
  "resolved" "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz"
  "version" "3.6.3"
  dependencies:
    "deep-equal" "^1.0.1"
    "extend" "^3.0.2"
    "fast-diff" "1.1.2"

"quill@1.3.7":
  "integrity" "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g=="
  "resolved" "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "clone" "^2.1.1"
    "deep-equal" "^1.0.1"
    "eventemitter3" "^2.0.3"
    "extend" "^3.0.2"
    "parchment" "^1.1.4"
    "quill-delta" "^3.6.2"

"qunit@^2.10.0":
  "integrity" "sha512-kJJ+uzx5xDWk0oRrbOZ3zsm+imPULE58ZMIrNl+3POZl4a1k6VXj2E4OiqTmZ9j6hh9egE3kNgnAti9Q+BG6Yw=="
  "resolved" "https://registry.npmjs.org/qunit/-/qunit-2.21.0.tgz"
  "version" "2.21.0"
  dependencies:
    "commander" "7.2.0"
    "node-watch" "0.7.3"
    "tiny-glob" "0.2.9"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@~1.2.0":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"rangetouch@^2.0.1":
  "integrity" "sha512-sln+pNSc8NGaHoLzwNBssFSf/rSYkqeBXzX1AtJlkJiUaVSJSbRAWJk+4omsXkN+EJalzkZhWQ3th1m0FpR5xA=="
  "resolved" "https://registry.npmjs.org/rangetouch/-/rangetouch-2.0.1.tgz"
  "version" "2.0.1"

"rateyo@2.3.5":
  "integrity" "sha512-X0ewsUTzZiJwxJkca4RhRn6q53OkpuHoRdF9VFPluhP84N8NM2JnD/uBUT4QOtCj/5dNa0A+bEkJA7n/gWsYag=="
  "resolved" "https://registry.npmjs.org/rateyo/-/rateyo-2.3.5.tgz"
  "version" "2.3.5"

"raw-body@^2.3.2":
  "integrity" "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"readable-stream@^2.3.5", "readable-stream@~2.3.6":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^10.1.0":
  "integrity" "sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.1.tgz"
  "version" "10.1.1"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"regenerator-transform@^0.15.2":
  "integrity" "sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.2.tgz"
  "version" "0.15.2"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-parser@^2.2.11":
  "integrity" "sha512-TVILVSz2jY5D47F4mA4MppkBrafEaiUWJO/TcZHEIuI13AqoZMkK1WMA4Om1YkYbTx+9Ki1/tSUXbceyr9saRg=="
  "resolved" "https://registry.npmjs.org/regex-parser/-/regex-parser-2.3.0.tgz"
  "version" "2.3.0"

"regexp.prototype.flags@^1.5.1", "regexp.prototype.flags@^1.5.2":
  "integrity" "sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz"
  "version" "1.5.2"
  dependencies:
    "call-bind" "^1.0.6"
    "define-properties" "^1.2.1"
    "es-errors" "^1.3.0"
    "set-function-name" "^2.0.1"

"regexpu-core@^5.3.1":
  "integrity" "sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.1.0"
    "regjsparser" "^0.9.1"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.1.0"

"regjsparser@^0.9.1":
  "integrity" "sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "jsesc" "~0.5.0"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-protobuf-schema@^2.1.0":
  "integrity" "sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ=="
  "resolved" "https://registry.npmjs.org/resolve-protobuf-schema/-/resolve-protobuf-schema-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "protocol-buffers-schema" "^3.3.1"

"resolve-url-loader@5.0.0":
  "integrity" "sha512-uZtduh8/8srhBoMx//5bwqjQ+rfYOUq8zC9NrMUGtjBiGTtFJM42s58/36+hTqeqINcnYe08Nj3LkK9lW4N8Xg=="
  "resolved" "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "adjust-sourcemap-loader" "^4.0.0"
    "convert-source-map" "^1.7.0"
    "loader-utils" "^2.0.0"
    "postcss" "^8.2.14"
    "source-map" "0.6.1"

"resolve@^1.14.2", "resolve@^1.22.4":
  "integrity" "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  "version" "1.22.8"
  dependencies:
    "is-core-module" "^2.13.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"resp-modifier@6.0.2":
  "integrity" "sha512-U1+0kWC/+4ncRFYqQWTx/3qkfE6a4B/h3XXgmXypfa0SPZ3t7cbbaFk297PjQS/yov24R18h6OZe6iZwj3NSLw=="
  "resolved" "https://registry.npmjs.org/resp-modifier/-/resp-modifier-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "^2.2.0"
    "minimatch" "^3.0.2"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rollup@^1.20.0||^2.0.0||^3.0.0||^4.0.0", "rollup@^4.13.0":
  "integrity" "sha512-QmJz14PX3rzbJCN1SG4Xe/bAAX2a6NpCP8ab2vfu2GiUr8AQcr2nCV/oEO3yneFarB67zk8ShlIyWb2LGTb3Sg=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-4.18.0.tgz"
  "version" "4.18.0"
  dependencies:
    "@types/estree" "1.0.5"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.18.0"
    "@rollup/rollup-android-arm64" "4.18.0"
    "@rollup/rollup-darwin-arm64" "4.18.0"
    "@rollup/rollup-darwin-x64" "4.18.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.18.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.18.0"
    "@rollup/rollup-linux-arm64-gnu" "4.18.0"
    "@rollup/rollup-linux-arm64-musl" "4.18.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.18.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.18.0"
    "@rollup/rollup-linux-s390x-gnu" "4.18.0"
    "@rollup/rollup-linux-x64-gnu" "4.18.0"
    "@rollup/rollup-linux-x64-musl" "4.18.0"
    "@rollup/rollup-win32-arm64-msvc" "4.18.0"
    "@rollup/rollup-win32-ia32-msvc" "4.18.0"
    "@rollup/rollup-win32-x64-msvc" "4.18.0"
    "fsevents" "~2.3.2"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rw@^1.3.3":
  "integrity" "sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ=="
  "resolved" "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz"
  "version" "1.3.3"

"rx@4.1.0":
  "integrity" "sha512-CiaiuN6gapkdl+cZUr67W6I8jquN4lkak3vtIsIWCl4XIPP8ffsoyN6/+PuGXnQy8Cu8W2y9Xxh31Rq4M6wUug=="
  "resolved" "https://registry.npmjs.org/rx/-/rx-4.1.0.tgz"
  "version" "4.1.0"

"safe-array-concat@^1.1.2":
  "integrity" "sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q=="
  "resolved" "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.7"
    "get-intrinsic" "^1.2.4"
    "has-symbols" "^1.0.3"
    "isarray" "^2.0.5"

"safe-buffer@^5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex-test@^1.0.3":
  "integrity" "sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw=="
  "resolved" "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-regex" "^1.1.4"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-loader@14.0.0":
  "integrity" "sha512-oceP9wWbep/yRJ2+sMbCzk0UsXsDzdNis+N8nu9i5GwPXjy6v3DNB6TqfJLSpPO9k4+B8x8p/CEgjA9ZLkoLug=="
  "resolved" "https://registry.npmjs.org/sass-loader/-/sass-loader-14.0.0.tgz"
  "version" "14.0.0"
  dependencies:
    "neo-async" "^2.6.2"

"sass@*", "sass@^1.3.0", "sass@1.77.4":
  "integrity" "sha512-vcF3Ckow6g939GMA4PeU7b2K/9FALXk2KF9J87txdHzXbUF9XRQRwSxcAs/fGaTnJeBFd7UoV22j3lzMLdM0Pw=="
  "resolved" "https://registry.npmjs.org/sass/-/sass-1.77.4.tgz"
  "version" "1.77.4"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"sax@^1.2.4":
  "integrity" "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz"
  "version" "1.4.1"

"schema-utils@^4.0.0", "schema-utils@^4.3.0", "schema-utils@^4.3.2":
  "integrity" "sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"select@^1.1.2":
  "integrity" "sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA=="
  "resolved" "https://registry.npmjs.org/select/-/select-1.1.2.tgz"
  "version" "1.1.2"

"select2@4.0.13":
  "integrity" "sha512-1JeB87s6oN/TDxQQYCvS5EFoQyvV6eYMZZ0AeA4tdFDYWN3BAGZ8npr17UBFddU0lgAt3H0yjX3X6/ekOj1yjw=="
  "resolved" "https://registry.npmjs.org/select2/-/select2-4.0.13.tgz"
  "version" "4.0.13"

"semver@^5.7.1":
  "integrity" "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
  "version" "5.7.2"

"semver@^6.3.0", "semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@~7.0.0":
  "integrity" "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.16.2":
  "integrity" "sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.16.2.tgz"
  "version" "0.16.2"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.6.2"
    "mime" "1.4.1"
    "ms" "2.0.0"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.0"
    "statuses" "~1.4.0"

"serialize-javascript@^6.0.2":
  "integrity" "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@1.9.1":
  "integrity" "sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw=="
  "resolved" "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.13.2":
  "integrity" "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.2"
    "send" "0.16.2"

"server-destroy@1.0.1":
  "integrity" "sha512-rb+9B5YBIEzYcD6x2VKidaa+cqYBJQKnU4oe4E3ANwRRN56yk/ua1YCJT1n21NTS8w6CcOclAKNP3PhdCXKYtQ=="
  "resolved" "https://registry.npmjs.org/server-destroy/-/server-destroy-1.0.1.tgz"
  "version" "1.0.1"

"set-function-length@^1.2.1":
  "integrity" "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg=="
  "resolved" "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"set-function-name@^2.0.1":
  "integrity" "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ=="
  "resolved" "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "functions-have-names" "^1.2.3"
    "has-property-descriptors" "^1.0.2"

"setimmediate@^1.0.5":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shepherd.js@11.2.0":
  "integrity" "sha512-2hbz3N7GuuTjI7y3sfnoqKnH0cNhExx67IJtCTGQI2KhBEyvegsDYW5qjj5BlvvVtQjmL/O/J1GQEciwfoZWpw=="
  "resolved" "https://registry.npmjs.org/shepherd.js/-/shepherd.js-11.2.0.tgz"
  "version" "11.2.0"
  dependencies:
    "@floating-ui/dom" "^1.5.1"
    "deepmerge" "^4.3.1"

"side-channel@^1.0.4", "side-channel@^1.0.6":
  "integrity" "sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.4"
    "object-inspect" "^1.13.1"

"signal-exit@^4.0.1":
  "integrity" "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  "version" "4.1.0"

"simple-update-notifier@^1.0.7":
  "integrity" "sha512-VpsrsJSUcJEseSbMHkrsrAVSdvVS5I96Qo1QAQ4FxQ9wXFcB+pjj7FB7/us9+GcgfW4ziHtYMc1J0PLczb55mg=="
  "resolved" "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "semver" "~7.0.0"

"socket.io-adapter@~2.5.2":
  "integrity" "sha512-wDNHGXGewWAjQPt3pyeYBtpWSq9cLE5UW1ZUPL/2eGK9jtse/FpXib7epSTsz0Q0m+6sg6Y4KtcFTlah1bdOVg=="
  "resolved" "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.5.4.tgz"
  "version" "2.5.4"
  dependencies:
    "debug" "~4.3.4"
    "ws" "~8.11.0"

"socket.io-client@^4.4.1":
  "integrity" "sha512-sJ/tqHOCe7Z50JCBCXrsY3I2k03iOiUe+tj1OmKeD2lXPiGH/RUCdTZFoqVyN7l1MnpIzPrGtLcijffmeouNlQ=="
  "resolved" "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.7.5.tgz"
  "version" "4.7.5"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.2"
    "engine.io-client" "~6.5.2"
    "socket.io-parser" "~4.2.4"

"socket.io-parser@~4.2.4":
  "integrity" "sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew=="
  "resolved" "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"

"socket.io@^4.4.1":
  "integrity" "sha512-DmeAkF6cwM9jSfmp6Dr/5/mfMwb5Z5qRrSXLpo3Fq5SqyU8CMF15jIN4ZhfSwu35ksM1qmHZDQ/DK5XTccSTvA=="
  "resolved" "https://registry.npmjs.org/socket.io/-/socket.io-4.7.5.tgz"
  "version" "4.7.5"
  dependencies:
    "accepts" "~1.3.4"
    "base64id" "~2.0.0"
    "cors" "~2.8.5"
    "debug" "~4.3.2"
    "engine.io" "~6.5.2"
    "socket.io-adapter" "~2.5.2"
    "socket.io-parser" "~4.2.4"

"sortablejs@1.15.2":
  "integrity" "sha512-FJF5jgdfvoKn1MAKSdGs33bIqLi3LmsgVTliuX6iITj834F+JRQZN90Z93yql8h0K2t0RwDPBmxwlbZfDcxNZA=="
  "resolved" "https://registry.npmjs.org/sortablejs/-/sortablejs-1.15.2.tgz"
  "version" "1.15.2"

"source-map-js@^1.2.0", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.0.tgz"
  "version" "1.2.0"

"source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map@^0.6.0", "source-map@0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spinkit@2.0.1":
  "integrity" "sha512-oYBGY0GV1H1dX+ZdKnB6JVsYC1w/Xl20H111eb+WSS8nUYmlHgGb4y5buFSkzzceEeYYh5kMhXoAmoTpiQauiA=="
  "resolved" "https://registry.npmjs.org/spinkit/-/spinkit-2.0.1.tgz"
  "version" "2.0.1"

"statuses@>= 1.4.0 < 2", "statuses@~1.4.0":
  "integrity" "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz"
  "version" "1.4.0"

"statuses@~1.3.1":
  "integrity" "sha512-wuTCPGlJONk/a1kqZ4fQM2+908lC7fa7nPYpTC1EhnvqLX/IICbeP1OZGDtA374trpSq68YubKUMo8oRhN46yg=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz"
  "version" "1.3.1"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"storage2@^0.1.0":
  "integrity" "sha512-I6VlBrP9/6GN9J1eMGPiSE2FBtHhKj1yF8NXPH+lpgMY8y/nZ6a22Fm4vHIqEqLrjQ/H2WnOC/fG4ONBm2UTBQ=="
  "resolved" "https://registry.npmjs.org/storage2/-/storage2-0.1.2.tgz"
  "version" "0.1.2"

"stream-throttle@^0.1.3":
  "integrity" "sha512-889+B9vN9dq7/vLbGyuHeZ6/ctf5sNuGWsDy89uNxkFTAgzy0eK7+w5fL3KLNRTkLle7EgZGvHUphZW0Q26MnQ=="
  "resolved" "https://registry.npmjs.org/stream-throttle/-/stream-throttle-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "commander" "^2.2.0"
    "limiter" "^1.0.5"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^5.0.1", "string-width@^5.1.2":
  "integrity" "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "eastasianwidth" "^0.2.0"
    "emoji-regex" "^9.2.2"
    "strip-ansi" "^7.0.1"

"string.prototype.trim@^1.2.9":
  "integrity" "sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw=="
  "resolved" "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz"
  "version" "1.2.9"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.0"
    "es-object-atoms" "^1.0.0"

"string.prototype.trimend@^1.0.8":
  "integrity" "sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"string.prototype.trimstart@^1.0.8":
  "integrity" "sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.1":
  "integrity" "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-bom@^3.0.0":
  "integrity" "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"superagent@^3.8.3":
  "integrity" "sha512-GLQtLMCoEIK4eDv6OGtkOoSMt3D+oq0y3dsxMuYuDvaNUvuT8eFBuLmfR0iYYzHC1e8hpzC6ZsxbuP6DIalMFA=="
  "resolved" "https://registry.npmjs.org/superagent/-/superagent-3.8.3.tgz"
  "version" "3.8.3"
  dependencies:
    "component-emitter" "^1.2.0"
    "cookiejar" "^2.1.0"
    "debug" "^3.1.0"
    "extend" "^3.0.0"
    "form-data" "^2.3.1"
    "formidable" "^1.2.0"
    "methods" "^1.1.1"
    "mime" "^1.4.1"
    "qs" "^6.5.1"
    "readable-stream" "^2.3.5"

"supercluster@^8.0.0":
  "integrity" "sha512-IiOea5kJ9iqzD2t7QJq/cREyLHTtSmUT6gQsweojg9WH2sYJqZK9SswTu6jrscO6D1G5v5vYZ9ru/eq85lXeZQ=="
  "resolved" "https://registry.npmjs.org/supercluster/-/supercluster-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "kdbush" "^4.0.2"

"supports-color@^5.3.0", "supports-color@^5.5.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg.draggable.js@^2.2.2":
  "integrity" "sha512-JzNHBc2fLQMzYCZ90KZHN2ohXL0BQJGQimK1kGk6AvSeibuKcIdDX9Kr0dT9+UJ5O8nYA0RB839Lhvk4CY4MZw=="
  "resolved" "https://registry.npmjs.org/svg.draggable.js/-/svg.draggable.js-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "svg.js" "^2.0.1"

"svg.easing.js@^2.0.0":
  "integrity" "sha512-//ctPdJMGy22YoYGV+3HEfHbm6/69LJUTAqI2/5qBvaNHZ9uUFVC82B0Pl299HzgH13rKrBgi4+XyXXyVWWthA=="
  "resolved" "https://registry.npmjs.org/svg.easing.js/-/svg.easing.js-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "svg.js" ">=2.3.x"

"svg.filter.js@^2.0.2":
  "integrity" "sha512-xkGBwU+dKBzqg5PtilaTb0EYPqPfJ9Q6saVldX+5vCRy31P6TlRCP3U9NxH3HEufkKkpNgdTLBJnmhDHeTqAkw=="
  "resolved" "https://registry.npmjs.org/svg.filter.js/-/svg.filter.js-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "svg.js" "^2.2.5"

"svg.js@^2.0.1", "svg.js@^2.2.5", "svg.js@^2.4.0", "svg.js@^2.6.5", "svg.js@>=2.3.x":
  "integrity" "sha512-ycbxpizEQktk3FYvn/8BH+6/EuWXg7ZpQREJvgacqn46gIddG24tNNe4Son6omdXCnSOaApnpZw6MPCBA1dODA=="
  "resolved" "https://registry.npmjs.org/svg.js/-/svg.js-2.7.1.tgz"
  "version" "2.7.1"

"svg.pathmorphing.js@^0.1.3":
  "integrity" "sha512-49HWI9X4XQR/JG1qXkSDV8xViuTLIWm/B/7YuQELV5KMOPtXjiwH4XPJvr/ghEDibmLQ9Oc22dpWpG0vUDDNww=="
  "resolved" "https://registry.npmjs.org/svg.pathmorphing.js/-/svg.pathmorphing.js-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "svg.js" "^2.4.0"

"svg.resize.js@^1.4.3":
  "integrity" "sha512-9k5sXJuPKp+mVzXNvxz7U0uC9oVMQrrf7cFsETznzUDDm0x8+77dtZkWdMfRlmbkEEYvUn9btKuZ3n41oNA+uw=="
  "resolved" "https://registry.npmjs.org/svg.resize.js/-/svg.resize.js-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "svg.js" "^2.6.5"
    "svg.select.js" "^2.1.2"

"svg.select.js@^2.1.2":
  "integrity" "sha512-tH6ABEyJsAOVAhwcCjF8mw4crjXSI1aa7j2VQR8ZuJ37H2MBUbyeqYr5nEO7sSN3cy9AR9DUwNg0t/962HlDbQ=="
  "resolved" "https://registry.npmjs.org/svg.select.js/-/svg.select.js-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "svg.js" "^2.2.5"

"svg.select.js@^3.0.1":
  "integrity" "sha512-h5IS/hKkuVCbKSieR9uQCj9w+zLHoPh+ce19bBYyqF53g6mnPB8sAtIbe1s9dh2S2fCmYX2xel1Ln3PJBbK4kw=="
  "resolved" "https://registry.npmjs.org/svg.select.js/-/svg.select.js-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "svg.js" "^2.6.5"

"sweetalert2@11.10.8":
  "integrity" "sha512-oAkYROBfXBY+4sVbQEIcN+ZxAx69lsmz5WEBwdEpyS4m59vOBNlRU5/fJpAI1MVfiDwFZiGwVzB/KBpOyfLNtg=="
  "resolved" "https://registry.npmjs.org/sweetalert2/-/sweetalert2-11.10.8.tgz"
  "version" "11.10.8"

"swiper@11.0.7":
  "integrity" "sha512-cDfglW1B6uSmB6eB6pNmzDTNLmZtu5bWWa1vak0RU7fOI9qHjMzl7gVBvYSl34b0RU2N11HxxETJqQ5LeqI1cA=="
  "resolved" "https://registry.npmjs.org/swiper/-/swiper-11.0.7.tgz"
  "version" "11.0.7"

"synckit@^0.8.6":
  "integrity" "sha512-HwOKAP7Wc5aRGYdKH+dw0PRRpbO841v2DENBtjnR5HFWoiNByAl7vrx3p0G/rCyYXQsrxqtX48TImFtPcIHSpQ=="
  "resolved" "https://registry.npmjs.org/synckit/-/synckit-0.8.8.tgz"
  "version" "0.8.8"
  dependencies:
    "@pkgr/core" "^0.1.0"
    "tslib" "^2.6.2"

"tapable@^2.1.1", "tapable@^2.2.0":
  "integrity" "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  "version" "2.2.1"

"terser-webpack-plugin@^5.3.11":
  "integrity" "sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw=="
  "resolved" "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz"
  "version" "5.3.14"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    "jest-worker" "^27.4.5"
    "schema-utils" "^4.3.0"
    "serialize-javascript" "^6.0.2"
    "terser" "^5.31.1"

"terser@^5.31.1", "terser@^5.4.0":
  "integrity" "sha512-LBAhFyLho16harJoWMg/nZsQYgTrg5jXOn2nCYjRUcZZEdE3qa2zb8QEDRUGVZBW4rlazf2fxkg8tztybTaqWw=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.39.0.tgz"
  "version" "5.39.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    "acorn" "^8.8.2"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"text-table@^0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"through2@^2.0.0":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"ticky@1.0.1":
  "integrity" "sha512-RX35iq/D+lrsqhcPWIazM9ELkjOe30MSeoBHQHSsRwd1YuhJO5ui1K1/R0r7N3mFvbLBs33idw+eR6j+w6i/DA=="
  "resolved" "https://registry.npmjs.org/ticky/-/ticky-1.0.1.tgz"
  "version" "1.0.1"

"timepicker@1.14.1":
  "integrity" "sha512-sNy0zVRacCNQ2bo4NLAG3+AX9ARxmH5IMqwZva9rzzFMJcfKfEnGzmDG7gMYeN5gbM76bzfZbld5WiWcshAUrw=="
  "resolved" "https://registry.npmjs.org/timepicker/-/timepicker-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "jquery" "^3.5.1"

"tiny-emitter@^2.0.0":
  "integrity" "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q=="
  "resolved" "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.1.0.tgz"
  "version" "2.1.0"

"tiny-glob@0.2.9":
  "integrity" "sha512-g/55ssRPUjShh+xkfx9UPDXqhckHEsHr4Vd9zX55oSdGZc/MD0m3sferOkwWtp98bv+kcVfEHtRJgBVJzelrzg=="
  "resolved" "https://registry.npmjs.org/tiny-glob/-/tiny-glob-0.2.9.tgz"
  "version" "0.2.9"
  dependencies:
    "globalyzer" "0.1.0"
    "globrex" "^0.1.2"

"tiny-inflate@^1.0.0", "tiny-inflate@^1.0.2":
  "integrity" "sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw=="
  "resolved" "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz"
  "version" "1.0.3"

"tinyqueue@^2.0.3":
  "integrity" "sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA=="
  "resolved" "https://registry.npmjs.org/tinyqueue/-/tinyqueue-2.0.3.tgz"
  "version" "2.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog=="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toastr@^2.1.4":
  "integrity" "sha512-LIy77F5n+sz4tefMmFOntcJ6HL0Fv3k1TDnNmFZ0bU/GcvIIfy6eG2v7zQmMiYgaalAiUv75ttFrPn5s0gyqlA=="
  "resolved" "https://registry.npmjs.org/toastr/-/toastr-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "jquery" ">=1.12.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"touch@^3.1.0":
  "integrity" "sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA=="
  "resolved" "https://registry.npmjs.org/touch/-/touch-3.1.1.tgz"
  "version" "3.1.1"

"tsconfig-paths@^3.15.0":
  "integrity" "sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg=="
  "resolved" "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  "version" "3.15.0"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.2"
    "minimist" "^1.2.6"
    "strip-bom" "^3.0.0"

"tslib@^2.6.2":
  "integrity" "sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.6.3.tgz"
  "version" "2.6.3"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"typeahead.js@0.11.1":
  "integrity" "sha512-yGaLzGjVHyryZdNfrWz2NHXUwEO7hrlVmGMGCo5+6mH3nEEhcQ0Te3mK3G60uRnxfERu8twOWSU4WmwScbwhMg=="
  "resolved" "https://registry.npmjs.org/typeahead.js/-/typeahead.js-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "jquery" ">=1.7"

"typed-array-buffer@^1.0.2":
  "integrity" "sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ=="
  "resolved" "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "is-typed-array" "^1.1.13"

"typed-array-byte-length@^1.0.1":
  "integrity" "sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw=="
  "resolved" "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"

"typed-array-byte-offset@^1.0.2":
  "integrity" "sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA=="
  "resolved" "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"

"typed-array-length@^1.0.6":
  "integrity" "sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g=="
  "resolved" "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"
    "possible-typed-array-names" "^1.0.0"

"ua-parser-js@^1.0.33":
  "integrity" "sha512-Aq5ppTOfvrCMgAPneW1HfWj66Xi7XL+/mIy996R1/CLS/rcyJQm6QZdsKrUeivDFQ+Oc9Wyuwor8Ze8peEoUoQ=="
  "resolved" "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.38.tgz"
  "version" "1.0.38"

"unbox-primitive@^1.0.2":
  "integrity" "sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw=="
  "resolved" "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-bigints" "^1.0.2"
    "has-symbols" "^1.0.3"
    "which-boxed-primitive" "^1.0.2"

"undefsafe@^2.0.5":
  "integrity" "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA=="
  "resolved" "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz"
  "version" "2.0.5"

"undici-types@~5.26.4":
  "integrity" "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="
  "resolved" "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
  "version" "5.26.5"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.1.0":
  "integrity" "sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unicode-properties@^1.2.2":
  "integrity" "sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg=="
  "resolved" "https://registry.npmjs.org/unicode-properties/-/unicode-properties-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "base64-js" "^1.3.0"
    "unicode-trie" "^2.0.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unicode-trie@^2.0.0":
  "integrity" "sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ=="
  "resolved" "https://registry.npmjs.org/unicode-trie/-/unicode-trie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pako" "^0.2.5"
    "tiny-inflate" "^1.0.0"

"universalify@^0.1.0":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"update-browserslist-db@^1.1.3":
  "integrity" "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2", "uri-js@^4.4.1":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"url-polyfill@^1.1.12":
  "integrity" "sha512-mYFmBHCapZjtcNHW0MDq9967t+z4Dmg5CJ0KqysK3+ZbyoNOWQHksGCTWwDhxGXllkWlOc10Xfko6v4a3ucM6A=="
  "resolved" "https://registry.npmjs.org/url-polyfill/-/url-polyfill-1.1.12.tgz"
  "version" "1.1.12"

"util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utils-merge@1.0.1":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"vary@^1":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vite-plugin-full-reload@^1.1.0":
  "integrity" "sha512-3cObNDzX6DdfhD9E7kf6w2mNunFpD7drxyNgHLw+XwIYAgb+Xt16SEXo0Up4VH+TMf3n+DSVJZtW2POBGcBYAA=="
  "resolved" "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "picocolors" "^1.0.0"
    "picomatch" "^2.3.1"

"vite@^5.0.0", "vite@5.2.12":
  "integrity" "sha512-/gC8GxzxMK5ntBwb48pR32GGhENnjtY30G4A0jemunsBkiEZFw60s8InGpN8gkhHEkjnRK1aSAxeQgwvFhUHAA=="
  "resolved" "https://registry.npmjs.org/vite/-/vite-5.2.12.tgz"
  "version" "5.2.12"
  dependencies:
    "esbuild" "^0.20.1"
    "postcss" "^8.4.38"
    "rollup" "^4.13.0"
  optionalDependencies:
    "fsevents" "~2.3.3"

"vt-pbf@^3.1.3":
  "integrity" "sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA=="
  "resolved" "https://registry.npmjs.org/vt-pbf/-/vt-pbf-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "@mapbox/point-geometry" "0.1.0"
    "@mapbox/vector-tile" "^1.3.1"
    "pbf" "^3.2.1"

"watchpack@^2.4.1":
  "integrity" "sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack@^5.0.0", "webpack@^5.1.0", "webpack@>=5":
  "integrity" "sha512-CNqKBRMQjwcmKR0idID5va1qlhrqVUKpovi+Ec79ksW8ux7iS1+A6VqzfZXgVYCFRKl7XL5ap3ZoMpwBJxcg0w=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-5.99.7.tgz"
  "version" "5.99.7"
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    "acorn" "^8.14.0"
    "browserslist" "^4.24.0"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.17.1"
    "es-module-lexer" "^1.2.1"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.11"
    "json-parse-even-better-errors" "^2.3.1"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^4.3.2"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.3.11"
    "watchpack" "^2.4.1"
    "webpack-sources" "^3.2.3"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-typed-array@^1.1.14", "which-typed-array@^1.1.15":
  "integrity" "sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA=="
  "resolved" "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.15.tgz"
  "version" "1.1.15"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-tostringtag" "^1.0.2"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.5":
  "integrity" "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  "version" "1.2.5"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^8.1.0":
  "integrity" "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "ansi-styles" "^6.1.0"
    "string-width" "^5.0.1"
    "strip-ansi" "^7.0.1"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@~8.11.0":
  "integrity" "sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.11.0.tgz"
  "version" "8.11.0"

"xmldoc@^1.1.2":
  "integrity" "sha512-y7IRWW6PvEnYQZNZFMRLNJw+p3pezM4nKYPfr15g4OOW9i8VpeydycFuipE2297OvZnh3jSb2pxOt9QpkZUVng=="
  "resolved" "https://registry.npmjs.org/xmldoc/-/xmldoc-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "sax" "^1.2.4"

"xmlhttprequest-ssl@~2.0.0":
  "integrity" "sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A=="
  "resolved" "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.0.0.tgz"
  "version" "2.0.0"

"xtend@~4.0.1":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs-parser@^21.1.1":
  "integrity" "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  "version" "21.1.1"

"yargs@^17.3.1":
  "integrity" "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  "version" "17.7.2"
  dependencies:
    "cliui" "^8.0.1"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.3"
    "y18n" "^5.0.5"
    "yargs-parser" "^21.1.1"

"yargs@17.1.1":
  "integrity" "sha512-c2k48R0PwKIqKhPMWjeiF6y2xY/gPMUlro0sgxqXpbOIohWiLNXWslsootttv7E1e73QPAMQSg5FeySbVcpsPQ=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-17.1.1.tgz"
  "version" "17.1.1"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"yocto-queue@^1.0.0":
  "integrity" "sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.0.0.tgz"
  "version" "1.0.0"
