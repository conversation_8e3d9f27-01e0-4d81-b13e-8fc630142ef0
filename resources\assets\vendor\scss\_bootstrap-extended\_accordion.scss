// Accordions
// *******************************************************************************

// arrow left

.accordion-arrow-left {
  .accordion-button.collapsed:focus {
    box-shadow: none;
  }
  .accordion-item {
    border: 0;
  }
  .accordion-button {
    padding: var(--#{$prefix}accordion-btn-padding-y) 0;
    // Accordion icon
    &::after {
      content: '';
      display: none;
    }
    &:not(.collapsed) {
      color: var(--#{$prefix}accordion-active-color);
      background-color: var(--#{$prefix}accordion-active-bg);
      box-shadow: none; // stylelint-disable-line function-disallowed-list

      &::before {
        background-image: var(--#{$prefix}accordion-btn-active-icon);
        transform: var(--#{$prefix}accordion-btn-icon-transform);
      }
      &::after {
        background-image: none;
        transform: none;
      }
    }
    &.collapsed::before {
      transform: rotate(-90deg);
    }
    &::before {
      flex-shrink: 0;
      width: var(--#{$prefix}accordion-btn-icon-width);
      height: var(--#{$prefix}accordion-btn-icon-width);
      margin-left: 0;
      margin-top: 0.75rem;
      margin-right: 0.9rem;
      content: '';
      background-image: var(--#{$prefix}accordion-btn-icon);
      background-repeat: no-repeat;
      background-size: var(--#{$prefix}accordion-btn-icon-width);
      @include transition(var(--#{$prefix}accordion-btn-icon-transition));
    }
  }
}

// Solid variant icon color
.accordion[class*='accordion-solid-'] {
  .accordion-button::after {
    background-image: str-replace(str-replace($accordion-button-icon, '#{$accordion-icon-color}', $white), '#', '%23');
  }
}

// Solid Accordion With Active Border
.accordion[class*='accordion-border-solid-'] {
  .accordion-button.collapsed::after {
    background-image: str-replace(str-replace($accordion-button-icon, '#{$accordion-icon-color}', $white), '#', '%23');
  }
}

.accordion-header + .accordion-collapse .accordion-body {
  padding-top: 0;
  padding-bottom: 1.25rem;
}

// accordion without icon
.accordion {
  &.accordion-without-arrow {
    .accordion-button::after {
      background-image: none !important;
    }
  }
}

.accordion-header {
  line-height: $line-height-base;
}

.accordion:not(.accordion-custom-button):not(.accordion-arrow-left) .accordion-item {
  box-shadow: $box-shadow-xs;
  border: 0;
  &:not(:first-child) {
    margin-top: $spacer * 0.5;
  }
  &:last-child {
    margin-bottom: $spacer * 0.5;
  }
  &.active {
    box-shadow: $box-shadow;
    & .accordion-button:not(.collapsed) {
      box-shadow: none;
    }
  }
}

// Accordion border radius
.accordion-button {
  font-weight: inherit;
  align-items: unset;
  @include border-top-radius($accordion-border-radius);
  &.collapsed {
    @include border-radius($accordion-border-radius);
  }
  &.collapsed {
    &::after {
      transform: rotate(-90deg);
    }
  }
}
// added box shadow
.accordion {
  &:not(.accordion-bordered) > .card.accordion-item {
    box-shadow: $box-shadow-xs;
    &.active {
      box-shadow: $card-box-shadow;
    }
  }
}
.accordion-header + .accordion-collapse .accordion-body {
  padding-top: 0;
}

// Accordion custom button

.accordion-custom-button {
  .accordion-item {
    transition: $accordion-transition;
    transition-property: margin-top, margin-bottom, border-radius, border;
    box-shadow: none;
    border: $accordion-border-width solid $accordion-border-color;
    &:not(:last-child) {
      border-bottom: 0;
    }
    &:not(.active):not(:first-child) {
      .accordion-header {
        border: none;
      }
    }
    .accordion-button {
      border-color: $accordion-border-color;
    }
    &.active {
      margin: 0;
      box-shadow: none;
      .accordion-header .accordion-button:focus {
        border-bottom: $accordion-border-width solid $accordion-border-color;
      }
      & + .accordion-item {
        @include border-top-radius(0);
      }
      &:not(:first-child) {
        @include border-top-radius(0);
      }
      &:not(:last-child) {
        @include border-bottom-radius(0);
      }
    }
    .accordion-body {
      padding-top: $accordion-body-padding-x;
    }
    &.previous-active {
      @include border-bottom-radius(0);
    }
  }

  .accordion-button {
    border-radius: 0;
    background-color: #fafafa;
    &:not(.collapsed) {
      &::after {
        background-image: escape-svg($accordion-custom-button-active-icon);
        transform: rotate(-180deg);
      }
    }
    // Accordion icon
    &::after {
      background-image: escape-svg($accordion-custom-button-icon);
    }
  }

  &:focus {
    z-index: 3;
    border-color: $border-color;
    outline: 0;
    box-shadow: var(--#{$prefix}accordion-btn-focus-box-shadow);
  }
}

// RTL
// *******************************************************************************

@include rtl-only {
  .accordion-arrow-left {
    .accordion-button {
      &::before {
        margin-left: 1.1rem;
        margin-right: 0;
        transform: rotate(90deg);
      }
      &:not(.collapsed)::before {
        transform: rotate(0deg);
      }
      // !- For RTL accordion icon rotation in other templates
      // &:not(.collapsed)::before {
      // transform: rotate(90deg);
      // }
    }
  }

  .accordion-button {
    text-align: right;
    &::after {
      margin-left: 0;
      margin-right: auto;
    }
    &.collapsed {
      &::after {
        transform: rotate(90deg);
      }
    }
  }
  .accordion-custom-button {
    .accordion-button:not(.collapsed)::after {
      transform: rotate(180deg);
    }
  }
}

//Dark style
// *******************************************************************************

@include dark-layout-only {
  .accordion-custom-button {
    .accordion-button {
      background-color: #353a52;
    }
  }
  .accordion:not(.accordion-custom-button):not(.accordion-arrow-left) .accordion-item {
    box-shadow: $box-shadow-xs;
    &.active {
      box-shadow: $box-shadow;
    }
  }
}
