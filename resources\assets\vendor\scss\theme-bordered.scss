@import './_components/include';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';
@import './_theme/_theme';

$primary-color: #7367f0;
$body-bg: #f8f7fa;

body {
  background: $card-bg;
}

.bg-body {
  background: $body-bg !important;
}

.dropdown-menu,
.popover,
.toast,
.flatpickr-calendar:not(.app-calendar-sidebar .flatpickr-calendar),
.datepicker.datepicker-inline,
.datepicker.datepicker-inline table,
.daterangepicker,
.pcr-app,
.ui-timepicker-wrapper,
.twitter-typeahead .tt-menu,
.tagify__dropdown,
.swal2-popup,
.select2-dropdown,
.select2-container--default .select2-dropdown.select2-dropdown--above,
.shepherd-element,
.menu-horizontal .menu-inner > .menu-item.open .menu-sub,
div.dataTables_wrapper .dt-button-collection {
  outline: none;
  box-shadow: none !important;
  border: 1px solid $border-color !important;
}

.flatpickr-days,
.flatpickr-time {
  border-width: 0 !important;
}

// Bootstrap select > double border fix
.dropdown-menu .dropdown-menu {
  border: none !important;
}
.datepicker.datepicker-inline {
  width: fit-content;
  border-radius: $border-radius;
}
.apexcharts-canvas .apexcharts-tooltip,
.modal-content,
.offcanvas,
div.dataTables_wrapper .dt-button-collection > div[role='menu'] {
  box-shadow: none !important;
}
.modal-content {
  & {
    border: $border-width solid $border-color !important;
  }
}
.offcanvas.offcanvas-start,
.offcanvas.offcanvas-end,
.offcanvas.offcanvas-top,
.offcanvas.offcanvas-bottom {
  border-width: $border-width;
}
.select2-dropdown {
  border-color: $border-color !important;
}
.bs-popover-start > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^='left'] > .popover-arrow::before {
  border-left-color: $border-color !important;
  right: -1px;
}
.bs-popover-end > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^='right'] > .popover-arrow::before {
  border-right-color: $border-color !important;
  left: -1px;
}
.bs-popover-top > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^='top'] > .popover-arrow::before {
  border-top-color: $border-color !important;
  bottom: -1px;
}
.bs-popover-bottom > .popover-arrow::before,
.bs-popover-auto[data-popper-placement^='bottom'] > .popover-arrow::before {
  border-bottom-color: $border-color !important;
  top: -1px;
}

@include template-common-theme($primary-color);
@include template-libs-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------

@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $headings-color, $active-color: $headings-color);
.layout-navbar,
.menu-horizontal {
  backdrop-filter: saturate(200%) blur(6px);
}
.navbar-detached {
  border: 1px solid $border-color;
  box-shadow: none;
}
.layout-navbar-fixed .layout-page:before {
  backdrop-filter: saturate(200%) blur(10px);
  background: linear-gradient(180deg, rgba($card-bg, 70%) 44%, rgba($card-bg, 43%) 73%, rgba($card-bg, 0%));
  -webkit-mask: linear-gradient($card-bg, $card-bg 18%, transparent 100%);
  mask: linear-gradient($card-bg, $card-bg 18%, transparent 100%);
}
.layout-horizontal {
  .layout-navbar {
    box-shadow: 0 1px 0 $border-color;
  }
  .layout-page .menu-horizontal {
    box-shadow: none;
    border-bottom: 1px solid $border-color;
  }
}

// Menu
// ---------------------------------------------------------------------------

@include template-menu-style(
  '.bg-menu-theme',
  $card-bg,
  $color: $headings-color,
  $active-color: $headings-color,
  $active-bg: $primary-color
);

.bg-menu-theme {
  .menu-inner {
    .menu-item {
      &.open,
      &.active {
        > .menu-link.menu-toggle {
          &,
          .layout-menu-hover.layout-menu-collapsed & {
            background: $gray-75;
          }
        }
      }
      &:not(.active) > .menu-link:hover {
        html:not(.layout-menu-collapsed) &,
        .layout-menu-hover.layout-menu-collapsed & {
          background: $gray-50;
        }
      }
    }
  }
  .menu-inner .menu-sub .menu-item:not(.active) > .menu-link::before {
    color: $body-color !important;
  }
}

@include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
  .layout-menu {
    box-shadow: 0 0 0 1px $border-color;
  }
}

.layout-menu-horizontal {
  box-shadow: 0 -1px 0 $border-color inset;
}

// timeline
.timeline {
  .timeline-item {
    .timeline-indicator,
    .timeline-indicator-advanced {
      box-shadow: 0 0 0 10px $card-bg;
    }
  }
}

// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $card-bg, $color: $primary-color, $active-color: $primary-color);

.content-footer .footer-container {
  .layout-footer-fixed .layout-wrapper:not(.layout-horizontal) & {
    border: 1px solid $border-color;
    border-bottom: 0;
  }
}
.layout-horizontal .bg-footer-theme {
  border-top: 1px solid $border-color;
}

// Component styles
// ---------------------------------------------------------------------------

// card
.card:not(.card-group .card),
.card-group {
  box-shadow: none;
  border: $border-width solid $card-border-color;
}
// card border-shadow variant
.card {
  &[class*='card-border-shadow-'] {
    &:hover {
      box-shadow: none !important;
    }
  }
}

//Accordion
.accordion {
  &:not(.accordion-custom-button):not(.accordion-arrow-left) {
    .accordion-item {
      border: $accordion-border-width solid $accordion-border-color;
    }
  }
  .accordion-item {
    box-shadow: none !important;
  }
}

// Tabs
.nav-tabs-shadow {
  box-shadow: none !important;
  border: 1px solid $border-color !important;
  border-radius: $border-radius;
}
.nav-pills:not(.card-header-pills) {
  ~ .tab-content {
    border: 1px solid $border-color !important;
    @include border-radius($border-radius);
    box-shadow: none;
  }
}
.nav-align-top .nav-tabs {
  @include border-top-radius($border-radius);
  ~ .tab-content {
    box-shadow: none;
    border-top-width: 0 !important;
    @include border-bottom-radius($border-radius);
  }
}
.nav-align-bottom .nav-tabs {
  @include border-bottom-radius($border-radius);
  ~ .tab-content {
    box-shadow: none;
    border-bottom-width: 0 !important;
    @include border-top-radius($border-radius);
  }
}
.nav-align-left .nav-tabs {
  @include ltr-style {
    @include border-start-radius($border-radius);
  }
  @include rtl-style {
    @include border-end-radius($border-radius);
  }
  ~ .tab-content {
    box-shadow: none;
    @include ltr-style {
      border-left-width: 0 !important;
      @include border-end-radius($border-radius);
    }
    @include rtl-style {
      border-right-width: 0 !important;
      @include border-start-radius($border-radius);
    }
  }
}
.nav-align-right .nav-tabs {
  @include ltr-style {
    @include border-end-radius($border-radius);
  }
  @include rtl-style {
    @include border-start-radius($border-radius);
  }
  ~ .tab-content {
    box-shadow: none;
    @include ltr-style {
      border-right-width: 0 !important;
      @include border-start-radius($border-radius);
    }
    @include rtl-style {
      border-left-width: 0 !important;
      @include border-end-radius($border-radius);
    }
  }
}

//Kanban-item
.kanban-item {
  box-shadow: none !important;
  border: $border-width solid $card-border-color;
}
// default form wizard style
.bs-stepper:not(.wizard-modern) {
  box-shadow: none !important;
  border: 1px solid $border-color;
  border-radius: $card-border-radius;
  .modal .modal-body & {
    border-width: 0;
  }
}

// modern form wizard style

.bs-stepper.wizard-modern {
  .bs-stepper-content {
    box-shadow: none !important;
    border: 1px solid $border-color;
    border-radius: $card-border-radius;
  }
}
// file upload (dropzone)
.light-style .dz-preview {
  box-shadow: none;
  border: 1px solid $border-color;
}

// App email rear card border effect

.app-email {
  .app-email-view {
    .email-card-last {
      &:before {
        border: 1px solid $border-color;
      }

      &:after {
        border: 1px solid $border-color;
      }
    }
  }
}

// authentication
.authentication-wrapper .authentication-bg {
  border-inline-start: 1px solid $border-color;
}
