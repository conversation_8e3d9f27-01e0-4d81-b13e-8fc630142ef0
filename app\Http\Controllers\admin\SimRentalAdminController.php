<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use App\Models\SmsServer;
use App\Models\SmsCountry;
use App\Models\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SimRentalAdminController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index()
    {
        $pageConfigs = [
            'pageHeader' => false,
            'contentLayout' => 'wide',
            'footerFixed' => false
        ];

        $servers = SmsServer::withCount(['countries', 'simRentals'])->orderBy('sort_order')->get();
        $countries = SmsCountry::with('server')->withCount('services')->orderBy('sort_order')->get();
        $services = SmsService::with(['country.server'])->orderBy('sort_order')->get();

        $stats = [
            'total_servers' => SmsServer::count(),
            'total_countries' => SmsCountry::count(),
            'total_services' => SmsService::count(),
            'active_servers' => SmsServer::where('is_active', true)->count(),
        ];

        return view('content.admin.sim-rental.index', compact('pageConfigs', 'servers', 'countries', 'services', 'stats'));
    }

    /**
     * Server Management
     */
    public function storeServer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $server = SmsServer::create([
            'name' => $request->name,
            'description' => $request->description,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Server created successfully',
            'data' => $server
        ]);
    }

    public function updateServer(Request $request, $id)
    {
        $server = SmsServer::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $server->update([
            'name' => $request->name,
            'description' => $request->description,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Server updated successfully',
            'data' => $server
        ]);
    }

    public function deleteServer($id)
    {
        $server = SmsServer::findOrFail($id);
        
        // Check if server has countries
        if ($server->countries()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete server with existing countries'
            ], 400);
        }

        $server->delete();

        return response()->json([
            'success' => true,
            'message' => 'Server deleted successfully'
        ]);
    }

    /**
     * Country Management
     */
    public function storeCountry(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|size:2|alpha',
            'server_id' => 'required|exists:sms_servers,id',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check unique constraint
        $exists = SmsCountry::where('server_id', $request->server_id)
                           ->where('code', strtoupper($request->code))
                           ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Country code already exists for this server'
            ], 422);
        }

        $country = SmsCountry::create([
            'name' => $request->name,
            'code' => strtoupper($request->code),
            'server_id' => $request->server_id,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        $country->load('server');

        return response()->json([
            'success' => true,
            'message' => 'Country created successfully',
            'data' => $country
        ]);
    }

    public function updateCountry(Request $request, $id)
    {
        $country = SmsCountry::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|size:2|alpha',
            'server_id' => 'required|exists:sms_servers,id',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check unique constraint (excluding current record)
        $exists = SmsCountry::where('server_id', $request->server_id)
                           ->where('code', strtoupper($request->code))
                           ->where('id', '!=', $id)
                           ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Country code already exists for this server'
            ], 422);
        }

        $country->update([
            'name' => $request->name,
            'code' => strtoupper($request->code),
            'server_id' => $request->server_id,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        $country->load('server');

        return response()->json([
            'success' => true,
            'message' => 'Country updated successfully',
            'data' => $country
        ]);
    }

    public function deleteCountry($id)
    {
        $country = SmsCountry::findOrFail($id);
        
        // Check if country has services
        if ($country->services()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete country with existing services'
            ], 400);
        }

        $country->delete();

        return response()->json([
            'success' => true,
            'message' => 'Country deleted successfully'
        ]);
    }

    /**
     * Service Management
     */
    public function storeService(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50',
            'country_id' => 'required|exists:sms_countries,id',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check unique constraint
        $exists = SmsService::where('country_id', $request->country_id)
                           ->where('code', $request->code)
                           ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Service code already exists for this country'
            ], 422);
        }

        $service = SmsService::create([
            'name' => $request->name,
            'code' => $request->code,
            'country_id' => $request->country_id,
            'price' => $request->price,
            'stock' => $request->stock,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        $service->load('country.server');

        return response()->json([
            'success' => true,
            'message' => 'Service created successfully',
            'data' => $service
        ]);
    }

    public function updateService(Request $request, $id)
    {
        $service = SmsService::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50',
            'country_id' => 'required|exists:sms_countries,id',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check unique constraint (excluding current record)
        $exists = SmsService::where('country_id', $request->country_id)
                           ->where('code', $request->code)
                           ->where('id', '!=', $id)
                           ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Service code already exists for this country'
            ], 422);
        }

        $service->update([
            'name' => $request->name,
            'code' => $request->code,
            'country_id' => $request->country_id,
            'price' => $request->price,
            'stock' => $request->stock,
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true)
        ]);

        $service->load('country.server');

        return response()->json([
            'success' => true,
            'message' => 'Service updated successfully',
            'data' => $service
        ]);
    }

    public function deleteService($id)
    {
        $service = SmsService::findOrFail($id);
        
        // Check if service has active rentals
        if ($service->simRentals()->whereIn('status', ['active', 'waiting_otp'])->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete service with active rentals'
            ], 400);
        }

        $service->delete();

        return response()->json([
            'success' => true,
            'message' => 'Service deleted successfully'
        ]);
    }
}
