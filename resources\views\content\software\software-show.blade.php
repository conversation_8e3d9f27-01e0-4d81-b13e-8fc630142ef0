@extends('layouts/layoutMaster')

@section('title', $software->name)

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/cleavejs/cleave.js', 'resources/assets/vendor/libs/cleavejs/cleave-phone.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/@form-validation/popular.js', 'resources/assets/vendor/libs/@form-validation/bootstrap5.js', 'resources/assets/vendor/libs/@form-validation/auto-focus.js'])
@endsection

@section('page-script')
@endsection

@section('content')
    <div
        class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-6 row-gap-4">

        <div class="d-flex flex-column justify-content-center">
            <div class="mb-1">
                <h1 class="h3">{{ $software->name }}</h1>
            </div>
            <p class="mb-0">{{ $software->updated_at->translatedFormat('d F Y, H:i') }}</p>
        </div>
        {{-- <div class="d-flex align-content-center flex-wrap gap-2">
            <button class="btn btn-label-danger delete-order">Delete Order</button>
        </div> --}}
    </div>

    <!-- Order Details Table -->

    <div class="row">
        <div class="col-12 col-lg-8">
            <div class="card mb-6">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title m-0">{{ __('messages.video_demo') }}</h5>
                </div>
                <div class="col-12 p-3 pt-0 mt-0">
                    <div class="ratio ratio-16x9">
                        {!! $software->video !!}
                    </div>
                </div>
            </div>
            <div class="card mb-6">
                <div class="card-header">
                    <h5 class="card-title m-0">{{ __('messages.software_detail') }}</h5>
                </div>
                <div class="card-body pt-1">
                    <span>
                        {!! nl2br(e($software->description)) !!}
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-4">
            <div class="card mb-6">
                <div class="card-header">
                    <h5 class="card-title m-0">{{ __('messages.price_list') }}</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>{{ __('messages.option') }}</th>
                                <th>{{ __('messages.price') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ __('messages.1month') }}</td>
                                <td>{{ number_format($software->price_1_m) }}đ</td>
                            </tr>
                            <tr>
                                <td>{{ __('messages.3month') }}</td>
                                <td>{{ number_format($software->price_3_m) }}đ</td>
                            </tr>
                            <tr>
                                <td>{{ __('messages.6month') }}</td>
                                <td>{{ number_format($software->price_6_m) }}đ</td>
                            </tr>
                            <tr>
                                <td>{{ __('messages.1year') }}</td>
                                <td>{{ number_format($software->price_1_y) }}đ</td>
                            </tr>
                            <tr>
                                <td>{{ __('messages.lifetime') }}</td>
                                <td>{{ number_format($software->price_lifetime) }}đ</td>
                            </tr>
                            <tr>
                                <td>{{ __('messages.pricecode') }}</td>
                                <td>{{ number_format($software->price_code) }}đ</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-6">

                <div class="card-header d-flex justify-content-between">
                    <h5 class="card-title m-0">Tag</h5>
                </div>
                <div class="card-body">
                    <div class="demo-inline-spacing pb-0 m-0">
                        @foreach ($software->tags as $tag)
                            <span class="badge rounded-pill m-0 mb-1"
                                style="background-color: #666;">{{ $tag->name }}</span>
                        @endforeach
                    </div>
                </div>

            </div>
            {{-- <div class="card mb-6">
                <div class="card-header d-flex justify-content-between">
                    <h5 class="card-title m-0">Billing address</h5>
                    <h6 class="m-0"><a href=" javascript:void(0)" data-bs-toggle="modal"
                            data-bs-target="#addNewAddress">Edit</a></h6>
                </div>
                <div class="card-body">
                    <p class="mb-6">45 Roker Terrace <br>Latheronwheel <br>KW5 8NW,London <br>UK</p>
                    <h5 class="mb-1">Mastercard</h5>
                    <p class="mb-0">Card Number: ******4291</p>
                </div>

            </div> --}}
        </div>
    </div>

    <!-- Modals -->
    @include('_partials/_modals/modal-edit-user')
    @include('_partials/_modals/modal-add-new-address')
@endsection
