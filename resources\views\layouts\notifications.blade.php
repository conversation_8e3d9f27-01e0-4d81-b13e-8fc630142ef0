@vite(['resources/assets/js/notify.js'])
<style>
    .toast-error {
    background-color: #BD362F !important;
    color: #fff !important;
}
.toast-success {
    background-color: #51A351 !important;
    color: #fff !important;
}
.toast-warning {
    background-color: #F89406 !important;
    color: #fff !important;
}
.toast-info {
    background-color: #2F96B4 !important;
    color: #fff !important;
}
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        @if (session('success'))
            window.notifySuccess("{{ session('success') }}");
        @elseif (session('error'))
            window.notifyError("{{ session('error') }}");
        @elseif (session('warning'))
            window.notifyWarning("{{ session('warning') }}");
        @endif
    });
</script>
