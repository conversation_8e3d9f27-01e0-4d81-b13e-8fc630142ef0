@import './_components/include';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';
@import './_theme/_theme';

$primary-color: #7367f0;
$body-bg: #f8f7fa;

body {
  background: $body-bg;
}

.bg-body {
  background: $body-bg !important;
}

@include template-common-theme($primary-color);
@include template-libs-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $headings-color, $active-color: $headings-color);

.layout-navbar {
  box-shadow: 0 1px 0 $border-color;
  backdrop-filter: saturate(200%) blur(6px);
}
.menu-horizontal {
  backdrop-filter: saturate(200%) blur(6px);
}
.navbar-detached {
  box-shadow: 0 0.125rem 0.5rem 0 rgba($black, 0.12);
}
.layout-navbar-fixed .layout-page:before {
  backdrop-filter: saturate(200%) blur(10px);
  background: linear-gradient(180deg, rgba($body-bg, 70%) 44%, rgba($body-bg, 43%) 73%, rgba($body-bg, 0%));
  -webkit-mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
  mask: linear-gradient($body-bg, $body-bg 18%, transparent 100%);
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  #2f3349,
  $color: #cfcce4,
  $active-color: $white,
  $active-bg: $primary-color
);
@include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
  .bg-menu-theme {
    box-shadow: 0px 2px 8px 0px rgba(#131120, 0.12);
  }
}
.bg-menu-theme {
  .menu-inner {
    .menu-item {
      &.open,
      &.active {
        > .menu-link.menu-toggle {
          &,
          .layout-menu-hover.layout-menu-collapsed & {
            background: rgba(#e1def5, 0.08);
          }
        }
      }
      &:not(.active) > .menu-link:hover {
        html:not(.layout-menu-collapsed) &,
        .layout-menu-hover.layout-menu-collapsed & {
          background: rgba(#e1def5, 0.06);
        }
      }
    }
  }
  .menu-inner .menu-sub .menu-item:not(.active) > .menu-link::before {
    color: rgba(#e1def5, 0.7) !important;
  }
}

// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $card-bg, $color: $primary-color, $active-color: $primary-color);

.layout-footer-fixed .layout-wrapper:not(.layout-horizontal) .content-footer .footer-container,
.layout-footer-fixed .layout-wrapper.layout-horizontal .content-footer {
  box-shadow: $box-shadow;
}
