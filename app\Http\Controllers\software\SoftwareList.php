<?php

namespace App\Http\Controllers\software;

use App\Http\Controllers\Controller;
use App\Models\Software;
use Illuminate\Http\Request;

class SoftwareList extends Controller
{
    public function index(Request $request)
    {
        // L<PERSON>y thống kê
        $totalSoftware = Software::count();
        $freeSoftware = Software::where('is_free', true)->count();
        $paidSoftware = Software::where('is_free', false)->count();
        $activeSoftware = Software::where('status', 'active')->count();

        // Lấy danh sách software với phân trang
        $query = Software::query();

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Lọc theo loại (free/paid)
        if ($request->filled('type')) {
            if ($request->type === 'free') {
                $query->where('is_free', true);
            } elseif ($request->type === 'paid') {
                $query->where('is_free', false);
            }
        }

        // T<PERSON>m kiếm theo tên
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('en_name', 'like', '%' . $request->search . '%');
            });
        }

        $softwares = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('content.software.software-list', compact(
            'softwares',
            'totalSoftware',
            'freeSoftware',
            'paidSoftware',
            'activeSoftware'
        ));
    }

    public function destroy($id)
    {
        try {
            $software = Software::findOrFail($id);

            // Xóa file ảnh nếu tồn tại
            if ($software->image && file_exists(public_path($software->image))) {
                unlink(public_path($software->image));
            }

            $software->delete();

            return response()->json([
                'success' => true,
                'message' => 'Xóa software thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa software!'
            ], 500);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        try {
            $software = Software::findOrFail($id);
            $software->status = $request->status;
            $software->save();

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật trạng thái thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái!'
            ], 500);
        }
    }
}
