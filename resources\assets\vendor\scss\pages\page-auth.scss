// * Authentication
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

$authentication-1-inner-max-width: 460px !default;

.authentication-wrapper {
  display: flex;
  flex-basis: 100%;
  min-height: 100vh;
  width: 100%;

  .authentication-inner {
    width: 100%;
  }

  &.authentication-basic {
    align-items: center;
    justify-content: center;
    .card-body {
      padding: 3rem;
      @include light.media-breakpoint-down(sm) {
        padding: 2rem;
      }
    }
  }

  &.authentication-cover {
    align-items: flex-start;
    .authentication-inner {
      height: 100%;
      margin: auto 0;

      @include light.media-breakpoint-down(lg) {
        height: 100vh;
      }
      // authentication cover background styles
      .auth-cover-bg {
        width: 100%;
        height: 100vh;
        position: relative;

        // authentication cover illustration height
        .auth-illustration {
          max-height: 65%;
          max-width: 65%;
          z-index: 1;
        }
      }

      // authentication cover platform bg styles
      .platform-bg {
        position: absolute;
        width: 100%;
        bottom: 0%;
        left: 0%;
        height: 35%;
      }

      // authentication multisteps styles
      .auth-multisteps-bg-height {
        height: 100vh;

        // z-index for illustration
        & > img:first-child {
          z-index: 1;
        }
      }
    }
  }

  &.authentication-basic .authentication-inner {
    max-width: $authentication-1-inner-max-width;
    position: relative;
    &:before {
      @include light.media-breakpoint-down(sm) {
        display: none;
      }
      width: 238px;
      height: 233px;
      content: ' ';
      position: absolute;
      top: -35px;
      left: -45px;
      background-image: url("data:image/svg+xml,%3Csvg width='239' height='234' viewBox='0 0 239 234' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='88.5605' y='0.700195' width='149' height='149' rx='19.5' stroke='%237367F0' stroke-opacity='0.16'/%3E%3Crect x='0.621094' y='33.761' width='200' height='200' rx='10' fill='%237367F0' fill-opacity='0.08'/%3E%3C/svg%3E%0A");
    }
    &:after {
      @include light.media-breakpoint-down(sm) {
        display: none;
      }
      width: 180px;
      height: 180px;
      content: ' ';
      position: absolute;
      z-index: -1;
      bottom: -30px;
      right: -56px;
      background-image: url("data:image/svg+xml,%3Csvg width='181' height='181' viewBox='0 0 181 181' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1.30469' y='1.44312' width='178' height='178' rx='19' stroke='%237367F0' stroke-opacity='0.16' stroke-width='2' stroke-dasharray='8 8'/%3E%3Crect x='22.8047' y='22.9431' width='135' height='135' rx='10' fill='%237367F0' fill-opacity='0.08'/%3E%3C/svg%3E");
    }
  }

  // For two-steps auth
  .auth-input-wrapper .auth-input {
    max-width: 50px;
    padding-left: 0.4rem;
    padding-right: 0.4rem;
    font-size: light.$large-font-size;
  }
}

// authentication multisteps responsive styles
@media (max-height: 636px) {
  .auth-multisteps-bg-height {
    height: 100% !important;
  }
}

// Two-steps auth responsive style
@include light.media-breakpoint-down(sm) {
  .authentication-wrapper {
    .auth-input-wrapper .auth-input {
      font-size: light.$h5-font-size;
    }
  }
}

// Two Steps Verification
// ? Used for validation specific style as we have validated hidden field
#twoStepsForm {
  .fv-plugins-bootstrap5-row-invalid .form-control {
    border-color: light.$form-feedback-invalid-color;
    border-width: light.$input-focus-border-width;
  }
}
@include light.media-breakpoint-down(sm) {
  .numeral-mask-wrapper .numeral-mask {
    padding: 0 !important;
  }
  .numeral-mask {
    margin-inline: 1px !important;
  }
}

// Light Layout
@if $enable-light-style {
  .light-style {
    .authentication-wrapper .authentication-bg {
      background-color: light.$white;
    }
    .auth-cover-bg-color {
      background-color: light.$body-bg;
    }
  }
}

// Dark Layout
@if $enable-dark-style {
  .dark-style {
    .authentication-wrapper .authentication-bg {
      background-color: dark.$card-bg;
    }
    .auth-cover-bg-color {
      background-color: dark.$body-bg;
    }
  }
}
