@extends('layouts/layoutMaster')

@section('title', 'Chỉnh sửa Software - Admin')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/quill/typography.scss', 'resources/assets/vendor/libs/quill/katex.scss', 'resources/assets/vendor/libs/quill/editor.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/dropzone/dropzone.scss', 'resources/assets/vendor/libs/flatpickr/flatpickr.scss', 'resources/assets/vendor/libs/tagify/tagify.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/quill/katex.js', 'resources/assets/vendor/libs/quill/quill.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/dropzone/dropzone.js', 'resources/assets/vendor/libs/jquery-repeater/jquery-repeater.js', 'resources/assets/vendor/libs/flatpickr/flatpickr.js', 'resources/assets/vendor/libs/tagify/tagify.js'])
@endsection

@section('page-script')
    <script>
        window.suggestions = @json($allTags);
    </script>
    @vite(['resources/assets/js/software/software-add.js'])
@endsection

@section('content')
    <!-- Success Message -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Error Messages -->
    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Có lỗi xảy ra:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="app-ecommerce">
        <form action="{{ route('software-update', $software->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <!-- Edit Product Header -->
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-6 row-gap-4">
                <div class="d-flex flex-column justify-content-center">
                    <h4 class="mb-1">Chỉnh sửa Software: {{ $software->name }}</h4>
                    <p class="mb-0">Cập nhật thông tin software/tool</p>
                </div>
                <div class="d-flex align-content-center flex-wrap gap-4">
                    <div class="d-flex gap-4">
                        <a href="{{ route('software-list') }}" class="btn btn-label-secondary">Hủy</a>
                    </div>
                    <button type="submit" class="btn btn-primary">Cập nhật Software</button>
                </div>
            </div>

            <div class="row">
                <!-- First column-->
                <div class="col-12 col-lg-8">
                    <!-- Product Information -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-tile mb-0">Thông tin Software</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label" for="ecommerce-product-name">Tên Software</label>
                                    <input type="text" class="form-control" id="ecommerce-product-name"
                                           placeholder="Tên software" name="name" aria-label="Product title"
                                           value="{{ old('name', $software->name) }}" required>
                                    @error('name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label" for="ecommerce-product-name-en">Tên tiếng Anh</label>
                                    <input type="text" class="form-control" id="ecommerce-product-name-en"
                                           placeholder="English name" name="en_name" aria-label="Product title english"
                                           value="{{ old('en_name', $software->en_name) }}">
                                    @error('en_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="form-label">Mô tả</label>
                                <div class="form-control p-0 pt-1">
                                    <div class="comment-toolbar border-0 border-bottom">
                                        <div class="d-flex justify-content-start">
                                            <span class="ql-formats me-0">
                                                <button class="ql-bold"></button>
                                                <button class="ql-italic"></button>
                                                <button class="ql-underline"></button>
                                                <button class="ql-list" value="ordered"></button>
                                                <button class="ql-list" value="bullet"></button>
                                                <button class="ql-link"></button>
                                                <button class="ql-image"></button>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="comment-editor border-0 pb-6" id="ecommerce-category-description">
                                        {!! old('description', $software->description) !!}
                                    </div>
                                    <textarea name="description" id="description-textarea" style="display: none;" required>{{ old('description', $software->description) }}</textarea>
                                </div>
                                @error('description')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-4">
                                <label class="form-label" for="ecommerce-product-video">Video URL</label>
                                <input type="text" class="form-control" id="ecommerce-product-video"
                                       placeholder="https://youtube.com/..." name="video"
                                       value="{{ old('video', $software->video) }}">
                                <small class="text-muted">Để trống nếu không có video</small>
                                @error('video')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <!-- /Product Information -->

                    <!-- Media -->
                    <div class="card mb-6">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 card-title">Hình ảnh</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Hình ảnh hiện tại</label>
                                    <div class="mb-3">
                                        <img src="{{ asset($software->image) }}" alt="{{ $software->name }}"
                                             class="img-fluid rounded" style="max-height: 200px;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Thay đổi hình ảnh</label>
                                    <input type="file" class="form-control" name="image" accept="image/*">
                                    <small class="text-muted">Để trống nếu không muốn thay đổi</small>
                                    @error('image')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /Media -->
                </div>
                <!-- /First column -->

                <!-- Second column -->
                <div class="col-12 col-lg-4">
                    <!-- Pricing Card -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Giá cả</h5>
                        </div>
                        <div class="card-body">
                            <!-- Free checkbox -->
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="is_free" name="is_free"
                                       {{ old('is_free', $software->is_free) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_free">
                                    Software miễn phí
                                </label>
                            </div>

                            <div id="pricing-fields" style="{{ old('is_free', $software->is_free) ? 'display: none;' : '' }}">
                                <div class="mb-4">
                                    <label class="form-label" for="price_1_m">Giá 1 tháng (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_1_m" name="price_1_m"
                                           placeholder="0" value="{{ old('price_1_m', $software->price_1_m) }}">
                                </div>
                                <div class="mb-4">
                                    <label class="form-label" for="price_3_m">Giá 3 tháng (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_3_m" name="price_3_m"
                                           placeholder="0" value="{{ old('price_3_m', $software->price_3_m) }}">
                                </div>
                                <div class="mb-4">
                                    <label class="form-label" for="price_6_m">Giá 6 tháng (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_6_m" name="price_6_m"
                                           placeholder="0" value="{{ old('price_6_m', $software->price_6_m) }}">
                                </div>
                                <div class="mb-4">
                                    <label class="form-label" for="price_1_y">Giá 1 năm (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_1_y" name="price_1_y"
                                           placeholder="0" value="{{ old('price_1_y', $software->price_1_y) }}">
                                </div>
                                <div class="mb-4">
                                    <label class="form-label" for="price_lifetime">Giá mua vĩnh viễn (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_lifetime" name="price_lifetime"
                                           placeholder="0" value="{{ old('price_lifetime', $software->price_lifetime) }}">
                                </div>
                                <div class="mb-4">
                                    <label class="form-label" for="price_code">Giá mua code (VNĐ)</label>
                                    <input type="number" class="form-control" id="price_code" name="price_code"
                                           placeholder="0" value="{{ old('price_code', $software->price_code) }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /Pricing Card -->

                    <!-- Organize Card -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Tổ chức</h5>
                        </div>
                        <div class="card-body">
                            <!-- Status -->
                            <div class="mb-4">
                                <label for="status" class="form-label">Trạng thái</label>
                                <select id="status" class="form-select" name="status" required>
                                    <option value="active" {{ old('status', $software->status) === 'active' ? 'selected' : '' }}>Hoạt động</option>
                                    <option value="inactive" {{ old('status', $software->status) === 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                                </select>
                            </div>
                            <!-- Tags -->
                            <div class="mb-4">
                                <label for="tags" class="form-label">Tags</label>
                                @php
                                    $currentTags = $software->tags->pluck('name')->map(function($tag) {
                                        return ['value' => $tag];
                                    })->toArray();
                                    $currentTagsJson = json_encode($currentTags);
                                @endphp
                                <input id="tags" class="form-control" name="tags"
                                       value="{{ old('tags', $currentTagsJson) }}">
                            </div>
                        </div>
                    </div>
                    <!-- /Organize Card -->
                </div>
                <!-- /Second column -->
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle pricing fields based on free checkbox
            const freeCheckbox = document.getElementById('is_free');
            const pricingFields = document.getElementById('pricing-fields');

            if (freeCheckbox && pricingFields) {
                freeCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        pricingFields.style.display = 'none';
                    } else {
                        pricingFields.style.display = 'block';
                    }
                });
            }

            // Handle Quill editor
            let quillInstance = null;
            const textarea = document.querySelector('textarea[name="description"]');

            // Wait for Quill to be initialized
            setTimeout(function() {
                const quillEditor = document.querySelector('.comment-editor');
                if (quillEditor && window.Quill) {
                    // Find the Quill instance
                    quillInstance = quillEditor.__quill;

                    if (quillInstance) {
                        // Update textarea when content changes
                        quillInstance.on('text-change', function() {
                            if (textarea) {
                                textarea.value = quillInstance.root.innerHTML;
                            }
                        });

                        // Set initial content
                        if (textarea && textarea.value) {
                            quillInstance.root.innerHTML = textarea.value;
                        }
                    }
                }
            }, 1000);

            // Before form submit, ensure description is synced
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (quillInstance && textarea) {
                        textarea.value = quillInstance.root.innerHTML;
                    }

                    // Basic validation
                    const name = document.querySelector('input[name="name"]');
                    const description = document.querySelector('textarea[name="description"]');

                    if (!name || !name.value.trim()) {
                        alert('Vui lòng nhập tên software');
                        e.preventDefault();
                        return false;
                    }

                    if (!description || !description.value.trim() || description.value.trim() === '<p><br></p>') {
                        alert('Vui lòng nhập mô tả software');
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    </script>

@endsection
