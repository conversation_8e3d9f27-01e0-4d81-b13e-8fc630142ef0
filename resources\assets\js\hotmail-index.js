import toastr from 'toastr';
import 'toastr/build/toastr.min.css';

// Optional: cấu hình mặc định
toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: 'toast-top-right',
    timeOut: '5000'
};

$('#modalPurchase').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget);
    var name = button.data('name');
    var price = button.data('price');
    var type = button.data('type');
    $('#modalPurchase #name').val(name);
    $('#modalPurchase #price').val(price);
    $('#modalPurchase #type').val(type);
    $('#modalPurchase #quantity').val('');
    $('#modalPurchase #total').text('$0');
    $('#modalPurchase #purchase').prop('disabled', false);
    $('#modalPurchase #loading').addClass('d-none');
});

$('#modalPurchase #quantity').on('input change', function () {
    var quantity = parseInt($(this).val(), 10) || 0;
    var price = parseFloat($('#modalPurchase #price').val()) || 0;
    var total = quantity * price;
    $('#modalPurchase #total').text(total.toLocaleString('en-US') + 'đ');
});

$('#modalPurchase #purchase').on('click', function () {
    var quantity = parseInt($('#modalPurchase #quantity').val(), 10) || 0;
    if (quantity == 0) {
        toastr.warning('Vui lòng nhập số lượng hợp lệ!');
        return;
    }
    var type = $('#modalPurchase #type').val();
    $('#modalPurchase #purchase').prop('disabled', true);
    $('#modalPurchase #loading').removeClass('d-none');
    // Lấy CSRF token từ thẻ meta hoặc input ẩn
    var csrfToken = $('meta[name="csrf-token"]').attr('content') || $('input[name="_token"]').val();

    $.post({
        url: '/hotmail/purchase',
        data: { quantity: quantity, type: type },
        headers: { 'X-CSRF-TOKEN': csrfToken }
    })
        .done(function (response) {
            if (response && response.success === true) {
                toastr.success(response && response.message ? response.message : 'Mua thành công!');
                $('#modalPurchase').modal('hide');
                var data = response.data;
                window.data_download = data;
                if (Array.isArray(data) && data.length > 0) {
                    // Lấy các key từ object đầu tiên
                    var keys = Object.keys(data[0]);
                    // Tạo dòng header
                    var lines = [keys.join('|')];
                    // Tạo các dòng dữ liệu
                    data.forEach(item => {
                        var row = keys.map(k => item[k]).join('|');
                        lines.push(row);
                    });
                    $('#modalDataPurchase').modal('show');
                    $('#dataPurchase').val(lines.join('\n'));
                } else {
                    $('#modalDataPurchase').modal('show');
                    $('#dataPurchase').val('');
                }
            } else {
                toastr.error(response && response.message ? response.message : 'Mua thất bại!');
            }
            $('#modalPurchase #purchase').prop('disabled', false);
            $('#modalPurchase #loading').addClass('d-none');
        })
        .fail(function (xhr) {
            toastr.error(xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Đã xảy ra lỗi!');
            $('#modalPurchase #purchase').prop('disabled', false);
            $('#modalPurchase #loading').addClass('d-none');
        });
});

$('#downloadTxt').on('click', function () {
    var text = $('#dataPurchase').val();
    var blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = 'data.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
$('#downloadJson').on('click', function () {
    var text = JSON.stringify(window.data_download, null, 2);
    var blob = new Blob([text], { type: 'application/json;charset=utf-8' });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = 'data.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
