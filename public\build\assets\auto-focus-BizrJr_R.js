import{l as u}from"./index-D_6hAncj.js";var c={exports:{}},f={},p={exports:{}},h={};/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-field-status
 * @version 2.4.0
 */var g=u,a=function(i,n){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},a(i,n)},v=function(i){function n(t){var e=i.call(this,t)||this;return e.statuses=new Map,e.opts=Object.assign({},{onStatusChanged:function(){}},t),e.elementValidatingHandler=e.onElementValidating.bind(e),e.elementValidatedHandler=e.onElementValidated.bind(e),e.elementNotValidatedHandler=e.onElementNotValidated.bind(e),e.elementIgnoredHandler=e.onElementIgnored.bind(e),e.fieldAddedHandler=e.onFieldAdded.bind(e),e.fieldRemovedHandler=e.onFieldRemoved.bind(e),e}return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}a(t,e),t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}(n,i),n.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},n.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},n.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every(function(t){return t==="Valid"||t==="NotValidated"||t==="Ignored"})},n.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},n.prototype.onFieldAdded=function(t){this.statuses.set(t.field,"NotValidated")},n.prototype.onFieldRemoved=function(t){this.statuses.has(t.field)&&this.statuses.delete(t.field),this.handleStatusChanged(this.areFieldsValid())},n.prototype.onElementValidating=function(t){this.statuses.set(t.field,"Validating"),this.handleStatusChanged(!1)},n.prototype.onElementValidated=function(t){this.statuses.set(t.field,t.valid?"Valid":"Invalid"),t.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},n.prototype.onElementNotValidated=function(t){this.statuses.set(t.field,"NotValidated"),this.handleStatusChanged(!1)},n.prototype.onElementIgnored=function(t){this.statuses.set(t.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},n.prototype.handleStatusChanged=function(t){this.isEnabled&&this.opts.onStatusChanged(t)},n}(g.Plugin);h.FieldStatus=v;p.exports=h;var y=p.exports;/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-auto-focus
 * @version 2.4.0
 */var _=u,b=y,r=function(i,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},r(i,n)},F=function(i){function n(t){var e=i.call(this,t)||this;return e.opts=Object.assign({},{onPrefocus:function(){}},t),e.invalidFormHandler=e.onFormInvalid.bind(e),e}return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}r(t,e),t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}(n,i),n.prototype.install=function(){this.core.on("core.form.invalid",this.invalidFormHandler).registerPlugin(n.FIELD_STATUS_PLUGIN,new b.FieldStatus)},n.prototype.uninstall=function(){this.core.off("core.form.invalid",this.invalidFormHandler).deregisterPlugin(n.FIELD_STATUS_PLUGIN)},n.prototype.onEnabled=function(){this.core.enablePlugin(n.FIELD_STATUS_PLUGIN)},n.prototype.onDisabled=function(){this.core.disablePlugin(n.FIELD_STATUS_PLUGIN)},n.prototype.onFormInvalid=function(){if(this.isEnabled){var t=this.core.getPlugin(n.FIELD_STATUS_PLUGIN).getStatuses(),e=Object.keys(this.core.getFields()).filter(function(m){return t.get(m)==="Invalid"});if(e.length>0){var o=e[0],l=this.core.getElements(o);if(l.length>0){var d=l[0],s={firstElement:d,field:o};this.core.emit("plugins.autofocus.prefocus",s),this.opts.onPrefocus(s),d.focus()}}}},n.FIELD_STATUS_PLUGIN="___autoFocusFieldStatus",n}(_.Plugin);f.AutoFocus=F;c.exports=f;var S=c.exports;try{FormValidation.plugins.AutoFocus=S.AutoFocus}catch{}
