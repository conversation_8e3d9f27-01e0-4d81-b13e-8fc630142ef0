// Alerts
// *******************************************************************************

// Alert mixins
@each $state, $value in $theme-colors {
  @if $state != primary and $state != light {
    @include template-alert-variant('.alert-#{$state}', $value);
    @include template-alert-outline-variant('.alert-outline-#{$state}', $value);
    @include template-alert-solid-variant('.alert-solid-#{$state}', $value);
  }
}

// Alert and alert-icon styles
.alert {
  line-height: 1.375rem;
  .alert-icon {
    color: $white;
    height: $alert-icon-size;
    width: $alert-icon-size;
    padding: $spacer * 0.75;
    margin-right: $spacer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &[class*='alert-solid-'] {
    .alert-icon {
      background-color: $white;
      box-shadow: $box-shadow-xs;
      :before {
        font-size: 1.375rem;
      }
    }
  }
}
// RTL
// *******************************************************************************

@include rtl-only {
  .alert-dismissible {
    padding-left: $alert-dismissible-padding-r;
    padding-right: $alert-padding-x;
  }

  .alert-dismissible .btn-close {
    right: auto;
    left: 0;
  }
  .alert .alert-icon {
    margin-right: 0;
    margin-left: $spacer;
  }
}
