import toastr from 'toastr';
import 'toastr/build/toastr.min.css';

// Optional: cấu hình mặc định
toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: 'toast-top-right',
    timeOut: '5000'
};

$('.downloadTxt').on('click', function () {
    var data = $(this).data('download');
    var keys = Object.keys(data[0]);
    // Tạo dòng header
    var lines = [keys.join('|')];
    // Tạo các dòng dữ liệu
    data.forEach(item => {
        var row = keys.map(k => item[k]).join('|');
        lines.push(row);
    });
    var blob = new Blob([lines.join('\n')], { type: 'text/plain;charset=utf-8' });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = 'data.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
$('.downloadJson').on('click', function () {
    var dataDownload = JSON.stringify($(this).data('download'));
    var blob = new Blob([dataDownload], { type: 'application/json;charset=utf-8' });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = 'data.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
