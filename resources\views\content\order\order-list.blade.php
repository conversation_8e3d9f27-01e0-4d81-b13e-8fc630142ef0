@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Order')

@section('vendor-style')
    @vite([])
@endsection

@section('page-style')
@endsection

@section('vendor-script')
@endsection

@section('page-script')
    @vite(['resources/assets/js/order-index.js'])
@endsection
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="fw-bold py-3 mb-4"><span class="">{{ __('messages.order_history') }}</h4>
        <div class="card">
            <div class="table-responsive text-nowrap">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Stt</th>
                            <th>{{ __('messages.time') }}</th>
                            <th>{{ __('messages.amount') }}</th>
                            <th>{{ __('messages.type') }}</th>
                            <th class="text-center">{{ __('messages.download') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($orders as $order)
                            <tr>
                                <td>{{ $loop->index + 1 }}</td>
                                <td>
                                    <strong>{{ $order->created_at }}</strong>
                                </td>
                                <td>{{ number_format($order->amount) }}đ</td>
                                <td>
                                    {{ $order->type }}
                                </td>
                                <td class="text-center">
                                    <button type="button" data-download="{{ $order->data }}"
                                        class="downloadTxt btn btn-outline-primary btn-sm">
                                        <i class="ti ti-download ti-md"></i> TXT
                                    </button>
                                    <button type="button" data-download="{{ $order->data }}"
                                        class="downloadJson btn btn-outline-success btn-sm">
                                        <i class="ti ti-download ti-md"></i> JSON
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-border-bottom-0">
                        <tr>
                            <th>Stt</th>
                            <th>{{ __('messages.time') }}</th>
                            <th>{{ __('messages.amount') }}</th>
                            <th>{{ __('messages.type') }}</th>
                            <th class="text-center">{{ __('messages.download') }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        @include('layouts.paginate', ['paginate' => $orders])
        <!-- Bootstrap Table with Header - Footer -->
    </div>
@endsection
@push('js')
@endpush
