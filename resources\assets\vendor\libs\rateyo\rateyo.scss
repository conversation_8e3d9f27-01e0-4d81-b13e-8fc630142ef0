// Rateyo
// *******************************************************************************

@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import 'rateyo/src/jquery.rateyo';
@import '../../scss/_custom-variables/libs';

// For Horizontal menu-item, z-index overrides
.jq-ry-container > .jq-ry-group-wrapper > .jq-ry-group,
.jq-ry-container > .jq-ry-group-wrapper > .jq-ry-group.jq-ry-rated-group {
  z-index: 8;
}

// Light Style
@if $enable-light-style {
  .light-style {
    $unratedStarColor: light.rgba-to-hex(rgba(light.$black, 0.16), light.$rgba-to-hex-bg);
    $ratedStarColor: light.$yellow;

    .jq-ry-container {
      padding: 0;
    }
    @include app-ltr-style {
      .jq-ry-container {
        .jq-ry-normal-group {
          svg {
            fill: $unratedStarColor;
          }
        }
      }
      .jq-ry-container:not(.multi-color-ratings) {
        .jq-ry-rated-group {
          svg {
            fill: $ratedStarColor;
          }
        }
      }
    }
    @include app-rtl-style {
      .jq-ry-container:not(.multi-color-ratings) {
        .jq-ry-normal-group {
          svg {
            fill: $ratedStarColor;
          }
        }
      }
      .jq-ry-container {
        .jq-ry-rated-group {
          svg {
            fill: light.rgba-to-hex($unratedStarColor, light.$rgba-to-hex-bg);
          }
        }
      }
    }
  }
}

// Dark Style
@if $enable-dark-style {
  .dark-style {
    $unratedStarColor: dark.rgba-to-hex(rgba(dark.$base, 0.16), dark.$rgba-to-hex-bg);
    $ratedStarColor: dark.$yellow;
    @include app-ltr-style {
      .jq-ry-container {
        .jq-ry-normal-group {
          svg {
            fill: $unratedStarColor;
          }
        }
      }
      .jq-ry-container:not(.multi-color-ratings) {
        .jq-ry-rated-group {
          svg {
            fill: $ratedStarColor;
          }
        }
      }
    }
    @include app-rtl-style {
      .jq-ry-container:not(.multi-color-ratings) {
        .jq-ry-normal-group {
          svg {
            fill: $ratedStarColor;
          }
        }
      }
      .jq-ry-container {
        .jq-ry-rated-group {
          svg {
            fill: dark.rgba-to-hex($unratedStarColor, dark.$rgba-to-hex-bg);
          }
        }
      }
    }
  }
}
