<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('software', function (Blueprint $table) {
            $table->id();
            $table->string('url')->unique();
            $table->string('name');
            $table->string('en_name')->nullable();
            $table->text('description');
            $table->text('en_description')->nullable();
            $table->text('video');
            $table->string('image');
            $table->boolean('is_free')->default(false);
            $table->integer('price_1_m')->default(0);
            $table->integer('price_3_m')->default(0);
            $table->integer('price_6_m')->default(0);
            $table->integer('price_1_y')->default(0);
            $table->integer('price_lifetime')->default(0);
            $table->integer('price_code')->default(0);
            $table->string('status')->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('software');
    }
};
