@extends('layouts/layoutMaster')

@section('title', 'Software Add - Apps')

@section('vendor-style')
    @vite(['resources/assets/vendor/libs/quill/typography.scss', 'resources/assets/vendor/libs/quill/katex.scss', 'resources/assets/vendor/libs/quill/editor.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/dropzone/dropzone.scss', 'resources/assets/vendor/libs/flatpickr/flatpickr.scss', 'resources/assets/vendor/libs/tagify/tagify.scss'])
@endsection

@section('vendor-script')
    @vite(['resources/assets/vendor/libs/quill/katex.js', 'resources/assets/vendor/libs/quill/quill.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/dropzone/dropzone.js', 'resources/assets/vendor/libs/jquery-repeater/jquery-repeater.js', 'resources/assets/vendor/libs/flatpickr/flatpickr.js', 'resources/assets/vendor/libs/tagify/tagify.js'])
@endsection

@section('page-script')
    <script>
        window.suggestions = @json($allTags);
    </script>
    @vite(['resources/assets/js/software/software-add.js'])
@endsection

@section('content')
    {{-- <script src="https://cdn.tiny.cloud/1/lx1qhc806q9drlq8kcx8103w35t92rah6u1073xb9t6pd3ji/tinymce/7/tinymce.min.js"
        referrerpolicy="origin"></script>

    <script>
        tinymce.init({
            selector: '#description',
            plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
            toolbar: 'blocks fontfamily fontsize | bold italic underline strikethrough | numlist bullist | link image media table mergetags | align lineheight | tinycomments | indent outdent | emoticons charmap | removeformat',
            tinycomments_author: 'Author name',
        });
    </script> --}}
    <div class="app-ecommerce">
        <form action="{{ route('software-create') }}" method="POST" enctype="multipart/form-data">
            @csrf
            <!-- Add Product -->
            <div
                class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-6 row-gap-4">

                <div class="d-flex flex-column justify-content-center">
                    <h4 class="mb-1">Add a new Software</h4>
                    <p class="mb-0">Orders placed across your store</p>
                </div>
                <div class="d-flex align-content-center flex-wrap gap-4">
                    <div class="d-flex gap-4">
                        <button class="btn btn-label-secondary">Discard</button>
                        {{-- <button class="btn btn-label-primary">Save draft</button> --}}
                    </div>
                    <button type="submit" class="btn btn-primary">Add software</button>
                </div>

            </div>

            <div class="row">
                <!-- First column-->
                <div class="col-12 col-lg-8">
                    <!-- Product Information -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-tile mb-0">Software information</h5>
                        </div>
                        <div class="card-body">
                            <!-- Name -->
                            <div class="mb-6">
                                <label class="form-label" for="ecommerce-product-name">Name</label>
                                <input type="text" class="form-control" id="ecommerce-product-name"
                                    placeholder="Software name" name="name" aria-label="Software name"
                                    value="{{ old('name') }}">
                                @error('name')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <!-- Video -->
                            <div class="mb-6">
                                <label class="form-label" for="video">Demo Video</label>
                                <input type="text" class="form-control" id="video" placeholder="Software video"
                                    name="video" aria-label="Software video" value="{{ old('video') }}">
                                @error('video')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <!-- Description -->
                            <div>
                                <label class="mb-1">Description</label>
                                <textarea class="form-control" name="description" id="description" cols="30" rows="10">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <!-- /Product Information -->
                    <!-- Media -->
                    <div class="card mb-6">
                        <div class="card-body">
                            <div class="fallback">
                                <input name="image" type="file" />
                                @error('image')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <!-- /Media -->
                </div>
                <!-- /Second column -->

                <!-- Second column -->
                <div class="col-12 col-lg-4">
                    <!-- Pricing Card -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Pricing</h5>
                        </div>
                        <div class="card-body">
                            <!-- Free switch -->
                            <div class="d-flex justify-content-between align-items-center border-top pt-2">
                                <span class="mb-0">Free</span>
                                <div class="w-25 d-flex justify-content-end">
                                    <div class="form-check form-switch me-n3">
                                        <input type="checkbox" class="form-check-input" name="is_free"
                                            {{ old('is_free') ? 'checked' : '' }} />
                                    </div>
                                </div>
                            </div>
                            <!-- Pricing Fields -->
                            @foreach (['price_1_m' => '1 Month', 'price_3_m' => '3 Month', 'price_6_m' => '6 Month', 'price_1_y' => '1 Year', 'price_lifetime' => 'Lifetime', 'price_code' => 'Source Code'] as $field => $label)
                                <div class="mb-6">
                                    <label class="form-label" for="{{ $field }}">{{ $label }}</label>
                                    <input type="number" class="form-control" id="{{ $field }}" placeholder="Price"
                                        name="{{ $field }}" aria-label="Product price" value="{{ old($field) }}">
                                    @error($field)
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- /Pricing Card -->
                    <!-- Organize Card -->
                    <div class="card mb-6">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Organize</h5>
                        </div>
                        <div class="card-body">
                            <!-- Tags -->
                            <div>
                                <label for="tags" class="form-label mb-1">Tags</label>
                                <input id="tags" class="form-control" name="tags"
                                    value="{{ old('tags', 'tool,software,mmo') }}" aria-label="Product Tags" />
                                @error('tags')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <!-- Status -->
                            <div class="mb-6 col ecommerce-select2-dropdown">
                                <label class="form-label mb-1" for="status">Status</label>
                                <select id="status" name="status" class="select2 form-select"
                                    data-placeholder="Published">
                                    <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active
                                    </option>
                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                                @error('status')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <!-- /Organize Card -->
                </div>
                <!-- /Second column -->
            </div>
        </form>
    </div>

@endsection
