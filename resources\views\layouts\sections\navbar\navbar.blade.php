@php
    use Illuminate\Support\Facades\Auth;
    use Illuminate\Support\Facades\Route;
    $containerNav = $configData['contentLayout'] === 'compact' ? 'container-xxl' : 'container-fluid';
    $navbarDetached = $navbarDetached ?? '';
@endphp


{{-- // ,
//     {
//       "url": "app/email",
//       "name": "Edumail",
//       "icon": "menu-icon tf-icons ti ti-mail-opened",
//       "slug": "app-email"
//     },
//     {
//       "url": "app/email",
//       "name": "Tempmail Free",
//       "icon": "menu-icon tf-icons ti ti-mail-fast",
//       "slug": "app-email"
//     },
//     {
//       "menuHeader": "Rent OTP"
//     },
//     {
//       "url": "app/email",
//       "name": "SMS",
//       "icon": "menu-icon tf-icons ti ti-device-sim",
//       "slug": "app-email"
//     },
//     {
//       "menuHeader": "Social Boost"
//     },
//     {
//       "url": "app/email",
//       "name": "Facebook",
//       "icon": "menu-icon tf-icons ti ti-brand-facebook",
//       "slug": "app-email"
//     },
//     {
//       "url": "app/email",
//       "name": "Tiktok",
//       "icon": "menu-icon tf-icons ti ti-brand-tiktok",
//       "slug": "app-email"
//     },
//     {
//       "url": "app/email",
//       "name": "Instagram",
//       "icon": "menu-icon tf-icons ti ti-brand-instagram",
//       "slug": "app-email"
//     } --}}


<!-- Navbar -->
@if (isset($navbarDetached) && $navbarDetached == 'navbar-detached')
    <nav class="layout-navbar {{ $containerNav }} navbar navbar-expand-xl {{ $navbarDetached }} align-items-center bg-navbar-theme"
        id="layout-navbar">
@endif
@if (isset($navbarDetached) && $navbarDetached == '')
    <nav class="layout-navbar navbar navbar-expand-xl align-items-center bg-navbar-theme" id="layout-navbar">
        <div class="{{ $containerNav }}">
@endif

<!--  Brand demo (display only for navbar-full and hide on below xl) -->
@if (isset($navbarFull))
    <div class="navbar-brand app-brand demo d-none d-xl-flex py-0 me-4">
        <a href="{{ url('/') }}" class="app-brand-link">
            <span class="app-brand-logo demo">@include('_partials.macros', ['height' => 20])</span>
            <span class="app-brand-text demo menu-text fw-bold">{{ config('variables.templateName') }}</span>
        </a>
        @if (isset($menuHorizontal))
            <a href="#" class="layout-menu-toggle menu-link text-large ms-auto d-xl-none">
                <i class="ti ti-x ti-md align-middle"></i>
            </a>
        @endif
    </div>
@endif

<!-- ! Not required for layout-without-menu -->
@if (!isset($navbarHideToggle))
    <div
        class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0{{ isset($menuHorizontal) ? ' d-xl-none ' : '' }} {{ isset($contentNavbar) ? ' d-xl-none ' : '' }}">
        <a class="nav-item nav-link px-0 me-xl-4" href="#"
           aria-label="Toggle navigation menu"
           role="button">
            <i class="ti ti-menu-2 ti-md"></i>
        </a>
    </div>
@endif

<div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">

    {{-- @if (!isset($menuHorizontal))
        <!-- Search -->
        <div class="navbar-nav align-items-center">
            <div class="nav-item navbar-search-wrapper mb-0">
                <a class="nav-item nav-link search-toggler d-flex align-items-center px-0" href="#">
                    <i class="ti ti-search ti-md me-2 me-lg-4 ti-lg"></i>
                    <span class="d-none d-md-inline-block text-muted fw-normal">Search (Ctrl+/)</span>
                </a>
            </div>
        </div>
        <!-- /Search -->
    @endif --}}

    <ul class="navbar-nav flex-row align-items-center ms-auto">
        {{-- @if (isset($menuHorizontal))
            <!-- Search -->
            <li class="nav-item navbar-search-wrapper">
                <a class="nav-link btn btn-text-secondary btn-icon rounded-pill search-toggler"
                    href="#">
                    <i class="ti ti-search ti-md"></i>
                </a>
            </li>
            <!-- /Search -->
        @endif --}}

        <!-- Balance -->
        @if (Auth::check())
            <li class="nav-item d-none d-md-flex">
                <a class="nav-link btn btn-text-secondary" href="#" title="Balance">
                    <i class="ti ti-wallet ti-md"></i>
                    <span class="ms-1">{{ number_format(Auth::user()->balance ?? 0) }}VND</span>
                </a>
            </li>
        @endif
        <!--/ Balance -->

        <!-- Language -->
        <li class="nav-item dropdown-language dropdown">
            <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                href="#" data-bs-toggle="dropdown"
                aria-label="Language selector"
                role="button"
                aria-expanded="false">
                <i class='ti ti-language rounded-circle ti-md'></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item {{ app()->getLocale() === 'vi' ? 'active' : '' }}"
                        href="{{ url('lang/vi') }}" data-language="vi" data-text-direction="ltr">
                        <span>Tiếng Việt</span>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {{ app()->getLocale() === 'en' ? 'active' : '' }}"
                        href="{{ url('lang/en') }}" data-language="en" data-text-direction="ltr">
                        <span>English</span>
                    </a>
                </li>
            </ul>
        </li>
        <!--/ Language -->

        @if ($configData['hasCustomizer'] == true)
            <!-- Style Switcher -->
            <li class="nav-item dropdown-style-switcher dropdown">
                <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                    href="#" data-bs-toggle="dropdown"
                    aria-label="Theme switcher"
                    role="button"
                    aria-expanded="false">
                    <i class='ti ti-md'></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end dropdown-styles">
                    <li>
                        <a class="dropdown-item" href="#" data-theme="light" onclick="return false;">
                            <span class="align-middle"><i class='ti ti-sun ti-md me-3'></i>Light</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" data-theme="dark" onclick="return false;">
                            <span class="align-middle"><i class="ti ti-moon-stars ti-md me-3"></i>Dark</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" data-theme="system" onclick="return false;">
                            <span class="align-middle"><i
                                    class="ti ti-device-desktop-analytics ti-md me-3"></i>System</span>
                        </a>
                    </li>
                </ul>
            </li>
            <!-- / Style Switcher -->
        @endif

        <!-- Notification -->
        <li class="nav-item dropdown-notifications navbar-dropdown dropdown me-3 me-xl-2">
            <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                href="#" data-bs-toggle="dropdown" data-bs-auto-close="outside"
                aria-label="Notifications"
                role="button"
                aria-expanded="false">
                <span class="position-relative">
                    <i class="ti ti-bell ti-md"></i>
                    <span class="badge rounded-pill bg-danger badge-dot badge-notifications border"></span>
                </span>
            </a>
            <ul class="dropdown-menu dropdown-menu-end p-0">
                <li class="dropdown-menu-header border-bottom">
                    <div class="dropdown-header d-flex align-items-center py-3">
                        <h6 class="mb-0 me-auto">Notification</h6>
                        <div class="d-flex align-items-center h6 mb-0">
                            <span class="badge bg-label-primary me-2">8 New</span>
                            <a href="#"
                                class="btn btn-text-secondary rounded-pill btn-icon dropdown-notifications-all"
                                data-bs-toggle="tooltip" data-bs-placement="top" title="Mark all as read"><i
                                    class="ti ti-mail-opened text-heading"></i></a>
                        </div>
                    </div>
                </li>
                <li class="dropdown-notifications-list scrollable-container">
                    <ul class="list-group list-group-flush">
                        
                    </ul>
                </li>
                <li class="border-top">
                    <div class="d-grid p-4">
                        <a class="btn btn-primary btn-sm d-flex" href="{{ url('/notifications') }}">
                            <small class="align-middle">View all notifications</small>
                        </a>
                    </div>
                </li>
            </ul>
        </li>
        <!--/ Notification -->

        <!-- User -->
        <li class="nav-item navbar-dropdown dropdown-user dropdown">
            <a class="nav-link dropdown-toggle hide-arrow p-0" href="#" data-bs-toggle="dropdown"
               aria-label="User account menu"
               role="button"
               aria-expanded="false">
                <div class="avatar avatar-online">
                    <img src="{{ Auth::user() ? asset('assets/img/avatars/1.png') : asset('assets/img/avatars/0.png') }}"
                        alt="User avatar" class="rounded-circle">
                </div>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item mt-0"
                        href="{{ Route::has('profile.show') ? route('profile.show') : url('pages/profile-user') }}">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-2">
                                <div class="avatar avatar-online">
                                    <img src="{{ Auth::user() ? asset('assets/img/avatars/1.png') : asset('assets/img/avatars/0.png') }}"
                                        alt class="rounded-circle">
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">
                                    @if (Auth::check())
                                        {{ Auth::user()->name }}
                                    @else
                                        Need login
                                    @endif
                                </h6>
                                <small class="text-muted">{{ number_format(Auth::user()->balance ?? 0) }}VND</small>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <div class="dropdown-divider my-1 mx-n2"></div>
                </li>
                @if (Auth::check())
                    {{-- <li>
                        <a class="dropdown-item"
                            href="{{ Route::has('profile.show') ? route('profile.show') : url('pages/profile-user') }}">
                            <i class="ti ti-user me-3 ti-md"></i><span class="align-middle">My Profile</span>
                        </a>
                    </li> --}}
                    <li>
                        <a class="dropdown-item" href="{{ route('deposit-index') }}">
                            <i class="ti ti-premium-rights me-3 ti-md"></i><span
                                class="align-middle">{{ __('messages.deposit') }}</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ route('order-list') }}">
                            <i class="ti ti-truck-return me-3 ti-md"></i><span
                                class="align-middle">{{ __('messages.purchase_history') }}</span>
                        </a>
                    </li>

                    {{-- <li>
                        <a class="dropdown-item" href="{{ url('pages/account-settings-billing') }}">
                            <span class="d-flex align-items-center align-middle">
                                <i class="flex-shrink-0 ti ti-file-dollar me-3 ti-md"></i><span
                                    class="flex-grow-1 align-middle">Billing</span>
                                <span
                                    class="flex-shrink-0 badge bg-danger d-flex align-items-center justify-content-center">4</span>
                            </span>
                        </a>
                    </li> --}}
                @endif

                @if (Auth::check())
                    <li>
                        <a class="dropdown-item" href="">
                            <i class="ti ti-key ti-md me-3"></i><span class="align-middle">API Tokens</span>
                        </a>
                    </li>
                @endif
                <li>
                    <div class="dropdown-divider my-1 mx-n2"></div>
                </li>
                @if (Auth::check())
                    <li>
                        <div class="d-grid px-2 pt-2 pb-1">
                            <a class="btn btn-sm btn-danger d-flex" href="{{ route('auth-logout') }}">
                                <small class="align-middle">Logout</small>
                                <i class="ti ti-logout ms-2 ti-14px"></i>
                            </a>
                        </div>
                    </li>
                @else
                    <li>
                        <div class="d-grid px-2 pt-2 pb-1">
                            <a class="btn btn-sm btn-primary d-flex"
                                href="{{ Route::has('login') ? route('login') : url('auth/login') }}">
                                <small class="align-middle">Login</small>
                                <i class="ti ti-login ms-2 ti-14px"></i>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="d-grid px-2 pt-2 pb-1">
                            <a class="btn btn-sm btn-success d-flex"
                                href="{{ Route::has('register') ? route('register') : url('auth/register') }}">
                                <small class="align-middle">Register</small>
                                <i class="ti ti-login ms-2 ti-14px"></i>
                            </a>
                        </div>
                    </li>
                @endif
            </ul>
        </li>
        <!--/ User -->
    </ul>
</div>

<!-- Search Small Screens -->
{{-- <div class="navbar-search-wrapper search-input-wrapper {{ isset($menuHorizontal) ? $containerNav : '' }} d-none">
    <input type="text"
        class="form-control search-input {{ isset($menuHorizontal) ? '' : $containerNav }} border-0"
        placeholder="Search..." aria-label="Search...">
    <i class="ti ti-x search-toggler cursor-pointer"></i>
</div> --}}
<!--/ Search Small Screens -->
{{-- @if (isset($navbarDetached) && $navbarDetached == '')
    </div>
@endif --}}
</nav>
<!-- / Navbar -->
