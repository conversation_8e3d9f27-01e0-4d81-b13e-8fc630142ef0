<?php $__env->startSection('title', __('sim_rental.page_title')); ?>

<?php $__env->startSection('vendor-style'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.scss']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-style'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/scss/pages/cards-advance.scss', 'resources/assets/scss/pages/sim-rental.scss']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/js/card/cards-advance.js', 'resources/assets/js/sim-rental.js']); ?>
    <script>
        // Pass translations to JavaScript
        window.simRentalTranslations = {
            chooseCountry: '<?php echo e(__('sim_rental.choose_country')); ?>',
            chooseService: '<?php echo e(__('sim_rental.choose_service')); ?>',
            pleaseChooseCountry: '<?php echo e(__('sim_rental.please_choose_country')); ?>',
            pleaseChooseService: '<?php echo e(__('sim_rental.please_choose_service')); ?>',
            renting: '<?php echo e(__('sim_rental.renting')); ?>',
            rentSimNow: '<?php echo e(__('sim_rental.rent_sim_now')); ?>',
            rentalSuccess: '<?php echo e(__('sim_rental.rental_success')); ?>',
            rentalError: '<?php echo e(__('sim_rental.rental_error')); ?>',
            error: '<?php echo e(__('sim_rental.error')); ?>',
            waitingOtp: '<?php echo e(__('sim_rental.waiting_otp')); ?>',
            available: '<?php echo e(__('sim_rental.available')); ?>',
            outOfStock: '<?php echo e(__('sim_rental.out_of_stock')); ?>',
            currency: '<?php echo e(__('sim_rental.currency')); ?>',
            viewDetails: '<?php echo e(__('sim_rental.view_details')); ?>',
            copyNumber: '<?php echo e(__('sim_rental.copy_number')); ?>',
            refresh: '<?php echo e(__('sim_rental.refresh')); ?>',
            cancelSim: '<?php echo e(__('sim_rental.cancel_sim')); ?>',
            active: '<?php echo e(__('sim_rental.active')); ?>',
            completed: '<?php echo e(__('sim_rental.completed')); ?>',
            expired: '<?php echo e(__('sim_rental.expired')); ?>',
            failed: '<?php echo e(__('sim_rental.failed')); ?>'
        };
    </script>


<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">🔢 <?php echo e(__('sim_rental.page_title')); ?></h4>
                            <p class="mb-0 text-muted"><?php echo e(__('sim_rental.page_subtitle')); ?></p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="badge bg-label-success fs-6 px-3 py-2">
                                <i class="ti ti-check me-1"></i>
                                <?php echo e(__('sim_rental.system_status')); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Selection Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3"><?php echo e(__('sim_rental.choose_server')); ?></h5>
                    <div class="server-tabs">
                        <?php $__currentLoopData = $servers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $server): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <button class="server-tab <?php echo e($index === 0 ? 'active' : ''); ?>" data-server="<?php echo e($server->id); ?>">
                                <i class="ti ti-server me-2"></i>
                                <?php echo e($server->name); ?>

                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="countrySelect" class="form-label fw-medium"><?php echo e(__('sim_rental.country')); ?></label>
                        <select id="countrySelect" class="select2 form-select">
                            <option value=""><?php echo e(__('sim_rental.choose_country')); ?></option>
                            
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="serviceSelect" class="form-label fw-medium"><?php echo e(__('sim_rental.service')); ?></label>
                        <select id="serviceSelect" class="select2 form-select">
                            <option value=""><?php echo e(__('sim_rental.choose_service')); ?></option>
                            
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button id="rentSimBtn" class="btn btn-primary w-100 d-flex align-items-center justify-content-center" style="height: 42px;">
                            <i class="ti ti-plus me-2"></i>
                            <?php echo e(__('sim_rental.rent_sim_now')); ?>

                        </button>
                    </div>
                </div>
            </div>



            <!-- Sim Rental History -->
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">📋 <?php echo e(__('sim_rental.rental_history')); ?></h5>
                        <p class="mb-0 text-muted"><?php echo e(__('sim_rental.rental_history_subtitle')); ?></p>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ti ti-filter me-1"></i>
                            <?php echo e(__('sim_rental.filter')); ?>

                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all"><?php echo e(__('sim_rental.all')); ?></a></li>
                            <li><a class="dropdown-item" href="#" data-filter="active"><?php echo e(__('sim_rental.active')); ?></a></li>
                            <li><a class="dropdown-item" href="#" data-filter="completed"><?php echo e(__('sim_rental.completed')); ?></a></li>
                            <li><a class="dropdown-item" href="#" data-filter="expired"><?php echo e(__('sim_rental.expired')); ?></a></li>
                            <li><a class="dropdown-item" href="#" data-filter="failed"><?php echo e(__('sim_rental.failed')); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="simHistoryTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('sim_rental.id')); ?></th>
                                    <th><?php echo e(__('sim_rental.phone_number')); ?></th>
                                    <th><?php echo e(__('sim_rental.country_name')); ?></th>
                                    <th><?php echo e(__('sim_rental.service_name')); ?></th>
                                    <th><?php echo e(__('sim_rental.server')); ?></th>
                                    <th><?php echo e(__('sim_rental.price')); ?></th>
                                    <th><?php echo e(__('sim_rental.rental_time')); ?></th>
                                    <th><?php echo e(__('sim_rental.expires_at')); ?></th>
                                    <th><?php echo e(__('sim_rental.status')); ?></th>
                                    <th><?php echo e(__('sim_rental.otp_code')); ?></th>
                                    <th><?php echo e(__('sim_rental.actions')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Row 1 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM001</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+84 987 654 321</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/vn.png" alt="VN" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span><?php echo e(__('sim_rental.vietnam')); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-telegram text-primary me-2"></i>
                                            <span><?php echo e(__('sim_rental.telegram')); ?></span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">15,000 <?php echo e(__('sim_rental.currency')); ?></span></td>
                                    <td><small class="text-muted">2024-01-15 14:30</small></td>
                                    <td><small class="text-muted">2024-01-15 14:40</small></td>
                                    <td><span class="badge bg-success"><?php echo e(__('sim_rental.completed')); ?></span></td>
                                    <td><span class="badge bg-label-info">123456</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 2 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM002</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">****** 123 4567</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/us.png" alt="US" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Hoa Kỳ</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-whatsapp text-success me-2"></i>
                                            <span>WhatsApp</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-info">Server 2</span></td>
                                    <td><span class="text-success fw-medium">25,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 15:15</small></td>
                                    <td><small class="text-muted">2024-01-15 15:25</small></td>
                                    <td><span class="badge bg-warning">Đang chờ</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 3 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM003</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+44 20 7946 0958</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/gb.png" alt="UK" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Anh</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-facebook text-info me-2"></i>
                                            <span>Facebook</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-warning">Server 3</span></td>
                                    <td><span class="text-success fw-medium">35,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 13:45</small></td>
                                    <td><small class="text-muted">2024-01-15 13:55</small></td>
                                    <td><span class="badge bg-danger">Hết hạn</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thuê lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 4 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM004</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+81 3 1234 5678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/jp.png" alt="JP" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Nhật Bản</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-twitter text-info me-2"></i>
                                            <span>Twitter</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-success">Server 1</span></td>
                                    <td><span class="text-success fw-medium">20,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 16:20</small></td>
                                    <td><small class="text-muted">2024-01-15 16:30</small></td>
                                    <td><span class="badge bg-primary">Đang hoạt động</span></td>
                                    <td><span class="text-muted">Đang chờ...</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-copy me-2"></i>Copy số</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Làm mới</a></li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li><a class="dropdown-item text-danger" href="#"><i
                                                            class="ti ti-trash me-2"></i>Hủy sim</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Row 5 -->
                                <tr>
                                    <td><span class="badge bg-label-primary">#SIM005</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-phone me-2 text-primary"></i>
                                            <span class="fw-medium">+49 30 12345678</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="https://flagcdn.com/24x18/de.png" alt="DE" class="me-2"
                                                style="width: 20px; height: 15px;">
                                            <span>Đức</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-brand-instagram text-danger me-2"></i>
                                            <span>Instagram</span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-label-danger">Server 4</span></td>
                                    <td><span class="text-success fw-medium">45,000 VNĐ</span></td>
                                    <td><small class="text-muted">2024-01-15 12:10</small></td>
                                    <td><small class="text-muted">2024-01-15 12:20</small></td>
                                    <td><span class="badge bg-danger">Thất bại</span></td>
                                    <td><span class="text-muted">-</span></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="#"><i
                                                            class="ti ti-refresh me-2"></i>Thử lại</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            Hiển thị 1-5 trong tổng số 23 kết quả
                        </div>
                        <nav aria-label="Sim history pagination">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">
                                        <i class="ti ti-chevron-left"></i>
                                    </a>
                                </li>
                                <li class="page-item active">
                                    <a class="page-link" href="#">1</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">
                                        <i class="ti ti-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\ppresent\resources\views/content/sim-rental/sim-rental-index.blade.php ENDPATH**/ ?>