// * Logistics Fleet
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_components/include';
@import '../_custom-variables/pages';

//Custom variables for Logistics-fleet page
$logistics-fleet-sidebar-width: 21.5rem !default;
$logistics-fleet-height: calc(100vh - 13.8rem);
$logistics-fleet-sidebar-height: calc(100vh - 15.8rem);
$logistics-fleet-horizontal-height-diff: 2rem;

// App logistics-fleet wrapper for sidebar
.app-logistics-fleet-wrapper {
  position: relative;
  border: none;
  overflow: hidden;
  height: $logistics-fleet-height;

  @include light.media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    & {
      height: calc(100vh - 10.5rem);
    }
  }
  //setting wrapper height when navbar is hidden
  .layout-navbar-hidden & {
    height: calc(100vh - 6.5rem);

    //For Sidebar Map menu Scroll
    .logistics-fleet-sidebar-body {
      height: calc($logistics-fleet-sidebar-height + 3rem) !important;
    }
  }
  //Sidebar functionality
  .app-logistics-fleet-sidebar {
    position: absolute;
    overflow: hidden;
    // border-radius: light.$border-radius;
    background-color: light.$card-bg;
    flex-grow: 0;
    flex-basis: $logistics-fleet-sidebar-width;
    left: calc(-#{$logistics-fleet-sidebar-width} - 1.2rem);
    height: 100%;
    width: $logistics-fleet-sidebar-width;
    transition: all 0.3s;
    z-index: 2;
    &.show {
      left: 0;
    }
  }

  //Close sidebar button
  .close-sidebar {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
  }

  //Setting sidebar height
  .logistics-fleet-sidebar-body {
    height: calc(100vh - 13rem);
    @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
      height: $logistics-fleet-sidebar-height !important;
    }
  }

  @include light.media-breakpoint-down(md) {
    //Setting wrapper height when screen < md
    & {
      height: calc(100vh - 11.5rem);
      //For Sidebar Map menu Scroll
      .logistics-fleet-sidebar-body {
        height: calc($logistics-fleet-sidebar-height - 1rem);
      }
    }
    //setting wrapper height when navbar is hidden
    .layout-navbar-hidden & {
      height: calc(100vh - 7.5rem);

      //For Sidebar Map menu Scroll
      .logistics-fleet-sidebar-body {
        height: calc($logistics-fleet-sidebar-height + 2.5rem) !important;
      }
    }
  }

  //For Sidebar Map menu Button to open in full screen
  @include light.media-breakpoint-up(md) {
    .app-logistics-fleet-sidebar {
      position: static;
      height: auto;
      background-color: transparent !important;
    }
  }
}
// RTL
@if $enable-rtl-support {
  [dir='rtl'] {
    .app-logistics-fleet-wrapper {
      .app-logistics-fleet-sidebar {
        left: auto;
        right: calc(-#{$logistics-fleet-sidebar-width} - 1.2rem);
        &.show {
          left: auto;
          right: 0;
        }
      }
      .close-sidebar {
        position: absolute;
        top: 0.5rem;
        left: 0.5rem;
        right: auto;
      }
    }
  }
}

//Light mode
@if $enable-light-style {
  .light-style {
    .btn-white.btn-white-dark-variant {
      color: light.$headings-color;
      box-shadow: light.$box-shadow-xs !important;
    }
  }
}
//Dark mode
@if $enable-dark-style {
  .dark-style {
    .app-logistics-fleet-wrapper {
      .app-logistics-fleet-sidebar {
        background-color: dark.$card-bg;
      }
      .btn-white.btn-white-dark-variant {
        border-color: dark.$card-bg;
        background-color: dark.$card-bg;
        color: dark.$headings-color;
        box-shadow: dark.$box-shadow-xs !important;
      }
    }
  }
}
