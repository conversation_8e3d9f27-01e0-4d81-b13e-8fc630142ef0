@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';
@import 'datatables.net-bs5/css/dataTables.bootstrap5';

// Margin between select, input field and text
div.dataTables_wrapper div.dataTables_length select {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
div.dataTables_wrapper div.dataTables_filter input {
  margin-left: 1em;
}

.dataTable .emp_name {
  font-weight: light.$font-weight-medium;
}

// Shadow none for action buttons
.dataTable td .btn {
  box-shadow: none !important;
}

// Card header inside the datatable
div.dataTables_wrapper .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
div.dataTables_wrapper div.dataTables_info {
  padding-top: light.$spacer * 0.5;
}

table.table-bordered.dataTable {
  // For complex header and column search datatable
  &.dt-complex-header,
  &.dt-column-search {
    thead tr th {
      border-width: 1px;
    }
    tfoot tr th {
      border-width: 1px;
    }
  }
  & tfoot tr th {
    border-bottom-width: 1px;
  }
  & > :not(caption) > * {
    & > * {
      border-width: 0;
    }
  }
}

// Remove left and right border from datatable with table-bordered class
table.table-bordered.dataTable {
  tr:first-child th,
  td {
    &:first-child {
      @include app-ltr() {
        border-left-width: 0;
      }
      @include app-rtl() {
        border-right-width: 0;
      }
    }
    &:last-child {
      @include app-ltr() {
        border-right-width: 0;
      }
      @include app-rtl() {
        border-left-width: 0;
      }
    }
  }
  > tbody:not(caption) tr:first-child {
    border-top-width: 0;
  }
}

// Responsive datatable in desktop screen
@media screen and (min-width: 1399.98px) {
  table.table-responsive {
    display: table;
  }
}

// RTL style
@include app-rtl(false) {
  div.dataTables_wrapper .dataTables_filter {
    display: flex;
    justify-content: flex-end;
    input {
      margin-left: 0;
      margin-right: 1rem;
    }
  }

  table.table-bordered.dataTable th,
  table.table-bordered.dataTable td {
    border-right-width: 0;
    border-left-width: 1px;

    &:last-child {
      border-left-width: 0;
    }
  }
}

table.dataTable {
  width: 100% !important;
  border-collapse: collapse !important;
  margin-bottom: light.$spacer !important;
  margin-top: 0 !important;
  thead th {
    &.sorting_disabled {
      &::before,
      &::after {
        display: none !important;
      }
    }
    &.sorting {
      &:before,
      &:after {
        visibility: hidden;
      }
      &:hover {
        &:before,
        &:after {
          visibility: visible;
        }
      }
    }
  }
  @include app-rtl {
    &.table-sm > thead > tr > th {
      padding-left: 1.25rem;
    }

    &.table-sm .sorting:before,
    &.table-sm .sorting_asc:before,
    &.table-sm .sorting_desc:before {
      right: auto !important;
      left: 0.85em !important;
    }
    thead th,
    thead td,
    tfoot th,
    tfoot td {
      text-align: right;
    }
  }
  // Checkbox height & width for datatables checkboxes
  .form-check-input {
    width: light.$form-datatables-check-input-size;
    height: light.$form-datatables-check-input-size;
  }
}

// to add spacing between table and datatable footer elements like pagination & info
.dataTables_scroll {
  margin-bottom: 0.75rem;
}

// Used while complex headers
table.dataTable thead th {
  vertical-align: middle;
}
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
  &::before,
  &::after {
    line-height: 1.25rem !important;
    font-family: tabler-icons !important;
    font-size: 1rem !important;
    width: 10px;
    height: 10px;
    right: 0.78rem !important;
  }
  &::before {
    content: '\ea62' !important;
    top: 0.58rem !important;
  }
  &::after {
    bottom: 0.72rem !important;
    content: '\ea5f' !important;
  }
  @include app-rtl {
    &::before {
      right: auto !important;
      left: 0.58em !important;
    }

    &::after {
      right: auto !important;
      left: 0.58em !important;
    }
  }
}

// DataTable within card
div.card-datatable.dataTable,
div.card-datatable .dataTable {
  border-right: 0;
  border-left: 0;
}

// Card header inside the datatable
@media screen and (max-width: 575.98px) {
  div.dataTables_wrapper .card-header {
    display: block;
  }

  .dtr-bs-modal.modal {
    // padding-left: 0.75rem;
    .modal-body {
      padding: 0;
      overflow: auto;
    }
  }
  .dataTable_select div.dataTables_wrapper div.dataTables_info {
    flex-direction: column;
  }
}

@media screen and (max-width: 767.98px) {
  div.dataTables_wrapper div.dataTables_info {
    padding-bottom: light.$table-cell-padding-y;
  }
}

div.dataTables_wrapper {
  div.dataTables_length,
  .dataTables_filter {
    margin-top: light.$spacer * 1.5;
    margin-bottom: light.$spacer * 1.5;
  }
}

// common style for light / dark

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
  .page-item {
    &,
    &.next,
    &.previous,
    &.first,
    &.last {
      .page-link {
        border-radius: light.$border-radius;
      }
    }
  }
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination .page-link {
  div:not(.table-responsive) div.dataTables_wrapper .dataTables_paginate {
    margin-right: 0;
  }
}

@include light.media-breakpoint-down(md) {
  div.dataTables_wrapper div.dataTables_length label,
  div.dataTables_wrapper div.dataTables_filter label,
  div.dataTables_wrapper div.dataTables_info,
  div.dataTables_wrapper div.dataTables_paginate {
    justify-content: center;
  }
}
@include light.media-breakpoint-down(sm) {
  div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    justify-content: start !important;
    overflow-x: scroll;
  }
}

// DataTable within card
div.card-datatable {
  padding-bottom: light.$card-spacer-x-sm; // Check this in site and update if needed

  [class*='col-md-'] {
    padding-right: light.$table-cell-padding-x !important;
    padding-left: light.$table-cell-padding-x !important;
  }
  // length, filter & info, pagination row margin
  &:not(.table-responsive) .dataTables_wrapper .row {
    &:first-child,
    &:last-child {
      margin: 0;
    }
  }
}

// LTR style
@include app-ltr(false) {
  div.card-datatable table.dataTable thead th,
  div.card-datatable table.dataTable tfoot th {
    &:first-child {
      padding-left: light.$card-spacer-x;
      padding-right: light.$card-spacer-x;
    }

    &:last-child {
      padding-right: light.$card-spacer-x-sm;
    }
  }
  div.card-datatable table.dataTable tbody td {
    &:first-child {
      padding-left: light.$card-spacer-x;
      padding-right: light.$card-spacer-x;
    }
  }
}

// RTL Style
@include app-rtl(false) {
  table.dataTable.table-sm > thead > tr > th {
    padding-right: light.$table-cell-padding-x-sm;
  }
  table.table-bordered.dataTable tr {
    td,
    th,
    th:first-child {
      border-left-width: 0 !important;
    }
  }

  table.dataTable {
    thead th,
    tbody td,
    tfoot th {
      padding-right: light.$table-cell-padding-x;
    }

    &.table-sm thead th,
    &.table-sm tbody td,
    &.table-sm tfoot th {
      padding-right: light.$table-cell-padding-x-sm;
    }
  }

  div.card-datatable table.dataTable {
    thead th,
    tbody td,
    tfoot th {
      &:first-child {
        padding-right: light.$card-spacer-x;
      }

      &:last-child {
        padding-left: light.$card-spacer-x;
      }
    }
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    div.dataTables_wrapper div.dataTables_info {
      color: light.$text-muted;
    }

    div.dataTables_scrollBody table {
      border-top-color: light.$table-border-color;
    }

    table.dataTable th,
    table.dataTable td {
      border-color: light.$table-border-color !important;
    }
  }
}

// Dark Style
@if $enable-dark-style {
  .dark-style {
    div.dataTables_wrapper div.dataTables_info {
      color: dark.$text-muted;
    }

    div.dataTables_scrollBody table {
      border-top-color: dark.$table-border-color;
    }

    table.dataTable th,
    table.dataTable td {
      border-color: dark.$table-border-color !important;
    }
  }
}
