<?php

namespace App\Http\Controllers\software;

use App\Http\Controllers\Controller;
use App\Models\Software;
use App\Models\Tag;
use Illuminate\Http\Request;

class SoftwareIndex extends Controller
{
    /**
     * Apply search and filter logic to query
     */
    private function applySearchFilters($query, Request $request)
    {
        // Xử lý tìm kiếm theo tên
        if ($request->filled('search')) {
            $searchTerm = trim($request->search);
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('en_name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('description', 'like', '%' . $searchTerm . '%');
            });
        }

        // Xử lý lọc theo tags - sử dụng relationship
        if ($request->filled('tag') && is_array($request->tag)) {
            $selectedTags = array_filter($request->tag); // Loại bỏ giá trị rỗng
            if (!empty($selectedTags)) {
                $query->whereHas('tags', function($q) use ($selectedTags) {
                    $q->whereIn('name', $selectedTags);
                });
            }
        }

        return $query;
    }

    public function index(Request $request)
    {
        $type = "All";
        $query = Software::with('tags'); // Load relationship

        // Áp dụng bộ lọc tìm kiếm
        $query = $this->applySearchFilters($query, $request);

        $softwares = $query->orderBy('id', 'desc')->paginate(12);

        // Giữ lại các tham số tìm kiếm trong pagination
        $softwares->appends($request->query());
        $allTags = Tag::all();
        return view('content.software.software-index', compact('softwares', 'type', 'allTags'));
    }

    public function free(Request $request)
    {
        $type = "Free";
        $query = Software::with('tags')->where('is_free', true); // Load relationship

        // Áp dụng bộ lọc tìm kiếm
        $query = $this->applySearchFilters($query, $request);

        $softwares = $query->orderBy('id', 'desc')->paginate(12);

        // Giữ lại các tham số tìm kiếm trong pagination
        $softwares->appends($request->query());

        $allTags = Tag::all();
        return view('content.software.software-index', compact('softwares', 'type', 'allTags'));
    }

    public function paid(Request $request)
    {
        $type = "Paid";
        $query = Software::with('tags')->where('is_free', false); // Load relationship

        // Áp dụng bộ lọc tìm kiếm
        $query = $this->applySearchFilters($query, $request);

        $softwares = $query->orderBy('id', 'desc')->paginate(12);

        // Giữ lại các tham số tìm kiếm trong pagination
        $softwares->appends($request->query());

        $allTags = Tag::all();
        return view('content.software.software-index', compact('softwares', 'type', 'allTags'));
    }
}
