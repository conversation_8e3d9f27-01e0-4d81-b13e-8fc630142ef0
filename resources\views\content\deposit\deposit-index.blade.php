@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Deposit')

@section('vendor-style')
    @vite([])
@endsection

@section('page-style')
<style>
.info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--bs-gray-50);
    border-radius: 0.375rem;
    border: 1px solid var(--bs-gray-200);
}

.info-label {
    font-weight: 600;
    color: var(--bs-gray-700);
    min-width: 120px;
    margin-bottom: 0;
}

.info-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: flex-end;
}

.info-value span {
    word-break: break-all;
    text-align: right;
}

.copy-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.copy-btn.copied {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
    color: white;
}

.qr-container {
    background: linear-gradient(135deg, var(--bs-gray-50) 0%, var(--bs-gray-100) 100%);
    border: 2px dashed var(--bs-gray-300);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.qr-container:hover {
    border-color: var(--bs-primary);
    background: linear-gradient(135deg, var(--bs-primary-bg-subtle) 0%, var(--bs-gray-100) 100%);
}

.payment-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.payment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

@media (max-width: 768px) {
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .info-value {
        width: 100%;
        justify-content: space-between;
    }

    .info-value span {
        text-align: left;
        flex: 1;
    }
}
</style>
@endsection

@section('vendor-script')
@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy functionality
    const copyButtons = document.querySelectorAll('.copy-btn');

    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const textToCopy = this.getAttribute('data-copy');

            // Use modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    showCopySuccess(this);
                }).catch(err => {
                    console.error('Failed to copy: ', err);
                    fallbackCopyTextToClipboard(textToCopy, this);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(textToCopy, this);
            }
        });
    });

    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess(button);
            } else {
                showCopyError(button);
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            showCopyError(button);
        }

        document.body.removeChild(textArea);
    }

    function showCopySuccess(button) {
        const originalIcon = button.innerHTML;
        const originalClass = button.className;

        button.innerHTML = '<i class="ti ti-check"></i>';
        button.classList.add('copied');
        button.disabled = true;

        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.className = originalClass;
            button.disabled = false;
        }, 2000);

        // Show toast notification if available
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: '{{ __('messages.copy_success') }}',
                text: '{{ __('messages.copy_success_message') }}',
                timer: 1500,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }

    function showCopyError(button) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: '{{ __('messages.copy_error') }}',
                text: '{{ __('messages.copy_error_message') }}',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }
});
</script>
@endsection
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="row align-items-center mb-4">
                    <div class="col-12 col-md-8 mb-3 mb-md-0">
                        <h1 class="h3 mb-1">{{ __('messages.deposit_title') }}</h1>
                        <p class="text-muted mb-0">{{ __('messages.deposit_description') }}</p>
                    </div>
                    <div class="col-12 col-md-4 text-start text-md-end">
                        <div class="badge bg-primary fs-6 px-3 py-2 d-inline-flex align-items-center">
                            <i class="ti ti-wallet me-2"></i>
                            <span>{{ __('messages.current_balance') }}: <strong>{{ number_format(auth()->user()->balance ?? 0) }} VNĐ</strong></span>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="row g-4">
                    <!-- Bank Transfer Method -->
                    <div class="col-lg-6">
                        <div class="card h-100 shadow-sm payment-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="ti ti-building-bank me-2"></i>
                                    {{ __('messages.bank_transfer') }}
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="text-center mb-4">
                                    <div class="qr-container">
                                        <!-- QR Code cho bank -->
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=Bank:Vietcombank|Account:**********|Name:CONG TY TNHH ABC"
                                            alt="QR Code Bank" class="img-fluid" style="max-width: 180px;">
                                    </div>
                                    <p class="text-muted small mt-2 mb-0">{{ __('messages.scan_qr_bank') }}</p>
                                </div>

                                <div class="bank-info">
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.bank_name') }}:</div>
                                        <div class="info-value">
                                            <span class="text-primary fw-semibold">Vietcombank</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="Vietcombank" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.account_number') }}:</div>
                                        <div class="info-value">
                                            <span class="text-primary fw-semibold">**********</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="**********" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.account_holder') }}:</div>
                                        <div class="info-value">
                                            <span class="text-primary fw-semibold">CONG TY TNHH ABC</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="CONG TY TNHH ABC" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.transfer_content') }}:</div>
                                        <div class="info-value">
                                            <span class="text-danger fw-bold">naptien{{ auth()->user()->id ?? '' }}</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="naptien{{ auth()->user()->id ?? '' }}" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info d-flex align-items-start" role="alert" style="min-height: 70px;">
                                    <i class="ti ti-info-circle me-2 mt-1 flex-shrink-0"></i>
                                    <div>
                                        <strong>{{ __('messages.note') }}:</strong> {{ __('messages.bank_note') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- USDT Transfer Method -->
                    <div class="col-lg-6">
                        <div class="card h-100 shadow-sm payment-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="ti ti-currency-bitcoin me-2"></i>
                                    {{ __('messages.usdt_transfer') }}
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="text-center mb-4">
                                    <div class="qr-container">
                                        <!-- QR Code cho USDT wallet -->
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TXYZabcd**********ABCDEF**********"
                                            alt="QR Code USDT" class="img-fluid" style="max-width: 180px;">
                                    </div>
                                    <p class="text-muted small mt-2 mb-0">{{ __('messages.scan_qr_usdt') }}</p>
                                </div>

                                <div class="usdt-info">
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.network') }}:</div>
                                        <div class="info-value">
                                            <span class="text-warning fw-bold">TRC20</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="TRC20" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.wallet_address') }}:</div>
                                        <div class="info-value">
                                            <span class="text-warning fw-semibold small">TXYZabcd**********ABCDEF**********</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="TXYZabcd**********ABCDEF**********" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.amount_to_send') }}:</div>
                                        <div class="info-value">
                                            <span class="text-danger fw-bold">X.000{{ auth()->user()->id ?? '' }}</span>
                                            <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                data-copy="X.000{{ auth()->user()->id ?? '' }}" title="{{ __('messages.copy_button') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">{{ __('messages.exchange_rate') }}:</div>
                                        <div class="info-value">
                                            <span class="text-info fw-semibold">{{ __('messages.usdt_rate') }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-warning d-flex align-items-start" role="alert" style="min-height: 70px;">
                                    <i class="ti ti-alert-triangle me-2 mt-1 flex-shrink-0"></i>
                                    <div>
                                        <strong>{{ __('messages.note') }}:</strong> {{ __('messages.usdt_note') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="ti ti-help-circle me-2 text-primary"></i>
                                    {{ __('messages.instructions_title') }}
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="border-start border-primary border-3 ps-3">
                                            <h6 class="text-primary mb-3 d-flex align-items-center">
                                                <i class="ti ti-building-bank me-2"></i>
                                                {{ __('messages.bank_instructions_title') }}
                                            </h6>
                                            <ol class="list-group list-group-flush">
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-1 text-primary me-2"></i>
                                                    {{ __('messages.bank_step_1') }}
                                                </li>
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-2 text-primary me-2"></i>
                                                    {{ __('messages.bank_step_2') }} <span class="badge bg-danger">naptien{{ auth()->user()->id ?? '' }}</span>
                                                </li>
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-3 text-primary me-2"></i>
                                                    {{ __('messages.bank_step_3') }} <strong>{{ __('messages.bank_time') }}</strong>
                                                </li>
                                            </ol>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="border-start border-warning border-3 ps-3">
                                            <h6 class="text-warning mb-3 d-flex align-items-center">
                                                <i class="ti ti-currency-bitcoin me-2"></i>
                                                {{ __('messages.usdt_instructions_title') }}
                                            </h6>
                                            <ol class="list-group list-group-flush">
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-1 text-warning me-2"></i>
                                                    {{ __('messages.usdt_step_1') }}
                                                </li>
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-2 text-warning me-2"></i>
                                                    {{ __('messages.usdt_step_2') }} <span class="badge bg-danger">X.000{{ auth()->user()->id ?? '' }}</span>
                                                </li>
                                                <li class="list-group-item border-0 px-0 py-2">
                                                    <i class="ti ti-number-3 text-warning me-2"></i>
                                                    {{ __('messages.usdt_step_3') }} <strong>{{ __('messages.usdt_time') }}</strong>
                                                </li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
@endpush
