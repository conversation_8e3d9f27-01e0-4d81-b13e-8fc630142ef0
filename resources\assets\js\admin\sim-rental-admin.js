/**
 * Sim Rental Admin functionality
 */

'use strict';

import $ from 'jquery';
import Swal from 'sweetalert2';
window.$ = window.jQuery = $;
window.Swal = Swal;

$(document).ready(function() {
    // Initialize DataTables
    initDataTables();
    
    // Initialize form handlers
    initFormHandlers();
    
    // Initialize cascading dropdowns
    initCascadingDropdowns();
});

// Initialize DataTables
function initDataTables() {
    $('#serversTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[4, 'asc']], // Sort by sort_order
        columnDefs: [
            { orderable: false, targets: [6] } // Actions column
        ]
    });

    $('#countriesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[5, 'asc']], // Sort by sort_order
        columnDefs: [
            { orderable: false, targets: [7] } // Actions column
        ]
    });

    $('#servicesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[7, 'asc']], // Sort by sort_order
        columnDefs: [
            { orderable: false, targets: [9] } // Actions column
        ]
    });
}

// Initialize form handlers
function initFormHandlers() {
    // Server form
    $('#serverForm').on('submit', function(e) {
        e.preventDefault();
        submitForm('server');
    });

    // Country form
    $('#countryForm').on('submit', function(e) {
        e.preventDefault();
        submitForm('country');
    });

    // Service form
    $('#serviceForm').on('submit', function(e) {
        e.preventDefault();
        submitForm('service');
    });
}

// Initialize cascading dropdowns
function initCascadingDropdowns() {
    // When server changes in service form, update countries
    $('#serviceServer').on('change', function() {
        const serverId = $(this).val();
        const $countrySelect = $('#serviceCountry');
        
        $countrySelect.empty().append('<option value="">Select Country</option>');
        
        if (serverId && window.adminData.countries) {
            const countries = window.adminData.countries.filter(country => 
                country.server_id == serverId && country.is_active
            );
            
            countries.forEach(country => {
                $countrySelect.append(`<option value="${country.id}">${country.name}</option>`);
            });
        }
    });
}

// Open add modal
window.openAddModal = function(type) {
    const modal = $(`#${type}Modal`);
    const form = $(`#${type}Form`);
    const title = $(`#${type}ModalTitle`);
    
    // Reset form
    form[0].reset();
    form.removeData('edit-id');
    
    // Set title
    title.text(`Add ${type.charAt(0).toUpperCase() + type.slice(1)}`);
    
    // Set default values
    if (type === 'server') {
        $('#serverSortOrder').val(getNextSortOrder('servers'));
    } else if (type === 'country') {
        $('#countrySortOrder').val(getNextSortOrder('countries'));
    } else if (type === 'service') {
        $('#serviceSortOrder').val(getNextSortOrder('services'));
    }
    
    modal.modal('show');
};

// Edit functions
window.editServer = function(id) {
    const server = window.adminData.servers.find(s => s.id === id);
    if (!server) return;
    
    const modal = $('#serverModal');
    const form = $('#serverForm');
    const title = $('#serverModalTitle');
    
    // Set form data
    form.data('edit-id', id);
    $('#serverName').val(server.name);
    $('#serverDescription').val(server.description);
    $('#serverSortOrder').val(server.sort_order);
    $('#serverIsActive').prop('checked', server.is_active);
    
    title.text('Edit Server');
    modal.modal('show');
};

window.editCountry = function(id) {
    const country = window.adminData.countries.find(c => c.id === id);
    if (!country) return;
    
    const modal = $('#countryModal');
    const form = $('#countryForm');
    const title = $('#countryModalTitle');
    
    // Set form data
    form.data('edit-id', id);
    $('#countryName').val(country.name);
    $('#countryCode').val(country.code);
    $('#countryServer').val(country.server_id);
    $('#countrySortOrder').val(country.sort_order);
    $('#countryIsActive').prop('checked', country.is_active);
    
    title.text('Edit Country');
    modal.modal('show');
};

window.editService = function(id) {
    const service = window.adminData.services.find(s => s.id === id);
    if (!service) return;
    
    const modal = $('#serviceModal');
    const form = $('#serviceForm');
    const title = $('#serviceModalTitle');
    
    // Set form data
    form.data('edit-id', id);
    $('#serviceName').val(service.name);
    $('#serviceCode').val(service.code);
    $('#serviceServer').val(service.country.server_id).trigger('change');
    
    // Wait for countries to load, then set country
    setTimeout(() => {
        $('#serviceCountry').val(service.country_id);
    }, 100);
    
    $('#servicePrice').val(service.price);
    $('#serviceStock').val(service.stock);
    $('#serviceSortOrder').val(service.sort_order);
    $('#serviceIsActive').prop('checked', service.is_active);
    
    title.text('Edit Service');
    modal.modal('show');
};

// Delete functions
window.deleteServer = function(id) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'This will delete the server and all its data!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            performDelete('server', id);
        }
    });
};

window.deleteCountry = function(id) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'This will delete the country and all its services!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            performDelete('country', id);
        }
    });
};

window.deleteService = function(id) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'This will delete the service!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            performDelete('service', id);
        }
    });
};

// Submit form
function submitForm(type) {
    const form = $(`#${type}Form`);
    const editId = form.data('edit-id');
    const isEdit = !!editId;
    
    const formData = new FormData(form[0]);
    
    // Convert checkbox to boolean
    formData.set('is_active', $(`#${type}IsActive`).is(':checked') ? '1' : '0');
    
    const url = isEdit 
        ? `/admin/sim-rental/${type}s/${editId}`
        : `/admin/sim-rental/${type}s`;
    
    const method = isEdit ? 'PUT' : 'POST';
    
    // Add method override for PUT
    if (isEdit) {
        formData.append('_method', 'PUT');
    }
    
    // Add CSRF token
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
    
    $.ajax({
        url: url,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            }
        },
        error: function(xhr) {
            let message = 'An error occurred';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            
            Swal.fire({
                title: 'Error!',
                text: message,
                icon: 'error'
            });
        }
    });
}

// Perform delete
function performDelete(type, id) {
    $.ajax({
        url: `/admin/sim-rental/${type}s/${id}`,
        method: 'DELETE',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Deleted!',
                    text: response.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            }
        },
        error: function(xhr) {
            let message = 'An error occurred';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            
            Swal.fire({
                title: 'Error!',
                text: message,
                icon: 'error'
            });
        }
    });
}

// Get next sort order
function getNextSortOrder(type) {
    if (!window.adminData[type] || window.adminData[type].length === 0) {
        return 1;
    }
    
    const maxOrder = Math.max(...window.adminData[type].map(item => item.sort_order || 0));
    return maxOrder + 1;
}
