/**
 * Software List Admin (jquery)
 */

'use strict';

$(function () {
  // Variable declaration for table
  var dt_software_table = $('.software-list-table');

  // Software datatable
  if (dt_software_table.length) {
    var dt_software = dt_software_table.DataTable({
      processing: true,
      serverSide: false,
      responsive: true,
      order: [[0, 'desc']],
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json'
      },
      columnDefs: [
        {
          targets: 0,
          orderable: true,
          searchable: false
        },
        {
          targets: 1,
          orderable: false,
          searchable: false
        },
        {
          targets: -1,
          orderable: false,
          searchable: false
        }
      ]
    });

    // Filter by status
    $('#SoftwareStatus').on('change', function () {
      var status = $(this).val();
      if (status) {
        dt_software.column(5).search(status).draw();
      } else {
        dt_software.column(5).search('').draw();
      }
    });

    // Filter by type
    $('#SoftwareType').on('change', function () {
      var type = $(this).val();
      if (type === 'free') {
        dt_software.column(3).search('Miễn phí').draw();
      } else if (type === 'paid') {
        dt_software.column(3).search('Trả phí').draw();
      } else {
        dt_software.column(3).search('').draw();
      }
    });
  }

  // Delete software
  $(document).on('click', '.delete-software', function () {
    var softwareId = $(this).data('id');
    var row = $(this).closest('tr');

    Swal.fire({
      title: 'Bạn có chắc chắn?',
      text: 'Bạn sẽ không thể hoàn tác hành động này!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Có, xóa nó!',
      cancelButtonText: 'Hủy'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: '/software/' + softwareId,
          type: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function (response) {
            if (response.success) {
              Swal.fire({
                title: 'Đã xóa!',
                text: response.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
              });
              
              // Remove row from table
              if (dt_software_table.length) {
                dt_software.row(row).remove().draw();
              } else {
                row.fadeOut(300, function() {
                  $(this).remove();
                });
              }
            } else {
              Swal.fire({
                title: 'Lỗi!',
                text: response.message,
                icon: 'error'
              });
            }
          },
          error: function () {
            Swal.fire({
              title: 'Lỗi!',
              text: 'Có lỗi xảy ra khi xóa software!',
              icon: 'error'
            });
          }
        });
      }
    });
  });

  // Toggle status
  $(document).on('click', '.toggle-status', function () {
    var softwareId = $(this).data('id');
    var currentStatus = $(this).data('status');
    var newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    var button = $(this);

    $.ajax({
      url: '/software/' + softwareId + '/status',
      type: 'PATCH',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      data: {
        status: newStatus
      },
      success: function (response) {
        if (response.success) {
          // Update button
          button.data('status', newStatus);
          var statusBadge = button.closest('tr').find('.status-badge');
          
          if (newStatus === 'active') {
            statusBadge.removeClass('bg-label-secondary').addClass('bg-label-success').text('Hoạt động');
            button.removeClass('btn-outline-success').addClass('btn-outline-secondary').text('Tắt');
          } else {
            statusBadge.removeClass('bg-label-success').addClass('bg-label-secondary').text('Không hoạt động');
            button.removeClass('btn-outline-secondary').addClass('btn-outline-success').text('Bật');
          }

          // Show success message
          toastr.success(response.message);
        } else {
          toastr.error(response.message);
        }
      },
      error: function () {
        toastr.error('Có lỗi xảy ra khi cập nhật trạng thái!');
      }
    });
  });

  // Initialize tooltips
  $('[data-bs-toggle="tooltip"]').tooltip();
});
