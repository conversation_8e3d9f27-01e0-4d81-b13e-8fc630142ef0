// Custom Options
// *******************************************************************************

// Custom option
.custom-option {
  padding-left: 0;
  border: $custom-option-border-width solid $custom-option-border-color;
  border-radius: $border-radius;
  margin: subtract($input-focus-border-width, $custom-option-border-width);
  &:hover {
    border-width: $custom-option-border-width;
    border-color: $custom-option-border-hover-color;
  }
  &.custom-option-image {
    border-width: $custom-option-image-border-width !important;
    overflow: hidden;
    margin: 0;
    &:hover {
      border-width: $custom-option-image-border-width !important;
      border-color: $custom-option-border-hover-color;
    }
  }
  .custom-option-content {
    cursor: $custom-option-cursor;
    width: 100%;
  }
  .form-check-input {
    background-color: transparent;
    margin-inline-start: $form-check-padding-start * -1.12;
  }
}

// Custom option basic
.custom-option-basic {
  .custom-option-content {
    padding: $custom-option-padding;
    padding-left: $custom-option-padding + $form-check-padding-start + 0.65em;
  }
  .custom-option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.4375rem;
  }
}

.custom-option-body {
  color: $body-color;
}

// Custom option with icon
.custom-option-icon {
  overflow: hidden;
  &.checked {
    i,
    svg {
      color: $component-active-bg;
    }
  }
  &:not(.checked) svg {
    color: $headings-color;
  }
  .custom-option-content {
    text-align: center;
    padding: $custom-option-padding;
  }
  .custom-option-body {
    display: block;
    margin-bottom: 0.5rem;
    i {
      color: $headings-color;
      &::before {
        font-size: 1.75rem;
      }
      margin-bottom: 0.5rem;
      display: block;
    }
    svg {
      height: 28px;
      width: 28px;
      margin-bottom: 0.5rem;
    }
    .custom-option-title {
      display: block;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      color: $headings-color;
      margin-bottom: 0.5rem;
    }
  }
  .form-check-input {
    float: none !important;
    margin: 0 !important;
  }
}

// Custom option with image
.custom-option-image {
  border-width: $custom-option-image-border-width;
  .custom-option-content {
    padding: 0;
  }
  .custom-option-body {
    img {
      height: 100%;
      width: 100%;
    }
  }
  //radio
  &.custom-option-image-radio {
    .form-check-input {
      display: none;
    }
  }
  //check
  &.custom-option-image-check {
    position: relative;
    .form-check-input {
      position: absolute;
      top: 16px;
      right: 16px;
      margin: 0;
      border: 0;
      opacity: 0;
      border: 1px solid transparent;
      &:checked {
        opacity: 1;
      }
    }
    &:hover {
      .form-check-input {
        border-color: $form-check-input-focus-border;
        border-width: 1px;
        opacity: 1;
        &:checked {
          border-color: $primary;
        }
      }
    }
  }
}

// RTL Style

@include rtl-only {
  .custom-option {
    padding-right: 0;
  }
  .custom-option-basic {
    .custom-option-content {
      padding-right: $custom-option-padding + $form-check-padding-start;
      padding-left: $custom-option-padding;
    }
  }
  .custom-option-image.custom-option-image-check {
    .form-check-input {
      right: auto;
      left: 16px;
    }
  }
}
