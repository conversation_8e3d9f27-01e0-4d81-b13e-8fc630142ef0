<?php

namespace App\Helpers;

class ImageHelper
{
    /**
     * Generate responsive image srcset for various image sources
     *
     * @param string $imageUrl
     * @return array
     */
    public static function generateResponsiveSrcset($imageUrl)
    {
        // YouTube thumbnails
        if (preg_match('/\/vi\/([^\/]+)\//', $imageUrl, $matches)) {
            $videoId = $matches[1];

            return [
                'srcset' => implode(', ', [
                    "https://i.ytimg.com/vi/{$videoId}/mqdefault.jpg 320w",
                    "https://i.ytimg.com/vi/{$videoId}/hqdefault.jpg 480w",
                    "https://i.ytimg.com/vi/{$videoId}/sddefault.jpg 640w",
                    "https://i.ytimg.com/vi/{$videoId}/maxresdefault.jpg 1280w"
                ]),
                'sizes' => '(max-width: 576px) 320px, (max-width: 768px) 480px, (max-width: 992px) 640px, 1280px',
                'default' => "https://i.ytimg.com/vi/{$videoId}/hqdefault.jpg"
            ];
        }

        // Local images (Laravel asset)
        if (strpos($imageUrl, asset('')) === 0 || strpos($imageUrl, '/') === 0) {
            return self::generateLocalImageSrcset($imageUrl);
        }

        // External images (other domains)
        if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return self::generateExternalImageSrcset($imageUrl);
        }

        // Fallback for any other cases
        return [
            'srcset' => $imageUrl,
            'sizes' => '(max-width: 576px) 280px, (max-width: 768px) 360px, (max-width: 992px) 480px, 640px',
            'default' => $imageUrl
        ];
    }

    /**
     * Generate srcset for local images
     *
     * @param string $imageUrl
     * @return array
     */
    private static function generateLocalImageSrcset($imageUrl)
    {
        // For local images, we could potentially generate different sizes
        // This would require implementing image resizing on the server

        $pathInfo = pathinfo($imageUrl);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? 'jpg';
        $directory = $pathInfo['dirname'];

        // Check if different sizes exist (common naming convention)
        $sizes = [];
        $sizeVariants = [
            ['suffix' => '-small', 'width' => 320],
            ['suffix' => '-medium', 'width' => 480],
            ['suffix' => '-large', 'width' => 640],
            ['suffix' => '', 'width' => 1280] // Original
        ];

        foreach ($sizeVariants as $variant) {
            $sizedUrl = $directory . '/' . $baseName . $variant['suffix'] . '.' . $extension;

            // For now, we'll assume the original exists
            // In production, you'd check if the file exists
            if ($variant['suffix'] === '' || $variant['suffix'] === '-medium') {
                $sizes[] = $sizedUrl . ' ' . $variant['width'] . 'w';
            }
        }

        return [
            'srcset' => !empty($sizes) ? implode(', ', $sizes) : $imageUrl,
            'sizes' => '(max-width: 576px) 280px, (max-width: 768px) 360px, (max-width: 992px) 480px, 640px',
            'default' => $imageUrl
        ];
    }

    /**
     * Generate srcset for external images
     *
     * @param string $imageUrl
     * @return array
     */
    private static function generateExternalImageSrcset($imageUrl)
    {
        // For external images, we can't generate different sizes
        // But we can still provide responsive sizes
        return [
            'srcset' => $imageUrl,
            'sizes' => '(max-width: 576px) 280px, (max-width: 768px) 360px, (max-width: 992px) 480px, 640px',
            'default' => $imageUrl
        ];
    }
    
    /**
     * Generate responsive image attributes
     *
     * @param string $imageUrl
     * @param bool $isLazy
     * @param bool $isCritical
     * @return array
     */
    public static function getResponsiveAttributes($imageUrl, $isLazy = true, $isCritical = false)
    {
        $responsive = self::generateResponsiveSrcset($imageUrl);

        return [
            'src' => $responsive['default'],
            'srcset' => $responsive['srcset'],
            'sizes' => $responsive['sizes'],
            'loading' => $isLazy ? 'lazy' : 'eager',
            'decoding' => $isCritical ? 'sync' : 'async',
            'fetchpriority' => $isCritical ? 'high' : 'auto'
        ];
    }
    
    /**
     * Generate WebP alternative URLs
     *
     * @param string $imageUrl
     * @return array
     */
    public static function generateWebPAlternatives($imageUrl)
    {
        // YouTube images - can't generate WebP directly
        if (strpos($imageUrl, 'ytimg.com') !== false) {
            return [
                'webp' => null,
                'fallback' => $imageUrl
            ];
        }

        // External images - can't generate WebP directly
        if (filter_var($imageUrl, FILTER_VALIDATE_URL) && !self::isLocalUrl($imageUrl)) {
            return [
                'webp' => null,
                'fallback' => $imageUrl
            ];
        }

        // Local images - generate WebP version
        if (self::isLocalUrl($imageUrl)) {
            $pathInfo = pathinfo($imageUrl);
            $webpUrl = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';

            return [
                'webp' => $webpUrl,
                'fallback' => $imageUrl
            ];
        }

        // Fallback
        return [
            'webp' => null,
            'fallback' => $imageUrl
        ];
    }

    /**
     * Check if URL is local
     *
     * @param string $url
     * @return bool
     */
    private static function isLocalUrl($url)
    {
        $appUrl = config('app.url');
        return strpos($url, $appUrl) === 0 || strpos($url, '/') === 0;
    }

    /**
     * Detect image type
     *
     * @param string $imageUrl
     * @return string
     */
    public static function detectImageType($imageUrl)
    {
        if (strpos($imageUrl, 'ytimg.com') !== false) {
            return 'youtube';
        }

        if (self::isLocalUrl($imageUrl)) {
            return 'local';
        }

        if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return 'external';
        }

        return 'unknown';
    }

    /**
     * Get optimized image URL based on device/screen size
     *
     * @param string $imageUrl
     * @param string $size (small, medium, large, xlarge)
     * @return string
     */
    public static function getOptimizedImageUrl($imageUrl, $size = 'medium')
    {
        $type = self::detectImageType($imageUrl);

        switch ($type) {
            case 'youtube':
                return self::getOptimizedYouTubeUrl($imageUrl, $size);
            case 'local':
                return self::getOptimizedLocalUrl($imageUrl, $size);
            case 'external':
            default:
                return $imageUrl; // Can't optimize external images
        }
    }

    /**
     * Get optimized YouTube thumbnail URL
     *
     * @param string $imageUrl
     * @param string $size
     * @return string
     */
    private static function getOptimizedYouTubeUrl($imageUrl, $size)
    {
        if (preg_match('/\/vi\/([^\/]+)\//', $imageUrl, $matches)) {
            $videoId = $matches[1];

            $sizeMap = [
                'small' => 'mqdefault.jpg',
                'medium' => 'hqdefault.jpg',
                'large' => 'sddefault.jpg',
                'xlarge' => 'maxresdefault.jpg'
            ];

            $filename = $sizeMap[$size] ?? $sizeMap['medium'];
            return "https://i.ytimg.com/vi/{$videoId}/{$filename}";
        }

        return $imageUrl;
    }

    /**
     * Get optimized local image URL
     *
     * @param string $imageUrl
     * @param string $size
     * @return string
     */
    private static function getOptimizedLocalUrl($imageUrl, $size)
    {
        if ($size === 'medium') {
            return $imageUrl; // Return original for medium
        }

        $pathInfo = pathinfo($imageUrl);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? 'jpg';
        $directory = $pathInfo['dirname'];

        $sizeMap = [
            'small' => '-small',
            'large' => '-large',
            'xlarge' => '-xlarge'
        ];

        $suffix = $sizeMap[$size] ?? '';
        return $directory . '/' . $baseName . $suffix . '.' . $extension;
    }
    
    /**
     * Get optimized image dimensions
     * 
     * @param string $size
     * @return array
     */
    public static function getOptimizedDimensions($size = 'medium')
    {
        $dimensions = [
            'small' => ['width' => 320, 'height' => 180],
            'medium' => ['width' => 480, 'height' => 270],
            'large' => ['width' => 640, 'height' => 360],
            'xlarge' => ['width' => 1280, 'height' => 720]
        ];
        
        return $dimensions[$size] ?? $dimensions['medium'];
    }
    
    /**
     * Generate picture element HTML
     * 
     * @param string $imageUrl
     * @param string $alt
     * @param array $options
     * @return string
     */
    public static function generatePictureElement($imageUrl, $alt = '', $options = [])
    {
        $responsive = self::generateResponsiveSrcset($imageUrl);
        $webp = self::generateWebPAlternatives($imageUrl);
        
        $loading = $options['loading'] ?? 'lazy';
        $decoding = $options['decoding'] ?? 'async';
        $fetchpriority = $options['fetchpriority'] ?? 'auto';
        $class = $options['class'] ?? '';
        $width = $options['width'] ?? '';
        $height = $options['height'] ?? '';
        
        $html = '<picture>';
        
        // WebP source (if available)
        if ($webp['webp']) {
            $html .= '<source srcset="' . htmlspecialchars($webp['webp']) . '" type="image/webp">';
        }
        
        // Fallback img element
        $html .= '<img src="' . htmlspecialchars($responsive['default']) . '"';
        $html .= ' srcset="' . htmlspecialchars($responsive['srcset']) . '"';
        $html .= ' sizes="' . htmlspecialchars($responsive['sizes']) . '"';
        $html .= ' alt="' . htmlspecialchars($alt) . '"';
        $html .= ' loading="' . htmlspecialchars($loading) . '"';
        $html .= ' decoding="' . htmlspecialchars($decoding) . '"';
        
        if ($fetchpriority !== 'auto') {
            $html .= ' fetchpriority="' . htmlspecialchars($fetchpriority) . '"';
        }
        
        if ($class) {
            $html .= ' class="' . htmlspecialchars($class) . '"';
        }
        
        if ($width) {
            $html .= ' width="' . htmlspecialchars($width) . '"';
        }
        
        if ($height) {
            $html .= ' height="' . htmlspecialchars($height) . '"';
        }
        
        $html .= '>';
        $html .= '</picture>';
        
        return $html;
    }
}
