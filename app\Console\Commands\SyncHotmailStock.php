<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class SyncHotmailStock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hotmail:sync-stock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $data = Http::get('https://api.zeus-x.ru/instock')->json()['Data'];
        foreach ($data as $item) {
            $key = 'hotmail_' . $item['AccountCode'];
            $stock = $item['Instock'];
            Cache::put($key, $stock, now()->addMinutes(20));
        }
    }
}
