<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_countries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 2); // VN, US, UK, etc.
            $table->foreignId('server_id')->constrained('sms_servers')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique(['server_id', 'code']); // Mỗi server có thể có cùng country code
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_countries');
    }
};
