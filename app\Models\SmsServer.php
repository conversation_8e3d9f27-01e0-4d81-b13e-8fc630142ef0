<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SmsServer extends Model
{
    use HasFactory;

    protected $table = 'sms_servers';

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get all sim rentals for this server
     */
    public function simRentals(): HasMany
    {
        return $this->hasMany(SmsSimRental::class, 'server_id');
    }

    /**
     * Get all countries for this server
     */
    public function countries(): HasMany
    {
        return $this->hasMany(SmsCountry::class, 'server_id');
    }

    /**
     * Scope for active servers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered servers
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
