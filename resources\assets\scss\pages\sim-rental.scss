// Sim Rental Page Styles
// *******************************************************************************

// Server tabs
.server-tabs {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    margin-bottom: 2rem;

    // Hide scrollbar but keep functionality
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}

.server-tab {
    background: #fff;
    border: 2px solid #e7eaf3;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #5d596c;
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 0.875rem;
}

.server-tab:hover {
    border-color: #696cff;
    background: #f8f7ff;
    color: #696cff;
}

.server-tab.active {
    background: #696cff;
    border-color: #696cff;
    color: white;
}

// Mobile responsive
@media (max-width: 768px) {
    .server-tabs {
        gap: 0.5rem;
        margin: 0 -1rem 2rem -1rem;
        padding: 0 1rem 0.5rem 1rem;
    }

    .server-tab {
        padding: 0.625rem 0.875rem;
        font-size: 0.8rem;
        min-width: auto;
    }
}

// Filter section
.filter-section {
    background: #f8f7fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

// Select2 customization
.select2-container--default .select2-selection--single {
    height: 42px;
    border: 1px solid #d9dee3;
    border-radius: 8px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 40px;
    padding-left: 12px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px;
    right: 8px;
}

.country-flag {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: 2px;
}

// History Table Styles
#simHistoryTable th {
    background: #f8f7fa;
    font-weight: 600;
}

#simHistoryTable tbody tr:hover {
    background: #f8f7fa;
}
